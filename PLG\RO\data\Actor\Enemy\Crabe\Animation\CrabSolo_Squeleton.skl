<?xml version="1.0" ?>
<root>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<AngleLocal type="number" value="90.************"/>
			<Lenght type="number" value="0.093755088746548"/>
			<Name type="string" value="body_a"/>
			<ParentCut type="boolean" value="true"/>
			<ParentUID type="string" value="MyApp75684"/>
			<PosLocal type="vector" x="-0.069268405437469" y="-0.096537947654724" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Crabe_Squeleton"/>
			<UID type="string" value="MyApp75567"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="2">
			<Alpha type="number" value="1"/>
			<AngleLocal type="number" value="-0.************18"/>
			<Lenght type="number" value="0.099609375"/>
			<Name type="string" value="body_b"/>
			<ParentUID type="string" value="MyApp75567"/>
			<PosLocal type="vector" x="0" y="0" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Crabe_Squeleton"/>
			<UID type="string" value="MyApp75568"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="3">
			<Alpha type="number" value="1"/>
			<AngleLocal type="number" value="-261.00367542564"/>
			<Lenght type="number" value="0.070319280028343"/>
			<Name type="string" value="arm_R"/>
			<ParentCut type="boolean" value="true"/>
			<ParentUID type="string" value="MyApp75567"/>
			<PosLocal type="vector" x="0.0065609216690063" y="-0.067318126559258" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Crabe_Squeleton"/>
			<UID type="string" value="MyApp75569"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="4">
			<Alpha type="number" value="1"/>
			<AngleLocal type="number" value="281.65737042058"/>
			<Lenght type="number" value="0.075442217290401"/>
			<Name type="string" value="forearm_R"/>
			<ParentUID type="string" value="MyApp75569"/>
			<PosLocal type="vector" x="0" y="0" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Crabe_Squeleton"/>
			<UID type="string" value="MyApp75570"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="5">
			<Alpha type="number" value="1"/>
			<AngleLocal type="number" value="1.9992884608331"/>
			<Lenght type="number" value="0.14348824322224"/>
			<Name type="string" value="claw_R_A"/>
			<ParentCut type="boolean" value="true"/>
			<ParentUID type="string" value="MyApp75570"/>
			<PosLocal type="vector" x="0.041007801890373" y="-0.03539514541626" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Crabe_Squeleton"/>
			<UID type="string" value="MyApp75571"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="6">
			<Alpha type="number" value="1"/>
			<AngleLocal type="number" value="-13.************"/>
			<Lenght type="number" value="0.13469837605953"/>
			<Name type="string" value="Claw_R_B"/>
			<ParentCut type="boolean" value="true"/>
			<ParentUID type="string" value="MyApp75571"/>
			<PosLocal type="vector" x="-0.14982886612415" y="0.064237058162689" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Crabe_Squeleton"/>
			<UID type="string" value="MyApp75572"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="7">
			<Alpha type="number" value="1"/>
			<AngleLocal type="number" value="-139.12910284487"/>
			<Lenght type="number" value="0.056039713323116"/>
			<Name type="string" value="claw_L"/>
			<ParentCut type="boolean" value="true"/>
			<ParentUID type="string" value="MyApp75567"/>
			<PosLocal type="vector" x="-0.0046079084277153" y="0.067338466644287" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Crabe_Squeleton"/>
			<UID type="string" value="MyApp75573"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="8">
			<Alpha type="number" value="1"/>
			<AngleLocal type="number" value="-0"/>
			<Lenght type="number" value="0.089896321296692"/>
			<Name type="string" value="root"/>
			<PosLocal type="vector" x="0.52038770914078" y="0.86704576015472" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Crabe_Squeleton"/>
			<UID type="string" value="MyApp75684"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="Crabe_Squeleton"/>
	<PatchPointList>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Crabe_Squeleton"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
</root>
