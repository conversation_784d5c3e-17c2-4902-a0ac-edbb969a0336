includeReference("Actor/Includes/helpers.ilu")
includeReference("Actor/Includes/gameplay_types.ilu")

params =
{
    
NAME = "Actor_Template",
Actor_Template =
    {
        SCALE = vector2dNew(0.6,0.6),
        RANK = 1000,
		STARTPAUSED=1,
        COMPONENTS =
        {
            {
                NAME="AnimLightComponent_Template",
                AnimLightComponent_Template=
                {
                    defaultAnimation = "Idle",
                    animSet=
                    {
						SubAnimSet_Template =
                        {
		                    animations=
		                    {
		                        {
		                            SubAnim_Template=
		                            {
		                                friendlyName="Idle",
		                                name="Actor/Bonus/Tuns/animation/TunsAnim1.anm",
		                                loop=1
		                            }
		                        },
		                    }
						}
					}
                }
            },
            {
                NAME="Ray_MiniLumsAIComponent",
                Ray_MiniLumsAIComponent=
                {
                    rewardType = 2,
                    registerToAIManager = 1,
                    -- faction = Faction.Neutral,
                }
            },
            {
                NAME="Ray_RewardComponent_Template",
                Ray_RewardComponent_Template=
                {
                    rewardClassName="Ray_EventSpawnRewardTun",
                }
            },
        }
    }
}

--includeReference("FX/Common/Prtcl_DiskFlow_Tiny.ilu")
includeReference("FX/Common/Trail_Fist1_Small.ilu")
includeReference("Actor/Bonus/Tuns/Components/Tuns_sound.ilu")
includeReference("Actor/Bonus/Tuns/Components/Tuns_fxController.ilu")
	
