params =
{
    
NAME = "Actor_Template",
Actor_Template =
    {
        COMPONENTS =
        {
            {
                NAME = "AnimLightComponent_Template",
                AnimLightComponent_Template =
                {
					scale = vector2dNew(0.3,0.3),
					red = 255,
					green = 255,
					blue = 100,
					patchHLevel = 8,
					patchVLevel = 2,					
					
					defaultAnimation = "Idle",
                    animSet=
                    {
						SubAnimSet_Template =
                        {
							animations =
							{
								{
		                            SubAnim_Template =
									{
										friendlyName = "Idle",
										name = "Actor/Friendly/Butterfly/animation/offstand.anm",
										loop = 1,
									},
								},
							},
							banks = 
							{
								{
									BankChange_Template = 
									{
										friendlyName = "Idle",
										bankName = "orange",
										bankPath = "Actor/Friendly/Butterfly/animation/white.tga",
									}
								}
							},
						},
					}               
				},
            },
        }
    }
}				
