<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="90.************"/>
			<AngleLocal type="number" value="90.************"/>
			<Lenght type="number" value="0.025459578260779"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Hair_02_R"/>
			<Pos type="vector" x="0.69882172346115" y="0.28005009889603" z="0"/>
			<PosEnd type="vector" x="0.69860953092575" y="0.25459140539169" z="0"/>
			<PosLocal type="vector" x="0.69882172346115" y="0.28005009889603" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="D4852MyApp4841"/>
				<Element index="2" type="string" value="D4852MyApp4842"/>
				<Element index="3" type="string" value="D4852MyApp4843"/>
				<Element index="4" type="string" value="D4852MyApp4844"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A42"/>
			<UID type="string" value="D4852MyApp4840"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A42.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Fx_Sparkle_02"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D4852MyApp4842"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D4852MyApp4840"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-1.3999929428101" y="0.041493482887745" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.99996519088745" y="0.0083344867452979" z="0"/>
			<PosUV type="vector" x="0.65762674808502" y="0.31603792309761" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A42"/>
			<UID type="string" value="D4852MyApp4841"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D4852MyApp4841"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D4852MyApp4840"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="-1.3837924003601" y="-0.040275879204273" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99996519088745" y="-0.0083344867452979" z="0"/>
			<PosUV type="vector" x="0.73938983678818" y="0.31494396924973" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A42"/>
			<UID type="string" value="D4852MyApp4842"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D4852MyApp4844"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D4852MyApp4840"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="1.************" y="0.038934409618378" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.99996519088745" y="0.0083344867452979" z="0"/>
			<PosUV type="vector" x="0.65960437059402" y="0.24626551568508" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A42"/>
			<UID type="string" value="D4852MyApp4843"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D4852MyApp4843"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D4852MyApp4840"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="1.3975846767426" y="-0.040759928524494" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99996519088745" y="-0.0083344867452979" z="0"/>
			<PosUV type="vector" x="0.73928368091583" y="0.24412970244884" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A42"/>
			<UID type="string" value="D4852MyApp4844"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A42"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="-1012.2744140625" y="-547.95129394531" z="0"/>
	<zoomSize type="vector" x="2970.6904296875" y="2970.6904296875" z="2.6500360965729"/>
</root>
