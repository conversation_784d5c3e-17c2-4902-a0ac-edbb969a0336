includeReference("Actor/Includes/helpers.ilu")
includeReference("Actor/Includes/gameplay_types.ilu")
params =
{
    
NAME = "Actor_Template",
Actor_Template =
    {
        PROCEDURAL = 0,
        zForced = 0.000000,
        rankForced = 0.000000,
        scaleForced = vector2dNew(1.2,1.2),
        --SCALE = vector2dNew(2.0,2.0),
        ObjectFamily = 1,
        RANK = -1.000000,
        STARTPAUSED = 0,
        COMPONENTS =
        {
			{
				NAME="Ray_FixedAIComponent_Template",
				Ray_FixedAIComponent_Template=
				{
					canBeVacuumed=1,
                    genericBehavior =
                    {
                        NAME = "AIPlayActionsBehavior_Template",
                        AIPlayActionsBehavior_Template =
                        { 
                            actions = 
                            {
                                {
                                    NAME="AIIdleAction_Template",
                                    AIIdleAction_Template =
                                    {
                                        action = "idle",
                                    }
                                },
                            }
                        },
                    },
				}
			},		
			
           {
                NAME = "AnimatedComponent_Template",
                AnimatedComponent_Template =
                {
					animationPath = "world/3_FoodWorld/Enemy/Knife/Animation/",
                    animSet =
                    {
                        SubAnimSet_Template =
                        {
                            animations =
                            {
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "Idle",
                                        name = "Stand.anm",
                                        playRate = 1.000000,
                                        loop = 1,
                                    },
                                },
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "Idle_wind",
                                        name = "damaged.anm",
                                        playRate = 3.000000,
                                        loop = 1,
                                    },
                                },
							},
                        },
                    },
                    defaultAnimation = "toto",
                    defaultColor = "0xffffffff",
                    flip = 1,
					inputs =
					{
						{InputDesc={name="Vacuum", varType=AnimInputTypes.bool}},              -- Used to play random animations
					},
					tree = 
					{
						AnimTree_Template =
						{
							nodes =
							{
								{NAME="BlendTreeNodeChooseBranch_Template",
								BlendTreeNodeChooseBranch_Template =
								{
									nodeName="idle",
									leafs=
									{
										{NAME="AnimTreeNodePlayAnim_Template",
										AnimTreeNodePlayAnim_Template =
										{
											animationName = "idle",
										}},
										{NAME="AnimTreeNodePlayAnim_Template",
										AnimTreeNodePlayAnim_Template =
										{
											animationName = "Idle_wind",
										}},
									},
									leafsCriterias=
									{
										{BlendLeaf =
										{
											criterias =
											{
												{CriteriaDesc={name="Vacuum",eval="==",value=0}},
											},
										}},
										{BlendLeaf =
										{
										}},
									},
								}},							
							}
						}
					},
                },
            },
            {
                NAME = "PhantomComponent_Template",
                PhantomComponent_Template =
                {
                    collisionGroup = CollisionGroup.Character,
                    -- drawDebug = 1,
                    shape =
                    {
                        NAME = "PhysShapeCircle",
                        PhysShapeCircle = 
                        { 
							radius = 0.3
						}
                    },
                },
            },
        },
        xFLIPPED = 0,
    },
}

includeReference("Actor/Enemy/Shooter/Common/Components/shooter_med_ActorParam.ilu")

