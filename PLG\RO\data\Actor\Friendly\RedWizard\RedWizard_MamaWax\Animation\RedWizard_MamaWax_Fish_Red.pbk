<?xml version="1.0" ?>
<root>
	<List>
		<Fish10>
			<BonesListT>
			</BonesListT>
			<Name type="string" value="Fish10"/>
			<PatchPointList>
			</PatchPointList>
			<UID type="string" value="Fish10"/>
		</Fish10>
		<Fish_Lum_C>
			<BonesListT>
				<Element index="1">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="70.39387057803"/>
					<AngleLocal type="number" value="70.39387057803"/>
					<Lenght type="number" value="0.050199795514345"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="fish_body_a"/>
					<ParentCut type="boolean" value="true"/>
					<Pos type="vector" x="0.20517322421074" y="0.11312375217676" z="0"/>
					<PosEnd type="vector" x="0.22201788425446" y="0.06583446264267" z="0"/>
					<PosLocal type="vector" x="0.20517322421074" y="0.11312375217676" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp651251"/>
						<Element index="2" type="string" value="MyApp651252"/>
						<Element index="3" type="string" value="MyApp651253"/>
						<Element index="4" type="string" value="MyApp651254"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish6"/>
					<UID type="string" value="D651249MyApp649310"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
				<Element index="2">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="74.020413090333"/>
					<AngleLocal type="number" value="3.6265425123029"/>
					<Lenght type="number" value="0.036200158298016"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="fish_body_b"/>
					<ParentUID type="string" value="D651249MyApp649310"/>
					<Pos type="vector" x="0.22201788425446" y="0.06583446264267" z="0"/>
					<PosEnd type="vector" x="0.23198360204697" y="0.031033083796501" z="0"/>
					<PosLocal type="vector" x="0" y="1.2290053419406e-009" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp651255"/>
						<Element index="2" type="string" value="MyApp651256"/>
						<Element index="3" type="string" value="MyApp651257"/>
						<Element index="4" type="string" value="MyApp651258"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish6"/>
					<UID type="string" value="D651249MyApp649311"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
				<Element index="3">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="211.57427382353"/>
					<AngleLocal type="number" value="211.57427382353"/>
					<Lenght type="number" value="0.049713376909494"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="fish_tail"/>
					<ParentCut type="boolean" value="true"/>
					<Pos type="vector" x="0.20363681018353" y="0.11303040385246" z="0"/>
					<PosEnd type="vector" x="0.16128289699554" y="0.13906049728394" z="0"/>
					<PosLocal type="vector" x="0.20363681018353" y="0.11303040385246" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp651290"/>
						<Element index="2" type="string" value="MyApp651291"/>
						<Element index="3" type="string" value="MyApp651292"/>
						<Element index="4" type="string" value="MyApp651293"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish6"/>
					<UID type="string" value="D651285MyApp649309"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
			</BonesListT>
			<Name type="string" value="Fish_Lum_C"/>
			<PatchPointList>
				<Element index="1">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651252"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651249MyApp649310"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.078865401446819" y="0.99688535928726" z="0"/>
							<Pos type="vector" x="0.024684483185411" y="0.021900093182921" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.9655510187149" y="-0.26021432876587" z="0"/>
					<PosUV type="vector" x="0.36991733312607" y="0.20921562612057" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish6"/>
					<UID type="string" value="MyApp651251"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="2">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651251"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651249MyApp649310"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.59041982889175" y="-0.80709630250931" z="0"/>
							<Pos type="vector" x="-0.07245159894228" y="-0.020003104582429" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.56218540668488" y="0.82701128721237" z="-0"/>
					<PosUV type="vector" x="0.4455923140049" y="0.24652405083179" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish6"/>
					<UID type="string" value="MyApp651252"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="3">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651254"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651249MyApp649310"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="1" z="0"/>
							<Pos type="vector" x="0.67339956760406" y="0.037626318633556" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.94202160835266" y="-0.33555236458778" z="0"/>
					<PosUV type="vector" x="0.3621432185173" y="0.1373071372509" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish6"/>
					<UID type="string" value="MyApp651253"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="4">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651253"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651249MyApp649310"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.47480016946793" y="-0.88009363412857" z="0"/>
							<Pos type="vector" x="0.94885289669037" y="-0.04713199287653" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.98838758468628" y="-0.15195453166962" z="0"/>
					<PosUV type="vector" x="0.53111135959625" y="0.16813684999943" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish6"/>
					<UID type="string" value="MyApp651254"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="5">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651256"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651249MyApp649311"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.99749499559402" y="0.070737212896347" z="0"/>
							<Pos type="vector" x="0.18078979849815" y="0.072869509458542" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.************" y="0.93947803974152" z="0"/>
					<PosUV type="vector" x="0.30753153562546" y="0.078964255750179" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish6"/>
					<UID type="string" value="MyApp651255"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="6">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651255"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651249MyApp649311"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.95305132865906" y="-0.30280882120132" z="0"/>
							<Pos type="vector" x="1.6572369337082" y="-0.11493379622698" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.028738111257553" y="0.************" z="-0"/>
					<PosUV type="vector" x="0.69805234670639" y="0.079602040350437" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish6"/>
					<UID type="string" value="MyApp651256"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="7">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651258"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651249MyApp649311"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.29552021622658" y="0.9553365111351" z="0"/>
							<Pos type="vector" x="0.96014904975891" y="0.12698268890381" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.99977731704712" y="0.021102041006088" z="0"/>
					<PosUV type="vector" x="0.21902079880238" y="-0.0050754621624947" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish6"/>
					<UID type="string" value="MyApp651257"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="8">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651257"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651249MyApp649311"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.29552021622658" y="-0.95533645153046" z="0"/>
							<Pos type="vector" x="2.8163592815399" y="-0.13236208260059" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.99977737665176" y="-0.021102041006088" z="0"/>
					<PosUV type="vector" x="0.75466501712799" y="0.0085197612643242" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish6"/>
					<UID type="string" value="MyApp651258"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="9">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651291"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651285MyApp649309"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="1" z="0"/>
							<Pos type="vector" x="-0.15264736115932" y="0.020172156393528" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.52360343933105" y="0.85196214914322" z="0"/>
					<PosUV type="vector" x="0.44132846593857" y="0.25248578190804" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish6"/>
					<UID type="string" value="MyApp651290"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="10">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651290"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651285MyApp649309"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.6797109246254" y="-0.73348015546799" z="0"/>
							<Pos type="vector" x="0.10019376128912" y="-0.020987618714571" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.19503524899483" y="-0.98079627752304" z="-0"/>
					<PosUV type="vector" x="0.37680804729462" y="0.19551560282707" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish6"/>
					<UID type="string" value="MyApp651291"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="11">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651293"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651285MyApp649309"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.67971086502075" y="0.73348021507263" z="0"/>
							<Pos type="vector" x="1.5126188993454" y="0.037653479725122" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.19503515958786" y="0.98079633712769" z="0"/>
					<PosUV type="vector" x="0.31857395172119" y="0.36896669864655" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish6"/>
					<UID type="string" value="MyApp651292"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="12">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651292"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651285MyApp649309"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.51142489910126" y="-0.85932797193527" z="0"/>
							<Pos type="vector" x="0.98207777738571" y="-0.076470740139484" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.01423242688179" y="-0.99989879131317" z="-0"/>
					<PosUV type="vector" x="0.24400326609612" y="0.14688761532307" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish6"/>
					<UID type="string" value="MyApp651293"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
			</PatchPointList>
			<UID type="string" value="Fish6"/>
			<zoomOrigin type="vector" x="278.89703369141" y="270.47424316406" z="0"/>
			<zoomSize type="vector" x="1393.0234375" y="1393.0235595703" z="1.3603744506836"/>
		</Fish_Lum_C>
		<Fish_Lum_D>
			<BonesListT>
				<Element index="1">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="67.38013478926"/>
					<AngleLocal type="number" value="67.38013478926"/>
					<Lenght type="number" value="0.0380859375"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="fish_body_a"/>
					<ParentCut type="boolean" value="true"/>
					<Pos type="vector" x="0.4189453125" y="0.107421875" z="0"/>
					<PosEnd type="vector" x="0.43359375" y="0.072265625" z="0"/>
					<PosLocal type="vector" x="0.4189453125" y="0.107421875" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp651263"/>
						<Element index="2" type="string" value="MyApp651264"/>
						<Element index="3" type="string" value="MyApp651265"/>
						<Element index="4" type="string" value="MyApp651266"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish7"/>
					<UID type="string" value="D651261MyApp649310"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
				<Element index="2">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="84.************"/>
					<AngleLocal type="number" value="17.************"/>
					<Lenght type="number" value="0.057781834155321"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="fish_body_b"/>
					<ParentUID type="string" value="D651261MyApp649310"/>
					<Pos type="vector" x="0.43359375" y="0.072265625" z="0"/>
					<PosEnd type="vector" x="0.43885216116905" y="0.014723557978868" z="0"/>
					<PosLocal type="vector" x="0" y="-1.746229827404e-010" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp651267"/>
						<Element index="2" type="string" value="MyApp651268"/>
						<Element index="3" type="string" value="MyApp651269"/>
						<Element index="4" type="string" value="MyApp651270"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish7"/>
					<UID type="string" value="D651261MyApp649311"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
				<Element index="3">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="90.000000000001"/>
					<AngleLocal type="number" value="90.000000000001"/>
					<Lenght type="number" value="0.05078125"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="fish_tail"/>
					<ParentCut type="boolean" value="true"/>
					<Pos type="vector" x="0.2236328125" y="0.23046875" z="0"/>
					<PosEnd type="vector" x="0.2236328125" y="0.1796875" z="0"/>
					<PosLocal type="vector" x="0.2236328125" y="0.23046875" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp651278"/>
						<Element index="2" type="string" value="MyApp651279"/>
						<Element index="3" type="string" value="MyApp651280"/>
						<Element index="4" type="string" value="MyApp651281"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish7"/>
					<UID type="string" value="D651273MyApp649309"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
			</BonesListT>
			<Name type="string" value="Fish_Lum_D"/>
			<PatchPointList>
				<Element index="1">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651264"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651261MyApp649310"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.64421784877777" y="0.76484215259552" z="0"/>
							<Pos type="vector" x="-0.47230362892151" y="0.021693034097552" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.95378422737122" y="0.30049255490303" z="0"/>
					<PosUV type="vector" x="0.78400492668152" y="0.23136565089226" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish7"/>
					<UID type="string" value="MyApp651263"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="2">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651263"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651261MyApp649310"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.5904198884964" y="-0.80709630250931" z="0"/>
							<Pos type="vector" x="-0.11115103214979" y="-0.030464248731732" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.51792740821838" y="0.85542464256287" z="-0"/>
					<PosUV type="vector" x="0.8908759355545" y="0.24609309434891" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish7"/>
					<UID type="string" value="MyApp651264"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="3">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651266"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651261MyApp649310"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.38941836357117" y="0.92106103897095" z="0"/>
							<Pos type="vector" x="0.18602864444256" y="0.054385602474213" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.99998646974564" y="0.0052088499069214" z="0"/>
					<PosUV type="vector" x="0.74293649196625" y="0.15992853045464" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish7"/>
					<UID type="string" value="MyApp651265"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="4">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651265"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651261MyApp649310"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="-1" z="0"/>
							<Pos type="vector" x="0.71466970443726" y="-0.073722012341022" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.9230769276619" y="0.38461539149284" z="0"/>
					<PosUV type="vector" x="0.99493038654327" y="0.22130277752876" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish7"/>
					<UID type="string" value="MyApp651266"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="5">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651268"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651261MyApp649311"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.20897459983826" y="0.97792106866837" z="0"/>
							<Pos type="vector" x="0.47115948796272" y="0.066781371831894" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.95484554767609" y="-0.29710274934769" z="0"/>
					<PosUV type="vector" x="0.73913407325745" y="0.078153446316719" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish7"/>
					<UID type="string" value="MyApp651267"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="6">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651267"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651261MyApp649311"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.19866932928562" y="-0.9800665974617" z="0"/>
							<Pos type="vector" x="0.49888452887535" y="-0.072106331586838" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.99407964944839" y="-0.10865440219641" z="0"/>
					<PosUV type="vector" x="1.0160484313965" y="0.10024156421423" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish7"/>
					<UID type="string" value="MyApp651268"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="7">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651270"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651261MyApp649311"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="1" z="0"/>
							<Pos type="vector" x="1.1880136728287" y="0.051044099032879" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.99585044384003" y="-0.091004572808743" z="0"/>
					<PosUV type="vector" x="0.77801704406738" y="-0.0014807706465945" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish7"/>
					<UID type="string" value="MyApp651269"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="8">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651269"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651261MyApp649311"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.19866932928562" y="-0.9800665974617" z="0"/>
							<Pos type="vector" x="1.3557007312775" y="-0.047484774142504" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.99407964944839" y="-0.10865440219641" z="0"/>
					<PosUV type="vector" x="0.97602063417435" y="-0.002845729701221" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish7"/>
					<UID type="string" value="MyApp651270"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="9">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651279"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651273MyApp649309"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.28039973974228" y="0.95988339185715" z="0"/>
							<Pos type="vector" x="-0.43449872732162" y="0.021285474300385" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.95988339185715" y="0.28039973974228" z="0"/>
					<PosUV type="vector" x="0.40469467639923" y="0.50506627559662" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish7"/>
					<UID type="string" value="MyApp651278"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="10">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651278"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651273MyApp649309"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="-1" z="0"/>
							<Pos type="vector" x="-0.52378141880035" y="-0.024308055639267" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="1" y="0" z="0"/>
					<PosUV type="vector" x="0.49588173627853" y="0.51413404941559" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish7"/>
					<UID type="string" value="MyApp651279"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="11">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651281"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651273MyApp649309"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.97026324272156" y="0.24205233156681" z="0"/>
							<Pos type="vector" x="1.1977952718735" y="0.028969973325729" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.24205233156681" y="-0.97026324272156" z="0"/>
					<PosUV type="vector" x="0.38932567834854" y="0.33928641676903" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish7"/>
					<UID type="string" value="MyApp651280"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="12">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651280"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651273MyApp649309"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.50235116481781" y="-0.86466377973557" z="0"/>
							<Pos type="vector" x="1.0776146650314" y="-0.056800812482834" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.86466377973557" y="0.50235116481781" z="-0"/>
					<PosUV type="vector" x="0.56086724996567" y="0.35149225592613" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish7"/>
					<UID type="string" value="MyApp651281"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
			</PatchPointList>
			<UID type="string" value="Fish7"/>
			<zoomOrigin type="vector" x="-117.29681396484" y="262.2399597168" z="0"/>
			<zoomSize type="vector" x="1624.7541503906" y="1624.7540283203" z="1.5866737365723"/>
		</Fish_Lum_D>
		<Fish_Lum_a>
			<BonesListT>
				<Element index="1">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="97.************"/>
					<AngleLocal type="number" value="97.************"/>
					<Lenght type="number" value="0.068565391004086"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="fish_body_a"/>
					<ParentCut type="boolean" value="true"/>
					<Pos type="vector" x="0.447265625" y="0.3095703125" z="0"/>
					<PosEnd type="vector" x="0.43854025006294" y="0.2415623664856" z="0"/>
					<PosLocal type="vector" x="0.447265625" y="0.3095703125" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp651227"/>
						<Element index="2" type="string" value="MyApp651228"/>
						<Element index="3" type="string" value="MyApp651229"/>
						<Element index="4" type="string" value="MyApp651230"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish4"/>
					<UID type="string" value="D651220MyApp649310"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
				<Element index="2">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="97.************"/>
					<AngleLocal type="number" value="0"/>
					<Lenght type="number" value="0.064453125"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="fish_body_b"/>
					<ParentUID type="string" value="D651220MyApp649310"/>
					<Pos type="vector" x="0.43854025006294" y="0.2415623664856" z="0"/>
					<PosEnd type="vector" x="0.43033817410469" y="0.17763325572014" z="0"/>
					<PosLocal type="vector" x="0" y="0" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp651231"/>
						<Element index="2" type="string" value="MyApp651232"/>
						<Element index="3" type="string" value="MyApp651233"/>
						<Element index="4" type="string" value="MyApp651234"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish4"/>
					<UID type="string" value="D651220MyApp649311"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
			</BonesListT>
			<Name type="string" value="Fish_Lum_a"/>
			<PatchPointList>
				<Element index="1">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651228"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651220MyApp649310"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="1" z="0"/>
							<Pos type="vector" x="-0.69561183452606" y="0.050065480172634" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.99186986684799" y="0.12725625932217" z="0"/>
					<PosUV type="vector" x="0.80735331773758" y="0.72649717330933" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish4"/>
					<UID type="string" value="MyApp651227"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="2">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651227"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651220MyApp649310"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="-1" z="0"/>
							<Pos type="vector" x="-0.83528554439545" y="-0.051063761115074" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.99186986684799" y="-0.12725625932217" z="0"/>
					<PosUV type="vector" x="1.0104048252106" y="0.71975636482239" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish4"/>
					<UID type="string" value="MyApp651228"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="3">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651230"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651220MyApp649310"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="1" z="0"/>
							<Pos type="vector" x="0.51578289270401" y="0.062671259045601" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.99186986684799" y="0.12725625932217" z="0"/>
					<PosUV type="vector" x="0.76120698451996" y="0.56493657827377" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish4"/>
					<UID type="string" value="MyApp651229"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="4">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651229"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651220MyApp649310"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="-1" z="0"/>
							<Pos type="vector" x="0.56564050912857" y="-0.056631103157997" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.99186986684799" y="-0.12725625932217" z="0"/>
					<PosUV type="vector" x="0.99700176715851" y="0.52779120206833" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish4"/>
					<UID type="string" value="MyApp651230"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="5">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651232"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651220MyApp649311"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.10506865382195" y="0.99446499347687" z="0"/>
							<Pos type="vector" x="-0.041367545723915" y="0.073631376028061" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.99975061416626" y="0.022337660193443" z="0"/>
					<PosUV type="vector" x="0.73169362545013" y="0.50715404748917" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish4"/>
					<UID type="string" value="MyApp651231"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="6">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651231"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651220MyApp649311"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="-1" z="0"/>
							<Pos type="vector" x="0.13553927838802" y="-0.066148914396763" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.99186986684799" y="-0.12725645303726" z="0"/>
					<PosUV type="vector" x="1.0060793161392" y="0.************" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish4"/>
					<UID type="string" value="MyApp651232"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="7">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651234"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651220MyApp649311"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.40872156620026" y="0.91265916824341" z="0"/>
							<Pos type="vector" x="2.068746805191" y="0.037520904093981" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.95725154876709" y="-0.28925684094429" z="0"/>
					<PosUV type="vector" x="0.76871275901794" y="0.22816801071167" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish4"/>
					<UID type="string" value="MyApp651233"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="8">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651233"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651220MyApp649311"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.56464248895645" y="-0.82533556222916" z="0"/>
							<Pos type="vector" x="1.7895538806915" y="-0.031216707080603" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.74677115678787" y="-0.66508120298386" z="0"/>
					<PosUV type="vector" x="0.90965020656586" y="0.24637049436569" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish4"/>
					<UID type="string" value="MyApp651234"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
			</PatchPointList>
			<UID type="string" value="Fish4"/>
			<zoomOrigin type="vector" x="-181.62463378906" y="-81.092163085938" z="0"/>
			<zoomSize type="vector" x="1710.2677001953" y="1710.2673339844" z="1.6701830625534"/>
		</Fish_Lum_a>
		<Fish_Lum_b>
			<BonesListT>
				<Element index="1">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="-81.17025563427"/>
					<AngleLocal type="number" value="-81.17025563427"/>
					<Lenght type="number" value="0.0654296875"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="fish_body_a"/>
					<ParentCut type="boolean" value="true"/>
					<Pos type="vector" x="0.275390625" y="0.2763671875" z="0"/>
					<PosEnd type="vector" x="0.28543400764465" y="0.34102144837379" z="0"/>
					<PosLocal type="vector" x="0.275390625" y="0.2763671875" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp651239"/>
						<Element index="2" type="string" value="MyApp651240"/>
						<Element index="3" type="string" value="MyApp651241"/>
						<Element index="4" type="string" value="MyApp651242"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish5"/>
					<UID type="string" value="D651237MyApp649310"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
				<Element index="2">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="-81.17025563427"/>
					<AngleLocal type="number" value="0"/>
					<Lenght type="number" value="0.064453125"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="fish_body_b"/>
					<ParentUID type="string" value="D651237MyApp649310"/>
					<Pos type="vector" x="0.28543400764465" y="0.34102144837379" z="0"/>
					<PosEnd type="vector" x="0.2953274846077" y="0.40471071004868" z="0"/>
					<PosLocal type="vector" x="0" y="0" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp651243"/>
						<Element index="2" type="string" value="MyApp651244"/>
						<Element index="3" type="string" value="MyApp651245"/>
						<Element index="4" type="string" value="MyApp651246"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish5"/>
					<UID type="string" value="D651237MyApp649311"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
			</BonesListT>
			<Name type="string" value="Fish_Lum_b"/>
			<PatchPointList>
				<Element index="1">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651240"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651237MyApp649310"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.31556549668312" y="0.94890385866165" z="0"/>
							<Pos type="vector" x="-0.17120659351349" y="0.0667375177145" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.9860971570015" y="0.16616998612881" z="0"/>
					<PosUV type="vector" x="0.67923545837402" y="0.51010763645172" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish5"/>
					<UID type="string" value="MyApp651239"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="2">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651239"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651237MyApp649310"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="-1" z="0"/>
							<Pos type="vector" x="-0.28377392888069" y="-0.051130417734385" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.98814880847931" y="0.15349887311459" z="0"/>
					<PosUV type="vector" x="0.44403222203255" y="0.53173691034317" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish5"/>
					<UID type="string" value="MyApp651240"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="3">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651242"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651237MyApp649310"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="1" z="0"/>
							<Pos type="vector" x="0.74392050504684" y="0.04743916913867" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.98814880847931" y="-0.15349887311459" z="0"/>
					<PosUV type="vector" x="0.65947812795639" y="0.63436591625214" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish5"/>
					<UID type="string" value="MyApp651241"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="4">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651241"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651237MyApp649310"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.21925680339336" y="-0.97566717863083" z="0"/>
							<Pos type="vector" x="0.62194716930389" y="-0.053287867456675" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.99776005744934" y="-0.066894546151161" z="-0"/>
					<PosUV type="vector" x="0.45796146988869" y="0.6495167016983" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish5"/>
					<UID type="string" value="MyApp651242"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="5">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651244"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651237MyApp649311"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.19866937398911" y="0.98006665706635" z="0"/>
							<Pos type="vector" x="0.11556731164455" y="0.053310826420784" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.93795609474182" y="-0.34675395488739" z="0"/>
					<PosUV type="vector" x="0.67851281166077" y="0.68039739131927" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish5"/>
					<UID type="string" value="MyApp651243"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="6">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651243"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651237MyApp649311"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.59041959047318" y="-0.80709654092789" z="0"/>
							<Pos type="vector" x="-0.12078698724508" y="-0.056119009852409" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.88816010951996" y="-0.45953398942947" z="-0"/>
					<PosUV type="vector" x="0.45757013559341" y="0.68388563394547" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish5"/>
					<UID type="string" value="MyApp651244"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="7">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651246"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651237MyApp649311"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.20897457003593" y="0.97792112827301" z="0"/>
							<Pos type="vector" x="2.6891684532166" y="0.065330319106579" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.99840891361237" y="0.056388214230537" z="0"/>
					<PosUV type="vector" x="0.75319057703018" y="1.0045289993286" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish5"/>
					<UID type="string" value="MyApp651245"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="8">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651245"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651237MyApp649311"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.45616787672043" y="-0.88989388942719" z="0"/>
							<Pos type="vector" x="2.2305264472961" y="-0.10677895694971" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.80932635068893" y="0.58735936880112" z="0"/>
					<PosUV type="vector" x="0.40397641062737" y="0.99894493818283" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish5"/>
					<UID type="string" value="MyApp651246"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
			</PatchPointList>
			<UID type="string" value="Fish5"/>
			<zoomOrigin type="vector" x="-12.186767578125" y="-178.12231445313" z="0"/>
			<zoomSize type="vector" x="1543.5164794922" y="1543.5163574219" z="1.5073401927948"/>
		</Fish_Lum_b>
		<Fish_body>
			<BonesListT>
				<Element index="1">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="-157.97085129098"/>
					<AngleLocal type="number" value="-157.97085129098"/>
					<Lenght type="number" value="0.082987681031227"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="fish_body_a"/>
					<ParentCut type="boolean" value="true"/>
					<Pos type="vector" x="0.17718762159348" y="0.20722408592701" z="0"/>
					<PosEnd type="vector" x="0.10025861114264" y="0.23835095763206" z="0"/>
					<PosLocal type="vector" x="0.17718762159348" y="0.20722408592701" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp650028"/>
						<Element index="2" type="string" value="MyApp650029"/>
						<Element index="3" type="string" value="MyApp650030"/>
						<Element index="4" type="string" value="MyApp650031"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish1"/>
					<UID type="string" value="D650026MyApp649310"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
				<Element index="2">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="-228.63734481905"/>
					<AngleLocal type="number" value="-70.66649352807"/>
					<Lenght type="number" value="0.097817048430443"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="fish_body_b"/>
					<ParentUID type="string" value="D650026MyApp649310"/>
					<Pos type="vector" x="0.10025861859322" y="0.23835095763206" z="0"/>
					<PosEnd type="vector" x="0.035618878901005" y="0.16493517160416" z="0"/>
					<PosLocal type="vector" x="-7.4505805969238e-009" y="-5.7230371552919e-009" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp650032"/>
						<Element index="2" type="string" value="MyApp650033"/>
						<Element index="3" type="string" value="MyApp650034"/>
						<Element index="4" type="string" value="MyApp650035"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish1"/>
					<UID type="string" value="D650026MyApp649311"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
			</BonesListT>
			<Name type="string" value="Fish_body"/>
			<PatchPointList>
				<Element index="1">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp650029"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650026MyApp649310"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.71735620498657" y="0.6967066526413" z="0"/>
							<Pos type="vector" x="-0.17926579713821" y="0.017557255923748" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.9263037443161" y="0.37677755951881" z="0"/>
					<PosUV type="vector" x="0.39512741565704" y="0.43583911657333" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish1"/>
					<UID type="string" value="MyApp650028"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="2">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp650028"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650026MyApp649310"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="-1" z="0"/>
							<Pos type="vector" x="0.029112616553903" y="-0.0294273737818" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.37507823109627" y="-0.92699313163757" z="0"/>
					<PosUV type="vector" x="0.32782089710236" y="0.3617025911808" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish1"/>
					<UID type="string" value="MyApp650029"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="3">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp650031"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650026MyApp649310"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.19350844621658" y="0.98109865188599" z="0"/>
							<Pos type="vector" x="0.8288956284523" y="0.067135997116566" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.54736971855164" y="0.83689087629318" z="0"/>
					<PosUV type="vector" x="0.27720549702644" y="0.59051924943924" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish1"/>
					<UID type="string" value="MyApp650030"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="4">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp650030"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650026MyApp649310"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.40872144699097" y="-0.9126592874527" z="0"/>
							<Pos type="vector" x="0.35183265805244" y="-0.054258991032839" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.036563336849213" y="-0.99933135509491" z="-0"/>
					<PosUV type="vector" x="0.25954023003578" y="0.33575564622879" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish1"/>
					<UID type="string" value="MyApp650031"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="5">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp650033"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650026MyApp649311"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.099833436310291" y="0.99500417709351" z="0"/>
							<Pos type="vector" x="-0.107048638165" y="0.075011879205704" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.68082010746002" y="0.73245072364807" z="0"/>
					<PosUV type="vector" x="0.10175731033087" y="0.59155917167664" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish1"/>
					<UID type="string" value="MyApp650032"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="6">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp650032"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650026MyApp649311"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.47942546010017" y="-0.87758260965347" z="0"/>
							<Pos type="vector" x="0.55773240327835" y="-0.050897970795631" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.34184718132019" y="-0.93975555896759" z="0"/>
					<PosUV type="vector" x="0.20481599867344" y="0.32754009962082" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish1"/>
					<UID type="string" value="MyApp650033"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="7">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp650035"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650026MyApp649311"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.50235122442245" y="0.86466372013092" z="0"/>
							<Pos type="vector" x="1.2287037372589" y="0.048767033964396" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.98093152046204" y="0.19435393810272" z="0"/>
					<PosUV type="vector" x="-0.03153233975172" y="0.36074215173721" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish1"/>
					<UID type="string" value="MyApp650034"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="8">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp650034"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650026MyApp649311"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.20897468924522" y="-0.97792112827301" z="0"/>
							<Pos type="vector" x="1.0905567407608" y="-0.045744400471449" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.87206602096558" y="-0.4893884062767" z="-0"/>
					<PosUV type="vector" x="0.12819680571556" y="0.25611585378647" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish1"/>
					<UID type="string" value="MyApp650035"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
			</PatchPointList>
			<UID type="string" value="Fish1"/>
			<zoomOrigin type="vector" x="129" y="132" z="0"/>
			<zoomSize type="vector" x="899" y="899" z="1"/>
		</Fish_body>
		<Fish_fish_mouth>
			<BonesListT>
				<Element index="1">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="87.************"/>
					<AngleLocal type="number" value="87.************"/>
					<Lenght type="number" value="0.040907673537731"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="Lum_Fish_mouth"/>
					<ParentCut type="boolean" value="true"/>
					<Pos type="vector" x="0.44710502028465" y="0.45132890343666" z="0"/>
					<PosEnd type="vector" x="0.44896051287651" y="0.41046333312988" z="0"/>
					<PosLocal type="vector" x="0.44710502028465" y="0.45132890343666" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp653005"/>
						<Element index="2" type="string" value="MyApp653006"/>
						<Element index="3" type="string" value="MyApp653007"/>
						<Element index="4" type="string" value="MyApp653008"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish20"/>
					<UID type="string" value="D653003MyApp653003"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
			</BonesListT>
			<Name type="string" value="Fish_fish_mouth"/>
			<PatchPointList>
				<Element index="1">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp653006"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D653003MyApp653003"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.29552021622658" y="0.95533657073975" z="0"/>
							<Pos type="vector" x="-1.2852408885956" y="0.045516364276409" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.96775752305984" y="0.25188386440277" z="0"/>
					<PosUV type="vector" x="0.79850149154663" y="1.0035729408264" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish20"/>
					<UID type="string" value="MyApp653005"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="2">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp653005"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D653003MyApp653003"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.32553640007973" y="-0.94552958011627" z="0"/>
							<Pos type="vector" x="-1.1020668745041" y="-0.041494928300381" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.92979073524475" y="0.************" z="-0"/>
					<PosUV type="vector" x="0.************" y="0.99649524688721" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish20"/>
					<UID type="string" value="MyApp653006"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="3">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp653008"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D653003MyApp653003"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.50235110521317" y="0.86466377973557" z="0"/>
							<Pos type="vector" x="1.133967757225" y="0.040059842169285" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.84098815917969" y="-0.54105353355408" z="0"/>
					<PosUV type="vector" x="0.81838095188141" y="0.80634325742722" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish20"/>
					<UID type="string" value="MyApp653007"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="4">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp653007"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D653003MyApp653003"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.64421772956848" y="-0.76484215259552" z="0"/>
							<Pos type="vector" x="1.2364302873611" y="-0.039296057075262" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.79327541589737" y="-0.60886293649673" z="0"/>
					<PosUV type="vector" x="0.97730964422226" y="0.80516773462296" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish20"/>
					<UID type="string" value="MyApp653008"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
			</PatchPointList>
			<UID type="string" value="Fish20"/>
			<zoomOrigin type="vector" x="-484.86474609375" y="-425.46130371094" z="0"/>
			<zoomSize type="vector" x="2210.2741699219" y="2210.2729492188" z="2.1584703922272"/>
		</Fish_fish_mouth>
		<Fish_nageoires>
			<BonesListT>
				<Element index="1">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="-180.43148682599"/>
					<AngleLocal type="number" value="-180.43148682599"/>
					<Lenght type="number" value="0.017699444666505"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="nageoir_R"/>
					<ParentCut type="boolean" value="true"/>
					<Pos type="vector" x="0.30512037873268" y="0.073593012988567" z="0"/>
					<PosEnd type="vector" x="0.28742143511772" y="0.073459722101688" z="0"/>
					<PosLocal type="vector" x="0.30512037873268" y="0.073593012988567" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp652509"/>
						<Element index="2" type="string" value="MyApp652510"/>
						<Element index="3" type="string" value="MyApp652511"/>
						<Element index="4" type="string" value="MyApp652512"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish19"/>
					<UID type="string" value="D652502MyApp652501"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
				<Element index="2">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="24.88846234945"/>
					<AngleLocal type="number" value="24.88846234945"/>
					<Lenght type="number" value="0.016811346635222"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="nageoir_L"/>
					<ParentCut type="boolean" value="true"/>
					<Pos type="vector" x="0.3330352306366" y="0.064864166080952" z="0"/>
					<PosEnd type="vector" x="0.34828528761864" y="0.05778905749321" z="0"/>
					<PosLocal type="vector" x="0.3330352306366" y="0.064864166080952" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp652504"/>
						<Element index="2" type="string" value="MyApp652505"/>
						<Element index="3" type="string" value="MyApp652506"/>
						<Element index="4" type="string" value="MyApp652507"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish19"/>
					<UID type="string" value="D652502MyApp652502"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
			</BonesListT>
			<Name type="string" value="Fish_nageoires"/>
			<PatchPointList>
				<Element index="1">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp652505"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D652502MyApp652502"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.29552030563354" y="0.95533645153046" z="0"/>
							<Pos type="vector" x="-0.51505970954895" y="0.0064722169190645" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.67013138532639" y="-0.74224263429642" z="0"/>
					<PosUV type="vector" x="0.64491337537766" y="0.12527427077293" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish19"/>
					<UID type="string" value="MyApp652504"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="2">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp652504"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D652502MyApp652502"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.50235116481781" y="-0.86466377973557" z="0"/>
							<Pos type="vector" x="-0.31848949193954" y="-0.0081699816510081" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.091800689697266" y="0.99577742815018" z="-0"/>
					<PosUV type="vector" x="0.66323322057724" y="0.14905747771263" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish19"/>
					<UID type="string" value="MyApp652505"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="3">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp652507"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D652502MyApp652502"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.1259800195694" y="0.99203276634216" z="0"/>
							<Pos type="vector" x="0.97734904289246" y="0.02109638042748" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.30322003364563" y="-0.95292061567307" z="0"/>
					<PosUV type="vector" x="0.67812275886536" y="0.07762436568737" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish19"/>
					<UID type="string" value="MyApp652506"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="4">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp652506"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D652502MyApp652502"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="-1" z="0"/>
							<Pos type="vector" x="1.7812727689743" y="-0.022113768383861" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.42085316777229" y="0.90712881088257" z="0"/>
					<PosUV type="vector" x="0.73901277780533" y="0.14464300870895" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish19"/>
					<UID type="string" value="MyApp652507"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="5">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp652510"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D652502MyApp652501"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.38941833376884" y="0.9210609793663" z="0"/>
							<Pos type="vector" x="-0.37116777896881" y="0.008343649096787" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.38247102499008" y="0.92396754026413" z="0"/>
					<PosUV type="vector" x="0.62325364351273" y="0.16397179663181" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish19"/>
					<UID type="string" value="MyApp652509"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="6">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp652509"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D652502MyApp652501"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.40872138738632" y="-0.91265922784805" z="0"/>
							<Pos type="vector" x="-0.36967033147812" y="-0.0068460591137409" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.41558289527893" y="-0.90955543518066" z="-0"/>
					<PosUV type="vector" x="0.62342941761017" y="0.1335928440094" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish19"/>
					<UID type="string" value="MyApp652510"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="7">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp652512"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D652502MyApp652501"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="1" z="0"/>
							<Pos type="vector" x="1.4572576284409" y="0.021824289113283" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.0075307949446142" y="0.99997168779373" z="0"/>
					<PosUV type="vector" x="0.55832821130753" y="0.19044488668442" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish19"/>
					<UID type="string" value="MyApp652511"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="8">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp652511"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D652502MyApp652501"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.10506886988878" y="-0.99446493387222" z="0"/>
							<Pos type="vector" x="1.3983092308044" y="-0.027963204309344" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.1125550121069" y="-0.9936455488205" z="-0"/>
					<PosUV type="vector" x="0.56116473674774" y="0.090888440608978" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish19"/>
					<UID type="string" value="MyApp652512"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
			</PatchPointList>
			<UID type="string" value="Fish19"/>
			<zoomOrigin type="vector" x="-426.86547851563" y="150.14807128906" z="0"/>
			<zoomSize type="vector" x="3006.8000488281" y="3006.80078125" z="2.9363276958466"/>
		</Fish_nageoires>
		<Lum>
			<BonesListT>
				<Element index="1">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="92.************"/>
					<AngleLocal type="number" value="92.************"/>
					<Lenght type="number" value="0.045209471136332"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="lum_body"/>
					<Pos type="vector" x="0.44643652439117" y="0.45290940999985" z="0"/>
					<PosEnd type="vector" x="0.44458621740341" y="0.40773782134056" z="0"/>
					<PosLocal type="vector" x="0.44643652439117" y="0.45290940999985" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp651341"/>
						<Element index="2" type="string" value="MyApp651342"/>
						<Element index="3" type="string" value="MyApp651343"/>
						<Element index="4" type="string" value="MyApp651344"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="D650976MyApp650963"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
				<Element index="2">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="-166.10682252492"/>
					<AngleLocal type="number" value="-258.4524460742"/>
					<Lenght type="number" value="0.0096801007166505"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="lum_leg_R_a"/>
					<ParentCut type="boolean" value="true"/>
					<ParentUID type="string" value="D650976MyApp650963"/>
					<Pos type="vector" x="0.32535427808762" y="0.14077651500702" z="0"/>
					<PosEnd type="vector" x="0.3159573674202" y="0.14310082793236" z="0"/>
					<PosLocal type="vector" x="0.27161747217178" y="-0.10820600390434" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp651026"/>
						<Element index="2" type="string" value="MyApp651027"/>
						<Element index="3" type="string" value="MyApp651028"/>
						<Element index="4" type="string" value="MyApp651029"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="D650976MyApp650964"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
				<Element index="3">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="-31.059113748982"/>
					<AngleLocal type="number" value="135.04770877594"/>
					<Lenght type="number" value="0.014484172686934"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="Lum_leg_R_b"/>
					<ParentUID type="string" value="D650976MyApp650964"/>
					<Pos type="vector" x="0.3159573674202" y="0.14310082793236" z="0"/>
					<PosEnd type="vector" x="0.32836502790451" y="0.15057353675365" z="0"/>
					<PosLocal type="vector" x="9.3132257461548e-010" y="-7.4610539968489e-011" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp651030"/>
						<Element index="2" type="string" value="MyApp651031"/>
						<Element index="3" type="string" value="MyApp651032"/>
						<Element index="4" type="string" value="MyApp651033"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="D650976MyApp650965"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
				<Element index="4">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="122.3329838655"/>
					<AngleLocal type="number" value="29.************"/>
					<Lenght type="number" value="0.011618829332292"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="lum_arm_R"/>
					<ParentCut type="boolean" value="true"/>
					<ParentUID type="string" value="D650976MyApp650963"/>
					<Pos type="vector" x="0.16094076633453" y="0.060758084058762" z="0"/>
					<PosEnd type="vector" x="0.15472656488419" y="0.050940707325935" z="0"/>
					<PosLocal type="vector" x="0.35829788446426" y="-0.26920679211617" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp650989"/>
						<Element index="2" type="string" value="MyApp650990"/>
						<Element index="3" type="string" value="MyApp650991"/>
						<Element index="4" type="string" value="MyApp650992"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="D650976MyApp650968"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
				<Element index="5">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="-152.55839366192"/>
					<AngleLocal type="number" value="-274.89137752742"/>
					<Lenght type="number" value="0.018699739128351"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="lum_hand_R"/>
					<ParentUID type="string" value="D650976MyApp650968"/>
					<Pos type="vector" x="0.15472656488419" y="0.050940707325935" z="0"/>
					<PosEnd type="vector" x="0.13813090324402" y="0.059558376669884" z="0"/>
					<PosLocal type="vector" x="-9.3132257461548e-010" y="-2.9507507548487e-010" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp650993"/>
						<Element index="2" type="string" value="MyApp650994"/>
						<Element index="3" type="string" value="MyApp650995"/>
						<Element index="4" type="string" value="MyApp650996"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="D650976MyApp650969"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
				<Element index="6">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="39.************"/>
					<AngleLocal type="number" value="-52.************"/>
					<Lenght type="number" value="0.010811121203005"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="Lum_arm_L"/>
					<ParentCut type="boolean" value="true"/>
					<ParentUID type="string" value="D650976MyApp650963"/>
					<Pos type="vector" x="0.092448502779007" y="0.136010825634" z="0"/>
					<PosEnd type="vector" x="0.10079365968704" y="0.12913778424263" z="0"/>
					<PosLocal type="vector" x="0.28591141104698" y="-0.34072157740593" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp650978"/>
						<Element index="2" type="string" value="MyApp650979"/>
						<Element index="3" type="string" value="MyApp650980"/>
						<Element index="4" type="string" value="MyApp650981"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="D650976MyApp650970"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
				<Element index="7">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="-38.************"/>
					<AngleLocal type="number" value="-77.************"/>
					<Lenght type="number" value="0.013935604132712"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="Lum_hand_L"/>
					<ParentUID type="string" value="D650976MyApp650970"/>
					<Pos type="vector" x="0.10079365968704" y="0.12913778424263" z="0"/>
					<PosEnd type="vector" x="0.11172616481781" y="0.13777962327003" z="0"/>
					<PosLocal type="vector" x="0" y="-2.4364510409214e-010" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp650982"/>
						<Element index="2" type="string" value="MyApp650983"/>
						<Element index="3" type="string" value="MyApp650984"/>
						<Element index="4" type="string" value="MyApp650985"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="D650976MyApp650971"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
				<Element index="8">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="110.58913293346"/>
					<AngleLocal type="number" value="18.************"/>
					<Lenght type="number" value="0.050611112266779"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="lum_wing_R"/>
					<ParentCut type="boolean" value="true"/>
					<ParentUID type="string" value="D650976MyApp650963"/>
					<Pos type="vector" x="0.028288066387177" y="0.32484608888626" z="0"/>
					<PosEnd type="vector" x="0.010489955544472" y="0.27746769785881" z="0"/>
					<PosLocal type="vector" x="0.099860295653343" y="-0.41255679726601" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp651378"/>
						<Element index="2" type="string" value="MyApp651379"/>
						<Element index="3" type="string" value="MyApp651380"/>
						<Element index="4" type="string" value="MyApp651381"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="D650976MyApp650972"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
				<Element index="9">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="9.************"/>
					<AngleLocal type="number" value="-82.************"/>
					<Lenght type="number" value="0.06273640692234"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="lum_wing_L"/>
					<ParentCut type="boolean" value="true"/>
					<ParentUID type="string" value="D650976MyApp650963"/>
					<Pos type="vector" x="0.14684444665909" y="0.30781352519989" z="0"/>
					<PosEnd type="vector" x="0.20870813727379" y="0.29738563299179" z="0"/>
					<PosLocal type="vector" x="0.11202637851238" y="-0.29340264201164" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp651360"/>
						<Element index="2" type="string" value="MyApp651361"/>
						<Element index="3" type="string" value="MyApp651362"/>
						<Element index="4" type="string" value="MyApp651363"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="D650976MyApp650973"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
				<Element index="10">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="29.************"/>
					<AngleLocal type="number" value="29.************"/>
					<Lenght type="number" value="0.015816111117601"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="Lum_leg_L_a"/>
					<ParentCut type="boolean" value="true"/>
					<Pos type="vector" x="0.27149605751038" y="0.15529337525368" z="0"/>
					<PosEnd type="vector" x="0.28520676493645" y="0.14740884304047" z="0"/>
					<PosLocal type="vector" x="0.27149605751038" y="0.15529337525368" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp651321"/>
						<Element index="2" type="string" value="MyApp651322"/>
						<Element index="3" type="string" value="MyApp651323"/>
						<Element index="4" type="string" value="MyApp651324"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="D651310MyApp650966"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
				<Element index="11">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="-92.172222404403"/>
					<AngleLocal type="number" value="-122.07387320352"/>
					<Lenght type="number" value="0.017378956079483"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="Lum_leg_L_b"/>
					<ParentUID type="string" value="D651310MyApp650966"/>
					<Pos type="vector" x="0.28520676493645" y="0.14740884304047" z="0"/>
					<PosEnd type="vector" x="0.28454804420471" y="0.16477531194687" z="0"/>
					<PosLocal type="vector" x="0" y="-9.5391250454213e-011" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp651325"/>
						<Element index="2" type="string" value="MyApp651326"/>
						<Element index="3" type="string" value="MyApp651327"/>
						<Element index="4" type="string" value="MyApp651328"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="D651310MyApp650967"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
			</BonesListT>
			<Name type="string" value="Lum"/>
			<PatchPointList>
				<Element index="1">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp650979"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650970"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="1" z="0"/>
							<Pos type="vector" x="-0.53990572690964" y="0.005024051759392" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.63573807477951" y="-0.77190488576889" z="0"/>
					<PosUV type="vector" x="0.16949784755707" y="0.27168706059456" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp650978"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="2">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp650978"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650970"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="-1" z="0"/>
							<Pos type="vector" x="-0.5138623714447" y="-0.0079380301758647" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.63573807477951" y="0.77190488576889" z="0"/>
					<PosUV type="vector" x="0.18641349673271" y="0.29134005308151" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp650979"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="3">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp650981"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650970"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="1" z="0"/>
							<Pos type="vector" x="0.7555667757988" y="0.0094008361920714" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.63573807477951" y="-0.77190488576889" z="0"/>
					<PosUV type="vector" x="0.18555471301079" y="0.24712246656418" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp650980"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="4">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp650980"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650970"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="-1" z="0"/>
							<Pos type="vector" x="0.28655990958214" y="-0.0055291368626058" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.63573807477951" y="0.77190488576889" z="0"/>
					<PosUV type="vector" x="0.19670994579792" y="0.2766185104847" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp650981"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="5">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp650983"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650971"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.19866916537285" y="0.98006665706635" z="0"/>
							<Pos type="vector" x="0.18408669531345" y="0.020072748884559" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.45190912485123" y="-0.89206397533417" z="0"/>
					<PosUV type="vector" x="0.23050767183304" y="0.22996304929256" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp650982"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="6">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp650982"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650971"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.99528837203979" y="-0.096959501504898" z="0"/>
							<Pos type="vector" x="0.55815970897675" y="-0.004453634377569" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.84093254804611" y="-0.54113990068436" z="-0"/>
					<PosUV type="vector" x="0.20826785266399" y="0.27491039037704" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp650983"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="7">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp650985"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650971"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="1" z="0"/>
							<Pos type="vector" x="2.2211575508118" y="0.018212027847767" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.62012660503387" y="-0.78450167179108" z="0"/>
					<PosUV type="vector" x="0.************" y="0.26809060573578" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp650984"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="8">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp650984"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650971"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="-1" z="0"/>
							<Pos type="vector" x="1.5163556337357" y="-0.0166936814785" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.62012660503387" y="0.78450167179108" z="0"/>
					<PosUV type="vector" x="0.21403805911541" y="0.31067621707916" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp650985"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="9">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp650990"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650968"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="1" z="0"/>
							<Pos type="vector" x="-0.28836622834206" y="0.0072028506547213" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.84495407342911" y="0.53483885526657" z="0"/>
					<PosUV type="vector" x="0.31329330801964" y="0.1348828971386" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp650989"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="10">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp650989"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650968"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="-1" z="0"/>
							<Pos type="vector" x="-0.43366232514381" y="-0.0047564920969307" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.84495407342911" y="-0.53483885526657" z="0"/>
					<PosUV type="vector" x="0.33530929684639" y="0.12494310736656" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp650990"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="11">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp650992"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650968"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="1" z="0"/>
							<Pos type="vector" x="0.34165143966675" y="0.0057620857842267" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.84495407342911" y="0.53483885526657" z="0"/>
					<PosUV type="vector" x="0.30789795517921" y="0.12097150087357" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp650991"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="12">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp650991"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650968"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="-1" z="0"/>
							<Pos type="vector" x="0.72379225492477" y="-0.0083804382011294" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.84495407342911" y="-0.53483885526657" z="0"/>
					<PosUV type="vector" x="0.32704812288284" y="0.098340317606926" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp650992"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="13">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp650994"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650969"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.56029087305069" y="0.82829606533051" z="0"/>
							<Pos type="vector" x="0.64600312709808" y="0.0074049215763807" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.87896299362183" y="0.47689005732536" z="0"/>
					<PosUV type="vector" x="0.29483646154404" y="0.12615895271301" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp650993"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="14">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp650993"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650969"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="-1" z="0"/>
							<Pos type="vector" x="0.28687509894371" y="-0.010916105471551" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.46084433794022" y="-0.88748091459274" z="0"/>
					<PosUV type="vector" x="0.28987011313438" y="0.087450131773949" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp650994"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="15">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp650996"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650969"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="1" z="0"/>
							<Pos type="vector" x="1.5761044025421" y="0.018451634794474" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.46084433794022" y="0.88748091459274" z="0"/>
					<PosUV type="vector" x="0.27414679527283" y="0.16179706156254" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp650995"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="16">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp650995"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650969"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="-1" z="0"/>
							<Pos type="vector" x="1.7288519144058" y="-0.020524498075247" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.46084433794022" y="-0.88748091459274" z="0"/>
					<PosUV type="vector" x="0.23315304517746" y="0.095248557627201" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp650996"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="17">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651027"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650964"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.19866928458214" y="0.9800665974617" z="0"/>
							<Pos type="vector" x="-1.0663915872574" y="0.0033431556075811" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.42818346619606" y="0.90369194746017" z="0"/>
					<PosUV type="vector" x="0.67235559225082" y="0.28308647871017" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp651026"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="18">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651026"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650964"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.29552021622658" y="-0.95533657073975" z="0"/>
							<Pos type="vector" x="0.16799111664295" y="-0.0073803239502013" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.51626300811768" y="-0.85643023252487" z="0"/>
					<PosUV type="vector" x="0.64400714635849" y="0.26800513267517" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp651027"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="19">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651029"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650964"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.93203896284103" y="0.36235803365707" z="0"/>
							<Pos type="vector" x="-0.80310040712357" y="0.0057124230079353" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.99177896976471" y="0.12796311080456" z="0"/>
					<PosUV type="vector" x="0.66854512691498" y="0.28891032934189" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp651028"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="20">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651028"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650964"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.6361311674118" y="-0.77158093452454" z="0"/>
							<Pos type="vector" x="2.1375937461853" y="-0.0040288385935128" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.80278742313385" y="-0.59626543521881" z="0"/>
					<PosUV type="vector" x="0.60860025882721" y="0.28366795182228" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp651029"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="21">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651031"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650965"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.74604666233063" y="0.66589367389679" z="0"/>
							<Pos type="vector" x="1.************" y="0.006233093328774" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.98263931274414" y="-0.1855261027813" z="0"/>
					<PosUV type="vector" x="0.67246031761169" y="0.29606837034225" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp651030"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="22">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651030"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650965"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.81188189983368" y="-0.58382177352905" z="0"/>
							<Pos type="vector" x="-0.55409151315689" y="-0.010302468203008" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.99669337272644" y="0.081254541873932" z="-0"/>
					<PosUV type="vector" x="0.6075342297554" y="0.29557144641876" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp651031"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="23">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651033"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650965"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="1" z="0"/>
							<Pos type="vector" x="1.58815741539" y="0.0059283878654242" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.51592212915421" y="-0.85663539171219" z="0"/>
					<PosUV type="vector" x="0.67744255065918" y="0.29978039860725" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp651032"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="24">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651032"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650965"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.40872138738632" y="-0.91265922784805" z="0"/>
							<Pos type="vector" x="0.90677332878113" y="-0.022746285423636" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.82098627090454" y="0.57094776630402" z="-0"/>
					<PosUV type="vector" x="0.63094598054886" y="0.33872431516647" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp651033"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="25">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651322"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651310MyApp650966"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="1" z="0"/>
							<Pos type="vector" x="-0.38114622235298" y="0.011992470361292" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.49851271510124" y="-0.86688238382339" z="0"/>
					<PosUV type="vector" x="0.52058374881744" y="0.29580494761467" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp651321"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="26">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651321"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651310MyApp650966"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="-1" z="0"/>
							<Pos type="vector" x="-0.50655436515808" y="-0.0021161132026464" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.49851271510124" y="0.86688238382339" z="0"/>
					<PosUV type="vector" x="0.53121149539948" y="0.32224348187447" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp651322"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="27">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651324"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651310MyApp650966"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.50235116481781" y="0.86466377973557" z="0"/>
							<Pos type="vector" x="1.4598693847656" y="0.003910924308002" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.0044334828853607" y="-0.99999022483826" z="0"/>
					<PosUV type="vector" x="0.57912451028824" y="0.28078535199165" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp651323"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="28">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651323"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651310MyApp650966"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.59041994810104" y="-0.80709624290466" z="0"/>
							<Pos type="vector" x="-0.036078561097383" y="-0.0040175686590374" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.10947692394257" y="0.99398934841156" z="-0"/>
					<PosUV type="vector" x="0.54600840806961" y="0.31812119483948" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp651324"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="29">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651326"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651310MyApp650967"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.099833525717258" y="0.99500417709351" z="0"/>
							<Pos type="vector" x="0.11032961308956" y="0.011351804248989" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.99807322025299" y="-0.062047798186541" z="0"/>
					<PosUV type="vector" x="0.59295547008514" y="0.29951030015945" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp651325"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="30">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651325"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651310MyApp650967"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="-1" z="0"/>
							<Pos type="vector" x="0.94833678007126" y="-0.012367371469736" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.99928146600723" y="-0.037903353571892" z="0"/>
					<PosUV type="vector" x="0.54444718360901" y="0.32681867480278" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp651326"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="31">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651328"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651310MyApp650967"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="1" z="0"/>
							<Pos type="vector" x="1.2242575883865" y="0.011505281552672" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.99928146600723" y="0.037903353571892" z="0"/>
					<PosUV type="vector" x="0.59179466962814" y="0.33821192383766" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp651327"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="32">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651327"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651310MyApp650967"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="-1" z="0"/>
							<Pos type="vector" x="1.3492187261581" y="-0.013309453614056" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.99928146600723" y="-0.037903353571892" z="0"/>
					<PosUV type="vector" x="0.54203623533249" y="0.34067106246948" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp651328"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="33">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651342"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650963"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.38480818271637" y="0.92299664020538" z="0"/>
							<Pos type="vector" x="-1.0571902990341" y="0.043799102306366" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.90647405385971" y="0.42226162552834" z="0"/>
					<PosUV type="vector" x="0.80926048755646" y="1.0049139261246" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp651341"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="34">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651341"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650963"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.5023500919342" y="-0.86466443538666" z="0"/>
							<Pos type="vector" x="-1.1198906898499" y="-0.039873700588942" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.88449984788895" y="0.46654069423676" z="-0"/>
					<PosUV type="vector" x="0.97669792175293" y="1.0037294626236" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp651342"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="35">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651344"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650963"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.67195254564285" y="0.74059420824051" z="0"/>
							<Pos type="vector" x="1.1117780208588" y="0.033523727208376" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.76747494935989" y="-0.64107888936996" z="0"/>
					<PosUV type="vector" x="0.82176750898361" y="0.80812132358551" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp651343"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="36">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651343"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650963"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.47942557930946" y="-0.87758260965347" z="0"/>
							<Pos type="vector" x="1.0403735637665" y="-0.041557278484106" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.85722559690475" y="-0.5149410367012" z="0"/>
					<PosUV type="vector" x="0.97206795215607" y="0.80842649936676" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp651344"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="37">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651361"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650973"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.64421772956848" y="0.76484221220016" z="0"/>
							<Pos type="vector" x="-0.18320159614086" y="0.011888559907675" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.76238632202148" y="-0.64712232351303" z="0"/>
					<PosUV type="vector" x="0.26706966757774" y="0.59600150585175" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp651360"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="38">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651360"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650973"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="-1" z="0"/>
							<Pos type="vector" x="-0.21621865034103" y="-0.012457445263863" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.16621755063534" y="0.9860891699791" z="0"/>
					<PosUV type="vector" x="0.27107802033424" y="0.64470475912094" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp651361"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="39">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651363"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650973"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.40872138738632" y="0.91265916824341" z="0"/>
							<Pos type="vector" x="1.3067853450775" y="0.018022287636995" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.25133574008942" y="-0.96789997816086" z="0"/>
					<PosUV type="vector" x="0.4493827521801" y="0.55282986164093" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp651362"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="40">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651362"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650973"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="-1" z="0"/>
							<Pos type="vector" x="1.1985914707184" y="-0.0096795447170734" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.16621755063534" y="0.9860891699791" z="0"/>
					<PosUV type="vector" x="0.44520527124405" y="0.60971927642822" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp651363"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="41">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651379"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650972"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.2955202460289" y="0.95533657073975" z="0"/>
							<Pos type="vector" x="-0.25875809788704" y="0.019698405638337" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.79039174318314" y="0.61260181665421" z="0"/>
					<PosUV type="vector" x="0.028906553983688" y="0.68806570768356" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp651378"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="42">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651378"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650972"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.31056699156761" y="-0.95055150985718" z="0"/>
							<Pos type="vector" x="-0.21475924551487" y="-0.016179777681828" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.99905145168304" y="-0.043544918298721" z="-0"/>
					<PosUV type="vector" x="0.09451337903738" y="0.65866237878799" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp651379"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="43">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651381"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650972"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.************" y="0.91265916824341" z="0"/>
							<Pos type="vector" x="1.3782991170883" y="0.0099474573507905" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.99809682369232" y="-0.061665326356888" z="0"/>
					<PosUV type="vector" x="-0.011110259220004" y="0.52608531713486" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp651380"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="44">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651380"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650976MyApp650972"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.47942557930946" y="-0.87758260965347" z="0"/>
							<Pos type="vector" x="1.2708884477615" y="-0.023052059113979" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.6529313325882" y="-0.7574171423912" z="0"/>
					<PosUV type="vector" x="0.054496582597494" y="0.51305371522903" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish8"/>
					<UID type="string" value="MyApp651381"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
			</PatchPointList>
			<UID type="string" value="Fish8"/>
			<zoomOrigin type="vector" x="225.43029785156" y="-104.92831420898" z="0"/>
			<zoomSize type="vector" x="1710.2678222656" y="1710.2673339844" z="1.6701829433441"/>
		</Lum>
		<Lum_Eye_L_a>
			<BonesListT>
				<Element index="1">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="81.116490215854"/>
					<AngleLocal type="number" value="81.116490215854"/>
					<Lenght type="number" value="0.022723915055394"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="lum_eye_L"/>
					<ParentCut type="boolean" value="true"/>
					<Pos type="vector" x="0.2821798324585" y="0.12545734643936" z="0"/>
					<PosEnd type="vector" x="0.************" y="0.10300602018833" z="0"/>
					<PosLocal type="vector" x="0.2821798324585" y="0.12545734643936" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="D651218MyApp651199"/>
						<Element index="2" type="string" value="D651218MyApp651200"/>
						<Element index="3" type="string" value="D651218MyApp651201"/>
						<Element index="4" type="string" value="D651218MyApp651202"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish9"/>
					<UID type="string" value="D651218D651197MyApp650975"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
			</BonesListT>
			<Name type="string" value="Lum_Eye_L_a"/>
			<PatchPointList>
				<Element index="1">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="D651218MyApp651200"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651218D651197MyApp650975"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.19866932928562" y="0.9800665974617" z="0"/>
							<Pos type="vector" x="-0.53733909130096" y="0.024878021329641" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.99898970127106" y="0.04493835568428" z="0"/>
					<PosUV type="vector" x="0.51142925024033" y="0.26735901832581" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish9"/>
					<UID type="string" value="D651218MyApp651199"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="2">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="D651218MyApp651199"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651218D651197MyApp650975"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.20897464454174" y="-0.97792106866837" z="0"/>
							<Pos type="vector" x="-0.51975518465042" y="-0.019245460629463" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.93391901254654" y="0.35748428106308" z="-0"/>
					<PosUV type="vector" x="0.59874105453491" y="0.28019708395004" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish9"/>
					<UID type="string" value="D651218MyApp651200"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="3">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="D651218MyApp651202"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651218D651197MyApp650975"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.67195248603821" y="0.74059420824051" z="0"/>
							<Pos type="vector" x="1.2397974729538" y="0.017744056880474" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.62794327735901" y="-0.77825891971588" z="0"/>
					<PosUV type="vector" x="0.53799855709076" y="0.18976420164108" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish9"/>
					<UID type="string" value="D651218MyApp651201"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="4">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="D651218MyApp651201"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651218D651197MyApp650975"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.38941836357117" y="-0.92106109857559" z="0"/>
							<Pos type="vector" x="1.1926655769348" y="-0.016968937590718" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.97014862298965" y="-0.24251119792461" z="0"/>
					<PosUV type="vector" x="0.60626095533371" y="0.20260173082352" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish9"/>
					<UID type="string" value="D651218MyApp651202"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
			</PatchPointList>
			<UID type="string" value="Fish9"/>
			<zoomOrigin type="vector" x="181" y="93" z="0"/>
			<zoomSize type="vector" x="1024" y="1024" z="1"/>
		</Lum_Eye_L_a>
		<Lum_bouche_a>
			<BonesListT>
				<Element index="1">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="90"/>
					<AngleLocal type="number" value="90"/>
					<Lenght type="number" value="0.03670746088028"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="lum_bouche"/>
					<ParentCut type="boolean" value="true"/>
					<Pos type="vector" x="0.30927264690399" y="0.23581975698471" z="0"/>
					<PosEnd type="vector" x="0.30927264690399" y="0.19911229610443" z="0"/>
					<PosLocal type="vector" x="0.30927264690399" y="0.23581975698471" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp651205"/>
						<Element index="2" type="string" value="MyApp651206"/>
						<Element index="3" type="string" value="MyApp651207"/>
						<Element index="4" type="string" value="MyApp651208"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish14"/>
					<UID type="string" value="D651203MyApp650976"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
			</BonesListT>
			<Name type="string" value="Lum_bouche_a"/>
			<PatchPointList>
				<Element index="1">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651206"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651203MyApp650976"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.19866932928562" y="0.9800665974617" z="0"/>
							<Pos type="vector" x="-0.65453380346298" y="0.050000011920929" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.9800665974617" y="0.19866932928562" z="0"/>
					<PosUV type="vector" x="0.51854526996613" y="0.5196920633316" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish14"/>
					<UID type="string" value="MyApp651205"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="2">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651205"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651203MyApp650976"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="-1" z="0"/>
							<Pos type="vector" x="-0.67219072580338" y="-0.05123096704483" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="1" y="0" z="0"/>
					<PosUV type="vector" x="0.72100722789764" y="0.52098834514618" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish14"/>
					<UID type="string" value="MyApp651206"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="3">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651208"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651203MyApp650976"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.5988826751709" y="0.80083680152893" z="0"/>
							<Pos type="vector" x="1.5380539894104" y="0.028458297252655" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.80083680152893" y="-0.5988826751709" z="0"/>
					<PosUV type="vector" x="0.56162869930267" y="0.35872340202332" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish14"/>
					<UID type="string" value="MyApp651207"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="4">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651207"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651203MyApp650976"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="-1" z="0"/>
							<Pos type="vector" x="1.0685747861862" y="-0.051846444606781" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="1" y="0" z="0"/>
					<PosUV type="vector" x="0.72223818302155" y="0.39319017529488" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish14"/>
					<UID type="string" value="MyApp651208"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
			</PatchPointList>
			<UID type="string" value="Fish14"/>
			<zoomOrigin type="vector" x="141.05883789063" y="103.89111328125" z="0"/>
			<zoomSize type="vector" x="1323.3723144531" y="1323.3725585938" z="1.2923557758331"/>
		</Lum_bouche_a>
		<Lum_bouche_b>
			<BonesListT>
				<Element index="1">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="102.19600756352"/>
					<AngleLocal type="number" value="102.19600756352"/>
					<Lenght type="number" value="0.010708040557802"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="lum_bouche"/>
					<ParentCut type="boolean" value="true"/>
					<Pos type="vector" x="0.33309090137482" y="0.18328316509724" z="0"/>
					<PosEnd type="vector" x="0.33082875609398" y="0.17281679809093" z="0"/>
					<PosLocal type="vector" x="0.33309090137482" y="0.18328316509724" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp651212"/>
						<Element index="2" type="string" value="MyApp651213"/>
						<Element index="3" type="string" value="MyApp651214"/>
						<Element index="4" type="string" value="MyApp651215"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish15"/>
					<UID type="string" value="D651209MyApp650976"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
			</BonesListT>
			<Name type="string" value="Lum_bouche_b"/>
			<PatchPointList>
				<Element index="1">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651213"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651209MyApp650976"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="1" z="0"/>
							<Pos type="vector" x="-0.48320752382278" y="0.010525550693274" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.97743058204651" y="0.21125668287277" z="0"/>
					<PosUV type="vector" x="0.64779198169708" y="0.38112837076187" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish15"/>
					<UID type="string" value="MyApp651212"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="2">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651212"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651209MyApp650976"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="-1" z="0"/>
							<Pos type="vector" x="-0.24990682303905" y="-0.016518360003829" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.97743058204651" y="-0.21125668287277" z="0"/>
					<PosUV type="vector" x="0.69960355758667" y="0.36481833457947" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish15"/>
					<UID type="string" value="MyApp651213"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="3">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651215"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651209MyApp650976"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="1" z="0"/>
							<Pos type="vector" x="1.1605015993118" y="0.013662998564541" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.97743058204651" y="0.21125668287277" z="0"/>
					<PosUV type="vector" x="0.63422209024429" y="0.34804666042328" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish15"/>
					<UID type="string" value="MyApp651214"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="4">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651214"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651209MyApp650976"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="-1" z="0"/>
							<Pos type="vector" x="1.3379833698273" y="-0.0098491432145238" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.97743058204651" y="-0.21125668287277" z="0"/>
					<PosUV type="vector" x="0.67938208580017" y="0.33439728617668" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish15"/>
					<UID type="string" value="MyApp651215"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
			</PatchPointList>
			<UID type="string" value="Fish15"/>
			<zoomOrigin type="vector" x="11.************" y="116.27252197266" z="0"/>
			<zoomSize type="vector" x="2099.7607421875" y="2099.7602539063" z="2.0505473613739"/>
		</Lum_bouche_b>
		<Lum_eye_L_b>
			<BonesListT>
				<Element index="1">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="5.3214140013457"/>
					<AngleLocal type="number" value="5.3214140013457"/>
					<Lenght type="number" value="0.030442848801613"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="lum_eye_L"/>
					<ParentCut type="boolean" value="true"/>
					<Pos type="vector" x="0.46435403823853" y="0.38398584723473" z="0"/>
					<PosEnd type="vector" x="0.49466568231583" y="0.38116249442101" z="0"/>
					<PosLocal type="vector" x="0.46435403823853" y="0.38398584723473" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp651193"/>
						<Element index="2" type="string" value="MyApp651194"/>
						<Element index="3" type="string" value="MyApp651195"/>
						<Element index="4" type="string" value="MyApp651196"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish11"/>
					<UID type="string" value="D651191MyApp650975"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
			</BonesListT>
			<Name type="string" value="Lum_eye_L_b"/>
			<PatchPointList>
				<Element index="1">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651194"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651191MyApp650975"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.4794254899025" y="0.87758255004883" z="0"/>
							<Pos type="vector" x="-0.24736286699772" y="0.017173821106553" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.55874866247177" y="-0.82933712005615" z="0"/>
					<PosUV type="vector" x="0.91052663326263" y="0.73516887426376" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish11"/>
					<UID type="string" value="MyApp651193"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="2">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651193"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651191MyApp650975"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.50235116481781" y="-0.86466377973557" z="0"/>
							<Pos type="vector" x="-0.30923268198967" y="-0.011156887747347" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.41999480128288" y="0.90752667188644" z="-0"/>
					<PosUV type="vector" x="0.91203081607819" y="0.79193544387817" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish11"/>
					<UID type="string" value="MyApp651194"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="3">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651196"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651191MyApp650975"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.4087216258049" y="0.91265910863876" z="0"/>
							<Pos type="vector" x="1.3588318824768" y="0.016554437577724" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.32231760025024" y="-0.94663166999817" z="0"/>
					<PosUV type="vector" x="1.0080143213272" y="0.7273325920105" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish11"/>
					<UID type="string" value="MyApp651195"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="4">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651195"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651191MyApp650975"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.28544780611992" y="-0.95839422941208" z="0"/>
							<Pos type="vector" x="1.************" y="-0.021573146805167" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.37310165166855" y="0.92779046297073" z="0"/>
					<PosUV type="vector" x="1.0055078268051" y="0.8041512966156" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish11"/>
					<UID type="string" value="MyApp651196"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
			</PatchPointList>
			<UID type="string" value="Fish11"/>
			<zoomOrigin type="vector" x="-27.************" y="-4.7649841308594" z="0"/>
			<zoomSize type="vector" x="1466.3406982422" y="1466.3406982422" z="1.4319732189178"/>
		</Lum_eye_L_b>
		<Lum_eye_R_a>
			<BonesListT>
				<Element index="1">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="79.************"/>
					<AngleLocal type="number" value="79.************"/>
					<Lenght type="number" value="0.014379156753421"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="lum_eye_R"/>
					<ParentCut type="boolean" value="true"/>
					<Pos type="vector" x="0.41718146204948" y="0.38986027240753" z="0"/>
					<PosEnd type="vector" x="0.41976299881935" y="0.37571474909782" z="0"/>
					<PosLocal type="vector" x="0.41718146204948" y="0.38986027240753" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp651181"/>
						<Element index="2" type="string" value="MyApp651182"/>
						<Element index="3" type="string" value="MyApp651183"/>
						<Element index="4" type="string" value="MyApp651184"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish12"/>
					<UID type="string" value="D651179MyApp650974"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
			</BonesListT>
			<Name type="string" value="Lum_eye_R_a"/>
			<PatchPointList>
				<Element index="1">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651182"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651179MyApp650974"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="1" z="0"/>
							<Pos type="vector" x="-1.1139476299286" y="0.016381707042456" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.98375189304352" y="-0.17953324317932" z="0"/>
					<PosUV type="vector" x="0.7963804602623" y="0.80535316467285" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish12"/>
					<UID type="string" value="MyApp651181"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="2">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651181"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651179MyApp650974"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="-1" z="0"/>
							<Pos type="vector" x="-0.43413934111595" y="-0.019610602408648" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.98375189304352" y="0.17953324317932" z="0"/>
					<PosUV type="vector" x="0.87070536613464" y="0.7990443110466" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish12"/>
					<UID type="string" value="MyApp651182"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="3">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651184"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651179MyApp650974"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="1" z="0"/>
							<Pos type="vector" x="1.************" y="0.014089813455939" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.98375189304352" y="-0.17953324317932" z="0"/>
					<PosUV type="vector" x="0.81479269266129" y="0.72999507188797" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish12"/>
					<UID type="string" value="MyApp651183"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="4">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651183"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651179MyApp650974"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="-1" z="0"/>
							<Pos type="vector" x="2.0088021755219" y="-0.01397558953613" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.98375189304352" y="0.17953324317932" z="0"/>
					<PosUV type="vector" x="0.87223154306412" y="0.72790759801865" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish12"/>
					<UID type="string" value="MyApp651184"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
			</PatchPointList>
			<UID type="string" value="Fish12"/>
			<zoomOrigin type="vector" x="-238.72875976563" y="-333.60992431641" z="0"/>
			<zoomSize type="vector" x="1895.0334472656" y="1895.0336914063" z="1.8506186008453"/>
		</Lum_eye_R_a>
		<Lum_eye_R_b>
			<BonesListT>
				<Element index="1">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="129.6768824459"/>
					<AngleLocal type="number" value="129.6768824459"/>
					<Lenght type="number" value="0.019837070256472"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="lum_eye_R"/>
					<ParentCut type="boolean" value="true"/>
					<Pos type="vector" x="0.32941237092018" y="0.12176708877087" z="0"/>
					<PosEnd type="vector" x="0.31674724817276" y="0.10649934411049" z="0"/>
					<PosLocal type="vector" x="0.32941237092018" y="0.12176708877087" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp651187"/>
						<Element index="2" type="string" value="MyApp651188"/>
						<Element index="3" type="string" value="MyApp651189"/>
						<Element index="4" type="string" value="MyApp651190"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish13"/>
					<UID type="string" value="D651185MyApp650974"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
			</BonesListT>
			<Name type="string" value="Lum_eye_R_b"/>
			<PatchPointList>
				<Element index="1">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651188"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651185MyApp650974"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.38941833376884" y="0.9210609793663" z="0"/>
							<Pos type="vector" x="-0.22276099026203" y="0.014568286016583" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.46027427911758" y="0.88777685165405" z="0"/>
					<PosUV type="vector" x="0.64204216003418" y="0.26893875002861" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish13"/>
					<UID type="string" value="MyApp651187"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="2">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651187"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651185MyApp650974"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.31056696176529" y="-0.95055150985718" z="0"/>
							<Pos type="vector" x="-0.44149872660637" y="-0.0095102023333311" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.92988264560699" y="-0.36785650253296" z="-0"/>
					<PosUV type="vector" x="0.68464720249176" y="0.24487183988094" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish13"/>
					<UID type="string" value="MyApp651188"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="3">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651190"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651185MyApp650974"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.5904198884964" y="0.80709624290466" z="0"/>
							<Pos type="vector" x="1.2944085597992" y="0.011475903913379" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.99814534187317" y="0.060875564813614" z="0"/>
					<PosUV type="vector" x="0.60837203264236" y="0.21866253018379" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish13"/>
					<UID type="string" value="MyApp651189"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="4">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651189"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651185MyApp650974"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.2955202460289" y="-0.95533645153046" z="0"/>
							<Pos type="vector" x="1.3331269025803" y="-0.01138093136251" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.54660457372665" y="-0.8373908996582" z="0"/>
					<PosUV type="vector" x="0.64257514476776" y="0.18829402327538" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish13"/>
					<UID type="string" value="MyApp651190"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
			</PatchPointList>
			<UID type="string" value="Fish13"/>
			<zoomOrigin type="vector" x="-479.765625" y="-42.031372070313" z="0"/>
			<zoomSize type="vector" x="2856.4599609375" y="2856.4599609375" z="2.************"/>
		</Lum_eye_R_b>
		<Lum_squash>
			<BonesListT>
				<Element index="1">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="25.************"/>
					<AngleLocal type="number" value="25.************"/>
					<Lenght type="number" value="0.011998207308352"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="lum_body"/>
					<Pos type="vector" x="0.36928808689117" y="0.33083909749985" z="0"/>
					<PosEnd type="vector" x="0.38013309240341" y="0.32570657134056" z="0"/>
					<PosLocal type="vector" x="0.36928808689117" y="0.33083909749985" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="D651357MyApp651341"/>
						<Element index="2" type="string" value="D651357MyApp651342"/>
						<Element index="3" type="string" value="D651357MyApp651343"/>
						<Element index="4" type="string" value="D651357MyApp651344"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish16"/>
					<UID type="string" value="D651357D650976MyApp650963"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
			</BonesListT>
			<Name type="string" value="Lum_squash"/>
			<PatchPointList>
				<Element index="1">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="D651357MyApp651342"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651357D650976MyApp650963"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.83875888586044" y="0.54450297355652" z="0"/>
							<Pos type="vector" x="-0.84969133138657" y="0.062690265476704" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.99106657505035" y="-0.13336879014969" z="0"/>
					<PosUV type="vector" x="0.66651177406311" y="0.55707067251205" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish16"/>
					<UID type="string" value="D651357MyApp651341"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="2">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="D651357MyApp651341"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651357D650976MyApp650963"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.59041959047318" y="-0.80709654092789" z="0"/>
							<Pos type="vector" x="-2.7293601036072" y="-0.071590237319469" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.18841648101807" y="0.98208928108215" z="-0"/>
					<PosUV type="vector" x="0.74062526226044" y="0.81911396980286" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish16"/>
					<UID type="string" value="D651357MyApp651342"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="3">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="D651357MyApp651344"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651357D650976MyApp650963"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.74604666233063" y="0.66589373350143" z="0"/>
							<Pos type="vector" x="2.5930926799774" y="0.055933225899935" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.38948845863342" y="-0.92103135585785" z="0"/>
					<PosUV type="vector" x="0.74696677923203" y="0.53394550085068" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish16"/>
					<UID type="string" value="D651357MyApp651343"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="4">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="D651357MyApp651343"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651357D650976MyApp650963"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.71735620498657" y="-0.69670671224594" z="0"/>
							<Pos type="vector" x="0.92266094684601" y="-0.05665772408247" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.94644117355347" y="0.32287645339966" z="0"/>
					<PosUV type="vector" x="0.80706214904785" y="0.7546312212944" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish16"/>
					<UID type="string" value="D651357MyApp651344"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
			</PatchPointList>
			<UID type="string" value="Fish16"/>
			<zoomOrigin type="vector" x="295" y="98" z="0"/>
			<zoomSize type="vector" x="1024" y="1024" z="1"/>
		</Lum_squash>
		<fish_langue_a>
			<BonesListT>
				<Element index="1">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="181.79047126384"/>
					<AngleLocal type="number" value="181.79047126384"/>
					<Lenght type="number" value="0.057313453406096"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="fish_tong"/>
					<ParentCut type="boolean" value="true"/>
					<Pos type="vector" x="0.1923828125" y="0.4873046875" z="0"/>
					<PosEnd type="vector" x="0.13509733974934" y="0.48909541964531" z="0"/>
					<PosLocal type="vector" x="0.1923828125" y="0.4873046875" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp651300"/>
						<Element index="2" type="string" value="MyApp651301"/>
						<Element index="3" type="string" value="MyApp651302"/>
						<Element index="4" type="string" value="MyApp651303"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish17"/>
					<UID type="string" value="D651298MyApp651298"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
			</BonesListT>
			<Name type="string" value="fish_langue_a"/>
			<PatchPointList>
				<Element index="1">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651301"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651298MyApp651298"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.19866932928562" y="0.9800665974617" z="0"/>
							<Pos type="vector" x="-0.21528194844723" y="0.0094568897038698" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.22919405996799" y="0.97338074445724" z="0"/>
					<PosUV type="vector" x="0.41002163290977" y="0.99274289608002" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish17"/>
					<UID type="string" value="MyApp651300"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="2">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651300"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651298MyApp651298"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.20897442102432" y="-0.97792118787766" z="0"/>
							<Pos type="vector" x="-0.21549724042416" y="-0.0036306052934378" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.17831769585609" y="-0.98397302627563" z="-0"/>
					<PosUV type="vector" x="0.40922847390175" y="0.96657991409302" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish17"/>
					<UID type="string" value="MyApp651301"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="3">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651303"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651298MyApp651298"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="1" z="0"/>
							<Pos type="vector" x="1.2985013723373" y="0.0071776076219976" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.031244533136487" y="0.99951177835464" z="0"/>
					<PosUV type="vector" x="0.23644362390041" y="0.99360811710358" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish17"/>
					<UID type="string" value="MyApp651302"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="4">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651302"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651298MyApp651298"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.19866932928562" y="-0.9800665974617" z="0"/>
							<Pos type="vector" x="1.2498944997787" y="-0.0065459203906357" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.22919405996799" y="-0.97338074445724" z="0"/>
					<PosUV type="vector" x="0.24115499854088" y="0.96600037813187" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish17"/>
					<UID type="string" value="MyApp651303"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
			</PatchPointList>
			<UID type="string" value="Fish17"/>
			<zoomOrigin type="vector" x="92.************" y="-350.30212402344" z="0"/>
			<zoomSize type="vector" x="1895.033203125" y="1895.0329589844" z="1.8506186008453"/>
		</fish_langue_a>
		<fish_langue_b>
			<BonesListT>
				<Element index="1">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="76.************"/>
					<AngleLocal type="number" value="76.************"/>
					<Lenght type="number" value="0.04132542014122"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="fish_tong"/>
					<ParentCut type="boolean" value="true"/>
					<Pos type="vector" x="0.35561317205429" y="0.16499091684818" z="0"/>
					<PosEnd type="vector" x="0.36513805389404" y="0.12477814406157" z="0"/>
					<PosLocal type="vector" x="0.35561317205429" y="0.16499091684818" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp651306"/>
						<Element index="2" type="string" value="MyApp651307"/>
						<Element index="3" type="string" value="MyApp651308"/>
						<Element index="4" type="string" value="MyApp651309"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish18"/>
					<UID type="string" value="D651304MyApp651298"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
			</BonesListT>
			<Name type="string" value="fish_langue_b"/>
			<PatchPointList>
				<Element index="1">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651307"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651304MyApp651298"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.19866921007633" y="0.9800665974617" z="0"/>
							<Pos type="vector" x="-0.14808382093906" y="0.018206730484962" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.9994695186615" y="-0.032570227980614" z="0"/>
					<PosUV type="vector" x="0.67297232151031" y="0.33349880576134" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish18"/>
					<UID type="string" value="MyApp651306"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="2">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651306"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651304MyApp651298"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.20897467434406" y="-0.97792112827301" z="0"/>
							<Pos type="vector" x="-0.2673379778862" y="-0.013463190756738" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.90342605113983" y="0.42874419689178" z="-0"/>
					<PosUV type="vector" x="0.73233503103256" y="0.35768875479698" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish18"/>
					<UID type="string" value="MyApp651307"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="3">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651309"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651304MyApp651298"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="1" z="0"/>
							<Pos type="vector" x="1.0761132240295" y="0.0096335122361779" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.97307598590851" y="-0.23048481345177" z="0"/>
					<PosUV type="vector" x="0.71297776699066" y="0.23899409174919" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish18"/>
					<UID type="string" value="MyApp651308"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="4">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp651308"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D651304MyApp651298"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="-1" z="0"/>
							<Pos type="vector" x="1.2079902887344" y="-0.014194169081748" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.97307598590851" y="0.23048481345177" z="0"/>
					<PosUV type="vector" x="0.76186227798462" y="0.23937164247036" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish18"/>
					<UID type="string" value="MyApp651309"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
			</PatchPointList>
			<UID type="string" value="Fish18"/>
			<zoomOrigin type="vector" x="-204.85083007813" y="36.************" z="0"/>
			<zoomSize type="vector" x="2099.7592773438" y="2099.7600097656" z="2.0505468845367"/>
		</fish_langue_b>
		<tail_a>
			<BonesListT>
				<Element index="1">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="99.************"/>
					<AngleLocal type="number" value="99.************"/>
					<Lenght type="number" value="0.022958250716329"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="fish_tail"/>
					<ParentCut type="boolean" value="true"/>
					<Pos type="vector" x="0.068710535764694" y="0.11886018514633" z="0"/>
					<PosEnd type="vector" x="0.064804285764694" y="0.096236690878868" z="0"/>
					<PosLocal type="vector" x="0.068710535764694" y="0.11886018514633" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp650040"/>
						<Element index="2" type="string" value="MyApp650041"/>
						<Element index="3" type="string" value="MyApp650042"/>
						<Element index="4" type="string" value="MyApp650043"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish2"/>
					<UID type="string" value="D650038MyApp649309"/>
					<Zorder type="number" value="3"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
			</BonesListT>
			<Name type="string" value="tail_a"/>
			<PatchPointList>
				<Element index="1">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp650041"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650038MyApp649309"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.71735566854477" y="0.6967071890831" z="0"/>
							<Pos type="vector" x="-0.31699422001839" y="0.01492365822196" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.56449341773987" y="0.82543766498566" z="0"/>
					<PosUV type="vector" x="0.11048547923565" y="0.25714179873466" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish2"/>
					<UID type="string" value="MyApp650040"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="2">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp650040"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650038MyApp649309"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.67195248603821" y="-0.74059426784515" z="0"/>
							<Pos type="vector" x="-0.44496250152588" y="-0.012030041776598" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.84412544965744" y="0.53614568710327" z="-0"/>
					<PosUV type="vector" x="0.16460660099983" y="0.25375986099243" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish2"/>
					<UID type="string" value="MyApp650041"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="3">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp650043"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650038MyApp649309"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.19350802898407" y="0.98109877109528" z="0"/>
							<Pos type="vector" x="2.1348602771759" y="0.054809629917145" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.9338686466217" y="0.35761630535126" z="0"/>
					<PosUV type="vector" x="0.012721583247185" y="0.15977562963963" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish2"/>
					<UID type="string" value="MyApp650042"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="4">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp650042"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650038MyApp649309"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.40872144699097" y="-0.91265916824341" z="0"/>
							<Pos type="vector" x="1.6221672296524" y="-0.060760118067265" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.96889382600784" y="0.24747671186924" z="-0"/>
					<PosUV type="vector" x="0.24449622631073" y="0.14364603161812" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish2"/>
					<UID type="string" value="MyApp650043"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
			</PatchPointList>
			<UID type="string" value="Fish2"/>
			<zoomOrigin type="vector" x="292.77410888672" y="216.08346557617" z="0"/>
			<zoomSize type="vector" x="2210.2736816406" y="2210.2739257813" z="2.1584706306458"/>
		</tail_a>
		<tail_b>
			<BonesListT>
				<Element index="1">
					<Alpha type="number" value="1"/>
					<Angle type="number" value="-110.52429972059"/>
					<AngleLocal type="number" value="-110.52429972059"/>
					<Lenght type="number" value="0.065470665693283"/>
					<Mirror type="boolean" value="false"/>
					<Name type="string" value="fish_tail"/>
					<ParentCut type="boolean" value="true"/>
					<Pos type="vector" x="0.074999779462814" y="0.021121829748154" z="0"/>
					<PosEnd type="vector" x="0.052045464515686" y="0.08243665099144" z="0"/>
					<PosLocal type="vector" x="0.074999779462814" y="0.021121829748154" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp650046"/>
						<Element index="2" type="string" value="MyApp650047"/>
						<Element index="3" type="string" value="MyApp650048"/>
						<Element index="4" type="string" value="MyApp650049"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="Fish3"/>
					<UID type="string" value="D650044MyApp649309"/>
					<Zorder type="number" value="3"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
			</BonesListT>
			<Name type="string" value="tail_b"/>
			<PatchPointList>
				<Element index="1">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp650047"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650044MyApp649309"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.56464248895645" y="0.82533574104309" z="0"/>
							<Pos type="vector" x="-0.30628079175949" y="0.0303526930511" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.9709125161171" y="-0.23943448066711" z="0"/>
					<PosUV type="vector" x="0.2209125161171" y="0.025968143716455" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish3"/>
					<UID type="string" value="MyApp650046"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="2">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp650046"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650044MyApp649309"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.5904198884964" y="-0.80709630250931" z="0"/>
							<Pos type="vector" x="0.067558310925961" y="-0.038007579743862" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.54886078834534" y="-0.83591383695602" z="-0"/>
					<PosUV type="vector" x="0.07570806145668" y="0.023877047002316" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish3"/>
					<UID type="string" value="MyApp650047"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="3">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp650049"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650044MyApp649309"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="-0.38941842317581" y="0.92106103897095" z="0"/>
							<Pos type="vector" x="0.43008109927177" y="0.052946705371141" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.9991272687912" y="-0.041771292686462" z="0"/>
					<PosUV type="vector" x="0.22942680120468" y="0.13211107254028" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish3"/>
					<UID type="string" value="MyApp650048"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
				<Element index="4">
					<Angle type="number" value="0"/>
					<BrotherUID type="string" value="MyApp650048"/>
					<Color>
						<A type="number" value="255"/>
						<B type="number" value="255"/>
						<G type="number" value="255"/>
						<R type="number" value="255"/>
					</Color>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="D650044MyApp649309"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0.33571907877922" y="-0.94196218252182" z="0"/>
							<Pos type="vector" x="1.0204639434814" y="-0.056150145828724" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.9998744726181" y="-0.015847444534302" z="0"/>
					<PosUV type="vector" x="-0.0020204111933708" y="0.12800979614258" z="0"/>
					<SetPicking type="boolean" value="true"/>
					<TemplateUID type="string" value="Fish3"/>
					<UID type="string" value="MyApp650049"/>
					<Zorder type="number" value="0"/>
					<isInversed type="boolean" value="false"/>
				</Element>
			</PatchPointList>
			<UID type="string" value="Fish3"/>
			<zoomOrigin type="vector" x="291.66357421875" y="244.6590423584" z="0"/>
			<zoomSize type="vector" x="1895.033203125" y="1895.0330810547" z="1.8506186008453"/>
		</tail_b>
	</List>
	<MediaWidth type="number" value="0.5"/>
	<Ratio type="number" value="1"/>
</root>
