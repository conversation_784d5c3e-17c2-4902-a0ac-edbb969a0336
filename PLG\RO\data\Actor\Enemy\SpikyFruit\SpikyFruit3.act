params =
{
    
NAME = "Actor_Template",
Actor_Template =
    {
        SCALE = vector2dNew(16.0,16.0),
        COMPONENTS =
        {
            {
                NAME="AnimLightComponent_Template",
                AnimLightComponent_Template =
                {
                    defaultAnimation = "Idle",
                    animSet=
                    {
						SubAnimSet_Template =
                        {
		                    animations =
		                    {
		                        {
		                            SubAnim_Template =
		                            {
		                                friendlyName = "Idle",
		                                name = "Actor/Enemy/SpikyFruit/animation/ShortStem.anm",
		                                loop = 1,
		                            },
		                        },
		                    },
						},
					},
                }
            },
            { 
                NAME="HingePlatformComponent_Template", 
                HingePlatformComponent_Template=
                {
                
                    hingePlatformScaleInitialSpeed = 50.0,
                    hingePlatformScaleStiff = 20.0,
                    hingePlatformScaleDamping = 5.0,
                    hingePlatformBranchStiff = 2.0,
                    hingePlatformBranchDamping = 0.2,
                    hingeWeightMultiplier = 2.0,
                    polylinesPunchForce = 0.1,
                    polylines =
                    {
                        {
                            PolyData =
                            {
                                friction = 1.0,
		                        gameMaterial = "GameMaterial/Danger_Spike.gmt",
                                bones =
                                {
                                    {PolylineBoneData = {Name = "col1"},},
                                    {PolylineBoneData = {Name = "col2"},},
									{PolylineBoneData = {Name = "col3"},},
                                    {PolylineBoneData = {Name = "col4"},},
									{PolylineBoneData = {Name = "col4*"},},
                                },
                            },
	                    },
                    },
--                    platforms =
--                    {
--                        {PlatformData={ poly = 0, scale = "B_Tige02", link = "B_Tige02", maxPitch = 25.0 },},
--                    },
                    hingeBones = 
                    {
                        {HingeBoneData ={name = "B_tige1",minAngle = -20.0,maxAngle = 20.0,weight = 0.0,resistance = 18.5,},},
                        {HingeBoneData ={ name = "B_tige2", minAngle = -20.0, maxAngle = 20.0, weight = 0.0, resistance = 18.5 },},
                        {HingeBoneData ={ name = "B_tige3", minAngle = -20.0, maxAngle = 20.0, weight = 0.0, resistance = 18.5 },},
						{HingeBoneData ={ name = "B_tige4", minAngle = -20.0, maxAngle = 20.0, weight = 0.0, resistance = 18.5 },},
                        {HingeBoneData ={ name = "B_fruit2", minAngle = -20.0, maxAngle = 20.0, weight = 0.0, resistance = 18.5 },},
                        {HingeBoneData ={ name = "B_fruit3", minAngle = -20.0, maxAngle = 20.0, weight = 0.0, resistance = 18.5 },},
                    },
                }
            },
            { 
                NAME="RenderSimpleAnimComponent_Template", 
                RenderSimpleAnimComponent_Template={}
            },
        }
    }
}
