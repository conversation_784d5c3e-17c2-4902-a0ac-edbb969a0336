<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-0.020826556980088"/>
			<AngleLocal type="number" value="-0.020826556980088"/>
			<Lenght type="number" value="0.082618400454521"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_01_L"/>
			<Pos type="vector" x="0.4710373878479" y="0.030069617554545" z="0"/>
			<PosEnd type="vector" x="0.55365580320358" y="0.030099648982286" z="0"/>
			<PosLocal type="vector" x="0.4710373878479" y="0.030069617554545" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp578"/>
				<Element index="2" type="string" value="MyApp579"/>
				<Element index="3" type="string" value="MyApp580"/>
				<Element index="4" type="string" value="MyApp581"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A9"/>
			<UID type="string" value="MyApp576"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="2">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-179.86644338388"/>
			<AngleLocal type="number" value="-179.86644338388"/>
			<Lenght type="number" value="0.071705028414726"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_01_R"/>
			<Pos type="vector" x="0.46540167927742" y="0.030197130516171" z="0"/>
			<PosEnd type="vector" x="0.39369684457779" y="0.030364274978638" z="0"/>
			<PosLocal type="vector" x="0.46540167927742" y="0.030197130516171" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp582"/>
				<Element index="2" type="string" value="MyApp583"/>
				<Element index="3" type="string" value="MyApp584"/>
				<Element index="4" type="string" value="MyApp585"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A9"/>
			<UID type="string" value="MyApp577"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="3">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-137.15171449235"/>
			<AngleLocal type="number" value="-137.13088793537"/>
			<Lenght type="number" value="0.095459163188934"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_02_L"/>
			<ParentCut type="boolean" value="true"/>
			<ParentUID type="string" value="MyApp576"/>
			<Pos type="vector" x="0.55883949995041" y="0.11053419858217" z="0"/>
			<PosEnd type="vector" x="0.48885294795036" y="0.17545211315155" z="0"/>
			<PosLocal type="vector" x="0.0052129551768303" y="0.080432660877705" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp604"/>
				<Element index="2" type="string" value="MyApp605"/>
				<Element index="3" type="string" value="MyApp606"/>
				<Element index="4" type="string" value="MyApp607"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A9"/>
			<UID type="string" value="MyApp602"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="4">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-47.2025908298"/>
			<AngleLocal type="number" value="132.66385255408"/>
			<Lenght type="number" value="0.093255192041397"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_02_R"/>
			<ParentCut type="boolean" value="true"/>
			<ParentUID type="string" value="MyApp577"/>
			<Pos type="vector" x="0.39449778199196" y="0.10819480568171" z="0"/>
			<PosEnd type="vector" x="0.45785611867905" y="0.17662179470062" z="0"/>
			<PosLocal type="vector" x="-0.00061951577663422" y="-0.077832192182541" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp624"/>
				<Element index="2" type="string" value="MyApp625"/>
				<Element index="3" type="string" value="MyApp626"/>
				<Element index="4" type="string" value="MyApp627"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A9"/>
			<UID type="string" value="MyApp603"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A9.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Cloak01"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp579"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp576"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-0.046811811625957" y="0.0086381221190095" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.00036349552101456" y="-0.99999994039536" z="0"/>
			<PosUV type="vector" x="0.46717301011086" y="0.021430090069771" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A9"/>
			<UID type="string" value="MyApp578"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp578"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp576"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="-0.042293705046177" y="-0.069181337952614" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.00036349552101456" y="0.99999994039536" z="0"/>
			<PosUV type="vector" x="0.46751800179482" y="0.099249683320522" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A9"/>
			<UID type="string" value="MyApp579"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp581"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp576"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="1.1203883886337" y="0.012742300517857" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.00036349552101456" y="-0.99999994039536" z="0"/>
			<PosUV type="vector" x="0.56360673904419" y="0.017360964789987" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A9"/>
			<UID type="string" value="MyApp580"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp580"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp576"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="1.1486337184906" y="-0.069727949798107" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.00036349552101456" y="0.99999994039536" z="0"/>
			<PosUV type="vector" x="0.56591033935547" y="0.099832057952881" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A9"/>
			<UID type="string" value="MyApp581"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="5">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp583"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp577"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-0.046661715954542" y="0.068863779306412" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.0023310005199164" y="0.99999725818634" z="0"/>
			<PosUV type="vector" x="0.46890807151794" y="0.099052920937538" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A9"/>
			<UID type="string" value="MyApp582"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="6">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp582"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp577"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="-0.0473288372159" y="-0.0087181972339749" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.0023310005199164" y="-0.99999725818634" z="0"/>
			<PosUV type="vector" x="0.46877506375313" y="0.021471045911312" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A9"/>
			<UID type="string" value="MyApp583"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="7">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp585"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp577"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="1.0813826322556" y="0.068858854472637" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.0023310005199164" y="0.99999725818634" z="0"/>
			<PosUV type="vector" x="0.38802182674408" y="0.09923654794693" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A9"/>
			<UID type="string" value="MyApp584"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="8">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp584"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp577"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="1.0681021213531" y="-0.010423202067614" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.0023310005199164" y="-0.99999725818634" z="0"/>
			<PosUV type="vector" x="0.38878929615021" y="0.019952483475208" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A9"/>
			<UID type="string" value="MyApp585"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="9">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp605"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp602"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-0.035249508917332" y="0.040424343198538" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.68005949258804" y="0.73315691947937" z="0"/>
			<PosUV type="vector" x="0.58879745006561" y="0.13788326084614" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A9"/>
			<UID type="string" value="MyApp604"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="10">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp604"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp602"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.10506886988878" y="-0.99446493387222" z="0"/>
					<Pos type="vector" x="-0.080612793564796" y="-0.014078164473176" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.59926337003708" y="-0.80055195093155" z="0"/>
			<PosUV type="vector" x="0.55490732192993" y="0.094979479908943" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A9"/>
			<UID type="string" value="MyApp605"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="11">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp607"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp602"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.20897462964058" y="0.97792112827301" z="0"/>
					<Pos type="vector" x="1.2963978052139" y="0.020231926813722" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.5118333697319" y="0.85908484458923" z="0"/>
			<PosUV type="vector" x="0.48186799883842" y="0.20952682197094" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A9"/>
			<UID type="string" value="MyApp606"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="12">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp606"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp602"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.10506886988878" y="-0.99446493387222" z="0"/>
					<Pos type="vector" x="0.88521605730057" y="-0.030064577236772" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.59926337003708" y="-0.80055195093155" z="0"/>
			<PosUV type="vector" x="0.47644057869911" y="0.14595852792263" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A9"/>
			<UID type="string" value="MyApp607"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="13">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp625"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp603"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.3845654129982" y="0.92309784889221" z="0"/>
					<Pos type="vector" x="-0.091041177511215" y="0.0053220707923174" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.41605594754219" y="-0.90933901071548" z="0"/>
			<PosUV type="vector" x="0.39263468980789" y="0.098349273204803" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A9"/>
			<UID type="string" value="MyApp624"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="14">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp624"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp603"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.08935435116291" y="-0.99599999189377" z="0"/>
					<Pos type="vector" x="0.18605481088161" y="-0.040391817688942" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.67011743783951" y="0.74225509166718" z="0"/>
			<PosUV type="vector" x="0.37664797902107" y="0.14836850762367" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A9"/>
			<UID type="string" value="MyApp625"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="15">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp627"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp603"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.29552021622658" y="0.95533657073975" z="0"/>
					<Pos type="vector" x="0.86919057369232" y="0.033597592264414" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.50020951032639" y="-0.86590451002121" z="0"/>
			<PosUV type="vector" x="0.47422084212303" y="0.14484442770481" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A9"/>
			<UID type="string" value="MyApp626"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="16">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp626"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp603"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.47016137838364" y="-0.88258045911789" z="0"/>
					<Pos type="vector" x="1.3857804536819" y="-0.0097824931144714" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.32817134261131" y="0.94461822509766" z="0"/>
			<PosUV type="vector" x="0.47512051463127" y="0.20966589450836" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A9"/>
			<UID type="string" value="MyApp627"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A9"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="-1260.7600097656" y="-307.03887939453" z="0"/>
	<zoomSize type="vector" x="4910.58203125" y="4910.5810546875" z="4.6589951515198"/>
</root>
