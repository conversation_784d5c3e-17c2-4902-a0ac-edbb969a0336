<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="0.97039705490898"/>
			<AngleLocal type="number" value="0.97039705490898"/>
			<Lenght type="number" value="0.11965119093657"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_01_R"/>
			<Pos type="vector" x="0.61532962322235" y="0.026299510151148" z="0"/>
			<PosEnd type="vector" x="0.7349636554718" y="0.024273119866848" z="0"/>
			<PosLocal type="vector" x="0.61532962322235" y="0.026299510151148" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="D736D647MyApp578"/>
				<Element index="2" type="string" value="D736D647MyApp579"/>
				<Element index="3" type="string" value="D736D647MyApp580"/>
				<Element index="4" type="string" value="D736D647MyApp581"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A12"/>
			<UID type="string" value="D736D647MyApp576"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="2">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-0.31377901470022"/>
			<AngleLocal type="number" value="-1.2841760696092"/>
			<Lenght type="number" value="0.095459163188934"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_02_R"/>
			<ParentCut type="boolean" value="false"/>
			<ParentUID type="string" value="D736D647MyApp576"/>
			<Pos type="vector" x="0.73502606153488" y="0.024337839335203" z="0"/>
			<PosEnd type="vector" x="0.83048379421234" y="0.024860616773367" z="0"/>
			<PosLocal type="vector" x="6.1303377151489e-005" y="6.5767140768003e-005" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="D736D647MyApp606"/>
				<Element index="2" type="string" value="D736D647MyApp607"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A12"/>
			<UID type="string" value="D736D647MyApp602"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A12.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Cloak02_R"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D736D647MyApp579"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D736D647MyApp576"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-0.07030776143074" y="0.0084694223478436" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.016935814172029" y="-0.99985659122467" z="0"/>
			<PosUV type="vector" x="0.60677498579025" y="0.017973773181438" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A12"/>
			<UID type="string" value="D736D647MyApp578"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D736D647MyApp578"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D736D647MyApp576"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="-0.063218727707863" y="-0.0837097838521" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.016935814172029" y="0.99985659122467" z="0"/>
			<PosUV type="vector" x="0.60918420553207" y="0.1101253926754" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A12"/>
			<UID type="string" value="D736D647MyApp579"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D736D647MyApp581"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D736D647MyApp576"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="0.98184794187546" y="0.015529074706137" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.016935814172029" y="-0.99985659122467" z="0"/>
			<PosUV type="vector" x="0.73252904415131" y="0.0087830554693937" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A12"/>
			<UID type="string" value="D736D647MyApp580"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D736D647MyApp580"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D736D647MyApp576"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="0.9959317445755" y="-0.082142189145088" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.016935814172029" y="0.99985659122467" z="0"/>
			<PosUV type="vector" x="0.73586809635162" y="0.10641176998615" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A12"/>
			<UID type="string" value="D736D647MyApp581"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="5">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D736D647MyApp607"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D736D647MyApp602"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.11553090810776" y="0.99330389499664" z="0"/>
					<Pos type="vector" x="1.1079103946686" y="0.010186457075179" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.12096896767616" y="-0.99265640974045" z="0"/>
			<PosUV type="vector" x="0.84084045886993" y="0.014730726368725" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A12"/>
			<UID type="string" value="D736D647MyApp606"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="6">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D736D647MyApp606"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D736D647MyApp602"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.17799282073975" y="-0.98403185606003" z="0"/>
					<Pos type="vector" x="0.88989800214767" y="-0.074182845652103" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.17260114848614" y="0.98499184846878" z="0"/>
			<PosUV type="vector" x="0.81956744194031" y="0.09898479282856" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A12"/>
			<UID type="string" value="D736D647MyApp607"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A12"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="-409.38342285156" y="387.82577514648" z="0"/>
	<zoomSize type="vector" x="2393.7434082031" y="2393.7443847656" z="2.1584703922272"/>
</root>
