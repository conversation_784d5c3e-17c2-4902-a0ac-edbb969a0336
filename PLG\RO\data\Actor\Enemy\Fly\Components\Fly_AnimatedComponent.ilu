includeReference("Actor/Includes/gameplay_types.ilu")

component = 
{
    NAME="AnimatedComponent_Template",
    AnimatedComponent_Template = 
    {
        animationPath = "Actor/Enemy/Fly/Animation/",
        defaultAnimation = "IDLEFLY",
		animSet=
		{
			SubAnimSet_Template =
			{
		        animations=
		        {
		            {
		                NAME="SubAnim_Template",
		                SubAnim_Template=
		                {
		                    friendlyName="IdleFly",
		                    name="Mch_Stand.anm",
		                    loop=1,
		                }
		            },
		            {
		                NAME="SubAnim_Template",
		                SubAnim_Template=
		                {
		                    friendlyName="MoveFly",
		                    name="Mch_Vole.anm",
		                    loop=1,
		                }
		            },
		            {
		                NAME="SubAnim_Template",
		                SubAnim_Template=
		                {
		                    friendlyName="ReceiveHitFlyImpact",
		                    name="Mch_Paf.anm",
		                    markerStart="MRK_Paf_Impact_Start",
		                    markerStop="MRK_Paf_Impact_Stop",
		                }
		            },
		            {
		                NAME="SubAnim_Template",
		                SubAnim_Template=
		                {
		                    friendlyName="ReceiveHitFlyEject",
		                    name="Mch_Paf.anm",
		                    markerStart="MRK_Paf_Ejection_Start",
		                    markerStop="MRK_Paf_Ejection_Stop",
		                    loop=1,
		                }
		            },
		            {
		                NAME="SubAnim_Template",
		                SubAnim_Template=
		                {
		                    friendlyName="ReceiveHitFlyEjectToStunned",
		                    name="Mch_Paf.anm",
		                    markerStart="MRK_Paf_tr_Etourdi_Start",
		                    markerStop="MRK_Paf_tr_Etourdi_Stop",
		                }
		            },
		            {
		                NAME="SubAnim_Template",
		                SubAnim_Template=
		                {
		                    friendlyName="ReceiveHitFlyStunned",
		                    name="Mch_Paf.anm",
		                    markerStart="MRK_Etourdi_Start",
		                    markerStop="MRK_Etourdi_Stop",
		                    loop=1,
		                }
		            },
		            {
		                NAME="SubAnim_Template",
		                SubAnim_Template=
		                {
		                    friendlyName="Death",
		                    name="Mch_Disappear.anm",
		                    markerStart="MRK_Start",
		                    markerStop="MRK_Stop",
		                }
		            },
		        },
			},
		},
        inputs =
        {
            {InputDesc={name="RandomN", varType=AnimInputTypes.float}},      -- Used to play random animations
            {InputDesc={name="Ejecting", varType=AnimInputTypes.uint}},      -- If the fly is ejected from a hit
            {InputDesc={name="Stunned", varType=AnimInputTypes.uint}},      -- If the fly is stunned
        },
        tree = 
        {
            AnimTree_Template =
            {
                nodes =
                {
                    {
                        NAME="AnimTreeNodePlayAnim_Template",
                        AnimTreeNodePlayAnim_Template =
                        {
                            nodeName = "IDLEFLY",
                            animationName = "IdleFly"
                        }
                    },
                    {
                        NAME="AnimTreeNodePlayAnim_Template",
                        AnimTreeNodePlayAnim_Template =
                        {
                            nodeName = "MOVEFLY",
                            animationName = "MoveFly"
                        }
                    },
                    {
                        NAME="AnimTreeNodePlayAnim_Template",
                        AnimTreeNodePlayAnim_Template =
                        {
                            nodeName = "DEAD",
                            animationName = "Death"
                        }
                    },
                    {
                        NAME="BlendTreeNodeChooseBranch_Template",
                        BlendTreeNodeChooseBranch_Template =
                        {
                            nodeName = "RECEIVEHIT",
                            leafs =
                            {
                                {
                                    NAME="AnimTreeNodePlayAnim_Template",
                                    AnimTreeNodePlayAnim_Template =
                                    {
                                        nodeName = "RECEIVEHITEJECT",
                                        animationName = "ReceiveHitFlyEject",
                                    }
                                },
                                {
                                    NAME="AnimTreeNodePlayAnim_Template",
                                    AnimTreeNodePlayAnim_Template =
                                    {
                                        nodeName = "STUNNED",
                                        animationName = "ReceiveHitFlyStunned",
                                    }
                                },
                                {
                                    NAME="AnimTreeNodePlayAnim_Template",
                                    AnimTreeNodePlayAnim_Template =
                                    {
                                        animationName="ReceiveHitFlyImpact",
                                    }
                                },
                            },
                            leafsCriterias =
                            {
                                {
                                    BlendLeaf =
                                    {
                                        -- Ejected
                                        criterias =
                                        {
                                            {CriteriaDesc={name="Ejecting",eval="==",value=1}},
                                        },
                                    }
                                },
                                {
                                    BlendLeaf =
                                    {
                                        -- Stunned
                                        criterias =
                                        {
                                            {CriteriaDesc={name="Stunned",eval="==",value=1}},
                                        },
                                    }
                                },
                                {
                                    BlendLeaf =
                                    {
                                        -- Normal
                                    }
                                },
                            },
                        }
                    },
                },
                nodeTransitions =
                {
                    {
                        BlendTreeTransition_Template =
                        {
                            from =
                            {
                                { VAL = "RECEIVEHITEJECT" },
                            },
                            to =
                            {
                                { VAL = "STUNNED" },
                            },
                            node =
                            {
                                NAME="AnimTreeNodePlayAnim_Template",
                                AnimTreeNodePlayAnim_Template =
                                {
                                    animationName = "ReceiveHitFlyEjectToStunned"
                                }
                            },
                        }
                    },
                },
            }
        },
    }
}

appendTable(params.Actor_Template.COMPONENTS,{component})
component = {}
