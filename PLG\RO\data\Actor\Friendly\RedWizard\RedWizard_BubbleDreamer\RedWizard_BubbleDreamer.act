params =
{
    
NAME = "Actor_Template",
Actor_Template =
    {
        SCALE = vector2dNew(1.0,1.0),
        COMPONENTS =
        {
            {
                NAME="AnimLightComponent_Template",
                AnimLightComponent_Template =
                {
                    animSet=
                    {
						SubAnimSet_Template =
                        {
							animations=
							{
								{
									SubAnim_Template=
									{
										friendlyName="Idle",
										name="Actor/Friendly/RedWizard/RedWizard_BubbleDreamer/Animation/Stand.anm",
										loop=1
									}
								},
								{
									SubAnim_Template=
									{
										friendlyName="Reacfly",
										name="Actor/Friendly/RedWizard/RedWizard_BubbleDreamer/Animation/1_1_seq02_BBD_Reacfly.anm",
										
									}
								},
								{
									SubAnim_Template=
									{
										friendlyName="Reac",
										name="Actor/Friendly/RedWizard/RedWizard_BubbleDreamer/Animation/1_1_seq02_BBD_Reacfly.anm",
										markerStart="MRK_Reac_start",
										markerStop="MRK_Reac_stop",
										
									}
								},
								{
									SubAnim_Template=
									{
										friendlyName="SpreadBeard",
										name="Actor/Friendly/RedWizard/RedWizard_BubbleDreamer/Animation/1_1_seq02_BBD_Reacfly.anm",
										markerStart="MRK_SpreadBeard_start",
										markerStop="MRK_SpreadBeard_stop",
										
									}
								},
								{
									SubAnim_Template=
									{
										friendlyName="Fly",
										name="Actor/Friendly/RedWizard/RedWizard_BubbleDreamer/Animation/1_1_seq02_BBD_Reacfly.anm",
										markerStart="MRK_Fly_start",
										markerStop="MRK_Fly_stop",
										
										
									}
								},
								{
									SubAnim_Template=
									{
										friendlyName="Stand_Fly",
										name="Actor/Friendly/RedWizard/RedWizard_BubbleDreamer/Animation/1_1_seq02_BBD_Reacfly.anm",
										markerStart="MRK_Stand_Fly_start",
										markerStop="MRK_Stand_Fly_stop",
										loop=1
										
									}
								},
								{
									SubAnim_Template=
									{
										friendlyName="Sick",
										name="Actor/Friendly/RedWizard/RedWizard_BubbleDreamer/Animation/1_1_seq03_BBD_Sick.anm",
										markerStart="MRK_Stand_Fly_start",
										markerStop="MRK_Stand_Fly_stop",
										loop=1
										
									}
								},
							},
						},
					},
					defaultAnimation = "Idle",
				}
            },
			
			{
                NAME="SoftPlatformComponent_Template",
                SoftPlatformComponent_Template =
                {
                    softPlatformParticles = 
                    {
                        {
                            BodyData =
                            {
                                bone = "beard_c",
                                static = 1,
                            },
                        },
                        {
                            BodyData =
                            {
                                bone = "beard_d",
                            },
                        },
                        {
                            BodyData =
                            {
                                bone = "beard_e",
                            },
                        },
                        {
                            BodyData =
                            {
                                bone = "beard_f",
                            },
                        },
                        {
                            BodyData =
                            {
                                bone = "beard_g",
                            },
                        },
                        {
                            BodyData =
                            {
                                bone = "beard_h",
                            },
                        },
                        {
                            BodyData =
                            {
                                bone = "beard_h*",
                            },
                        },
                    },
                    softPlatformConstraints =
                    {
                        {
                            ConstraintData =
                            {
                                bodyA = "beard_c",
                                bodyB = "beard_d",
                                stiff = 250.0,
                                damp = 100.5
                            },
                        },
                        {
                            ConstraintData =
                            {
                                bodyA = "beard_d",
                                bodyB = "beard_e",
                                stiff = 250.0,
                                damp = 100.5
                            },
                        },
                        {
                            ConstraintData =
                            {
                                bodyA = "beard_e",
                                bodyB = "beard_f",
                                stiff = 250.0,
                                damp = 100.5
                            },
                        },
                        {
                            ConstraintData =
                            {
                                bodyA = "beard_f",
                                bodyB = "beard_g",
                                stiff = 250.0,
                                damp = 100.5
                            },
                        },
                        {
                            ConstraintData =
                            {
                                bodyA = "beard_g",
                                bodyB = "beard_h",
                                stiff = 250.0,
                                damp = 100.5
                            },
                        },
                        {
                            ConstraintData =
                            {
                                bodyA = "beard_h",
                                bodyB = "beard_h*",
                                stiff = 250.0,
                                damp = 100.5
                            },
                        },
                    },
                    polylines =
                    {
	                    {
	                        PolyData =
	                        {
	                            gameMaterial = "GameMaterial/Rope_Liane.gmt",
                                friction = 1.0,
                                speedLoss = 1.0,
                                environment = 0,
                                punchForceMultiplier = 1.0,
                                impulseMultiplier = 0.5,
                                weightMultiplier = 2.0,
                                landSpeedMultiplier = 1.0,
                                verticalClimb = 1,
                                Bones =
                                {
                                    {
                                        PolylineBoneData =
                                        {
                                            Name = "beard_c",
                                        }
                                    },
                                    {
                                        PolylineBoneData =
                                        {
                                            Name = "beard_d",
                                        }
                                    },
                                    {
                                        PolylineBoneData =
                                        {
                                            Name = "beard_e",
                                        }
                                    },
                                    {
                                        PolylineBoneData =
                                        {
                                            Name = "beard_f",
                                        }
                                    },
                                    {
                                        PolylineBoneData =
                                        {
                                            Name = "beard_g",
                                        }
                                    },
                                    {
                                        PolylineBoneData =
                                        {
                                            Name = "beard_h",
                                        }
                                    },
                                    {
                                        PolylineBoneData =
                                        {
                                            Name = "beard_h*",
                                        }
                                    },
                                },
                            }
                        },
                    }
                }
            },
			
            {
                NAME="AxisPolylineComponent_Template",
                AxisPolylineComponent_Template =
                {
                    polylines =
                    {
	                    {
	                        PolyData =
	                        {
	                            gameMaterial = "GameMaterial/Rope_Liane.gmt",
                                friction = 1.0,
                                speedLoss = 1.0,
                                environment = 1,
                                punchForceMultiplier = 100.0,
                                impulseMultiplier = 1.0,
                                weightMultiplier = 5.0,
                                landSpeedMultiplier = 1.0,
                                verticalClimb = 0,
                                Bones =
                                {
                                    {
                                        PolylineBoneData =
                                        {
                                            Name = "baton_col",
                                        }
                                    },
                                    {
                                        PolylineBoneData =
                                        {
                                            Name = "baton_col*",
                                        }
                                    },
                                },
                            }
                        },
                    },
                    axisPolylines =
                    {
                        {
                            AxisPoly =
                            {
                                bone = "baton_axe",
                                stiff = 10.0,
                                damp = 2.0,
                                weightToAngle = 0.15,
								maxAngle = 90,
                                polylines =
                                {
                                    {
                                        VAL = 0,
                                    },
                                },
                            }
                        },
                    },
                }
            },
            {
                NAME="SoftPlatformComponent_Template",
                SoftPlatformComponent_Template =
                {
                    softPlatformParticles = 
                    {
						{
                            BodyData =
                            {
                                bone = "grigri_a",
                                static = 1,
                            },
                        },
                        {
                            BodyData =
                            {
                                bone = "grigri_a*",
                            },
                        },
                        {
                            BodyData =
                            {
                                bone = "grigri_b",
                                static = 1,
                            },
                        },
                        {
                            BodyData =
                            {
                                bone = "grigri_b*",
                            },
                        },
                        {
                            BodyData =
                            {
                                bone = "grigri_c",
                                static = 1,
                            },
                        },
                        {
                            BodyData =
                            {
                                bone = "grigri_c*",
                            },
                        },
                        {
                            BodyData =
                            {
                                bone = "grigri_fil_e",
                                static = 1,
                            },
                        },
                        {
                            BodyData =
                            {
                                bone = "grigri_fil_e*",
                            },
                        },
                    },
                    softPlatformConstraints =
                    {
					    {
                            ConstraintData =
                            {bodyA = "grigri_a",bodyB = "grigri_a*",stiff =20.1,damp = 100.1,minAngle=0,maxAngle=20,minLength=0.1,maxLength=20},
						},
						{
							ConstraintData =
							{bodyA = "grigri_b",bodyB = "grigri_b*",stiff =20.1,damp = 100.1,minAngle=0,maxAngle=20,minLength=0.1,maxLength=20},
						},
						{
							ConstraintData =
							{bodyA = "grigri_c",bodyB = "grigri_c*",stiff =20.1,damp = 100.1,minAngle=0,maxAngle=20,minLength=0.1,maxLength=20},
						},
						{
							ConstraintData =
							{bodyA = "grigri_fil_e",bodyB = "grigri_fil_e*",stiff =20.1,damp = 100.1,minAngle=0,maxAngle=20,minLength=0.1,maxLength=20},
                        },
                    },
                }
            },
            {
                NAME="RenderSimpleAnimComponent_Template",
                RenderSimpleAnimComponent_Template =
                {
                }
            },
        }
    }
}
