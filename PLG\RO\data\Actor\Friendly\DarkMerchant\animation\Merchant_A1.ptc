<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="90"/>
			<AngleLocal type="number" value="90"/>
			<Lenght type="number" value="0.007176861166954"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Hat_01"/>
			<Pos type="vector" x="0.13404966890812" y="0.20442464947701" z="0"/>
			<PosEnd type="vector" x="0.13404966890812" y="0.19724778831005" z="0"/>
			<PosLocal type="vector" x="0.13404966890812" y="0.20442464947701" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp104"/>
				<Element index="2" type="string" value="MyApp105"/>
				<Element index="3" type="string" value="MyApp106"/>
				<Element index="4" type="string" value="MyApp107"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A1"/>
			<UID type="string" value="MyApp101"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="2">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="91.************"/>
			<AngleLocal type="number" value="1.3384386433488"/>
			<Lenght type="number" value="0.066794201731682"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Hat_02"/>
			<ParentUID type="string" value="MyApp101"/>
			<Pos type="vector" x="0.13404966890812" y="0.19724778831005" z="0"/>
			<PosEnd type="vector" x="0.1324894875288" y="0.13047181069851" z="0"/>
			<PosLocal type="vector" x="0" y="0" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp110"/>
				<Element index="2" type="string" value="MyApp111"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A1"/>
			<UID type="string" value="MyApp102"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="3">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="90.************"/>
			<AngleLocal type="number" value="-0.96273343369147"/>
			<Lenght type="number" value="0.095173418521881"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Hat_03"/>
			<ParentUID type="string" value="MyApp102"/>
			<Pos type="vector" x="0.1324894875288" y="0.13047181069851" z="0"/>
			<PosEnd type="vector" x="0.13186541199684" y="0.035300441086292" z="0"/>
			<PosLocal type="vector" x="0" y="0" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp114"/>
				<Element index="2" type="string" value="MyApp115"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A1"/>
			<UID type="string" value="MyApp103"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A1.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Hat01"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp105"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp101"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.98544979095459" y="0.16996715962887" z="0"/>
					<Pos type="vector" x="-3.9287691116333" y="0.12853428721428" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.16996715962887" y="0.98544979095459" z="0"/>
			<PosUV type="vector" x="0.00551538169384" y="0.23262088000774" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A1"/>
			<UID type="string" value="MyApp104"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp104"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp101"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.9530513882637" y="-0.30280882120132" z="0"/>
					<Pos type="vector" x="-1.5206662416458" y="-0.13343818485737" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.30280882120132" y="0.9530513882637" z="0"/>
			<PosUV type="vector" x="0.26748785376549" y="0.21533825993538" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A1"/>
			<UID type="string" value="MyApp105"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp107"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp101"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.86872965097427" y="0.49528670310974" z="0"/>
					<Pos type="vector" x="-0.17074689269066" y="0.12929674983025" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.49528670310974" y="-0.86872965097427" z="0"/>
			<PosUV type="vector" x="0.0047529190778732" y="0.2056500762701" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A1"/>
			<UID type="string" value="MyApp106"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp106"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp101"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.88638341426849" y="-0.4629519879818" z="0"/>
					<Pos type="vector" x="3.2553572654724" y="-0.14341552555561" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.4629519879818" y="-0.88638341426849" z="0"/>
			<PosUV type="vector" x="0.27746519446373" y="0.18106140196323" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A1"/>
			<UID type="string" value="MyApp107"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="5">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp111"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp102"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.20897464454174" y="0.97792106866837" z="0"/>
					<Pos type="vector" x="0.53670692443848" y="0.049566749483347" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.98253548145294" y="-0.18607529997826" z="0"/>
			<PosUV type="vector" x="0.083659090101719" y="0.1625664383173" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A1"/>
			<UID type="string" value="MyApp110"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="6">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp110"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp102"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="0.60086953639984" y="-0.032203640788794" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99972712993622" y="-0.023358035832644" z="0"/>
			<PosUV type="vector" x="0.16530705988407" y="0.15637192130089" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A1"/>
			<UID type="string" value="MyApp111"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="7">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp115"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp103"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.21925495564938" y="0.97566759586334" z="0"/>
					<Pos type="vector" x="0.87673628330231" y="0.029080016538501" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.97708433866501" y="-0.21285253763199" z="0"/>
			<PosUV type="vector" x="0.10286293923855" y="0.047222305089235" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A1"/>
			<UID type="string" value="MyApp114"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="8">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp114"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp103"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.0052631683647633" y="-0.99998623132706" z="0"/>
					<Pos type="vector" x="1.4183880090714" y="-0.038724724203348" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99999922513962" y="-0.0012941006571054" z="0"/>
			<PosUV type="vector" x="0.17032818496227" y="-0.0047720489092171" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A1"/>
			<UID type="string" value="MyApp115"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A1"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="368.5" y="-1" z="0"/>
	<zoomSize type="vector" x="1075" y="1075" z="1"/>
</root>
