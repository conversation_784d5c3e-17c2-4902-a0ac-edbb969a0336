<?xml version="1.0" ?>
<root>
	<AnimsList>
		<Element index="1" type="string" value="Actor/Friendly/Fee/Animation/Boule_Lache.anm"/>
		<Element index="2" type="string" value="Actor/Friendly/Fee/Animation/Boule_Lance.anm"/>
		<Element index="3" type="string" value="Actor/Friendly/Fee/Animation/Boule_Malaxe.anm"/>
		<Element index="4" type="string" value="Actor/Friendly/Fee/Animation/Cine_Free.anm"/>
		<Element index="5" type="string" value="Actor/Friendly/Fee/Animation/Cine_PW_FireBalls_Stand.anm"/>
		<Element index="6" type="string" value="Actor/Friendly/Fee/Animation/Cine_PW_FireBalls_Start.anm"/>
		<Element index="7" type="string" value="Actor/Friendly/Fee/Animation/Cine_PW_Free.anm"/>
		<Element index="8" type="string" value="Actor/Friendly/Fee/Animation/Cine_PW_Power_Start.anm"/>
		<Element index="9" type="string" value="Actor/Friendly/Fee/Animation/Cine_Poopidoo.anm"/>
		<Element index="10" type="string" value="Actor/Friendly/Fee/Animation/Cine_Pouvoir.anm"/>
		<Element index="11" type="string" value="Actor/Friendly/Fee/Animation/Stand.anm"/>
		<Element index="12" type="string" value="Actor/Friendly/Fee/Animation/Stand_EyesOpen.anm"/>
	</AnimsList>
	<PatchBankList>
		<Fee_A type="string" value="Actor/Friendly/Fee/Animation/Fee_A.png"/>
		<Fee_B type="string" value="Actor/Friendly/Fee/Animation/Fee_B.png"/>
		<bubble type="string" value="Actor/Friendly/Fee/Animation/bubble.png"/>
	</PatchBankList>
	<Scale type="number" value="1"/>
	<SceneVersion type="number" value="20"/>
	<Squeleton type="string" value="Actor/Friendly/Fee/Animation/Fee_Template1.skl"/>
	<UseDataFolder type="boolean" value="true"/>
	<UseRelative type="boolean" value="true"/>
</root>
