component = 
{
    NAME = "FXControllerComponent_Template",
    FXControllerComponent_Template=
    {
        fxControlList =
        {
            {
                FXControl =
                {
                    name="MRK_FlyPaf",
                    sound="MRK_FlyPaf",
                }
            },
            {
                FXControl =
                {
                    name="MRK_FlyMove",
                    sound="MRK_FlyMove",
                    fxStopOnEndAnim=1,
                    fxPlayOnce=1,
                }
            },
            {
                FXControl =
                {
                    name="MRK_Death",
                    particle="flash",
                    particleDuration=0.25,
                    fxEmitFromBase=0,
                }
            },
			{
                FXControl =
                {
                    name="Punch_Weak",
                    sound="Punch_Weak",
                    particle="flash",
                    particleDuration=0.25,
                    fxEmitFromBase=0,
                }
            },
            {
                FXControl =
                {
                    name="Punch_Mega",
                    sound="Punch_Mega",
                    particle="flash",
                    particleDuration=0.25,
                    fxEmitFromBase=0,
                }
            },
            {
                FXControl =
                {
                    name="Punch_Strong",
                    sound="Punch_Strong",
                    particle="flash",
                    particleDuration=0.25,
                    fxEmitFromBase=0,
                }
            },
            {
                FXControl =
                {
                    name="Slap_Weak",
                    sound="Slap_Weak",
                    particle="flash",
                    particleDuration=0.25,
                    fxEmitFromBase=0,
                }
            },
            {
                FXControl =
                {
                    name="Slap_Mega",
                    sound="Slap_Mega",
                    particle="flash",
                    particleDuration=0.25,
                    fxEmitFromBase=0,
                }
            },
            {
                FXControl =
                {
                    name="Slap_Strong",
                    sound="Slap_Strong",
                    particle="flash",
                    particleDuration=0.25,
                    fxEmitFromBase=0,
                }
            },
        },
        fxHit =
        {
            {
                FXHit =
                {
                    instigator="rayman",
                    level=0,
                    name="Punch_Weak",
                }
            },
            {
                FXHit =
                {
                    instigator="rayman",
                    level=1,
                    name="Punch_Strong",
                }
            },
            {
                FXHit =
                {
                    instigator="rayman",
                    level=2,
                    name="Punch_Mega",
                }
            },
            {
                FXHit =
                {
                    instigator="rayman2",
                    level=0,
                    name="Punch_Weak",
                }
            },
            {
                FXHit =
                {
                    instigator="rayman2",
                    level=1,
                    name="Punch_Strong",
                }
            },
            {
                FXHit =
                {
                    instigator="rayman2",
                    level=2,
                    name="Punch_Mega",
                }
            },
            {
                FXHit =
                {
                    instigator="globox",
                    level=0,
                    name="Slap_Weak",
                }
            },
            {
                FXHit =
                {
                    instigator="globox",
                    level=1,
                    name="Slap_Strong",
                }
            },
            {
                FXHit =
                {
                    instigator="globox",
                    level=2,
                    name="Slap_Mega",
                }
            },
            {
                FXHit =
                {
                    instigator="globox2",
                    level=0,
                    name="Slap_Weak",
                }
            },
            {
                FXHit =
                {
                    instigator="globox2",
                    level=1,
                    name="Slap_Strong",
                }
            },
            {
                FXHit =
                {
                    instigator="globox2",
                    level=2,
                    name="Slap_Mega",
                }
            },
        },
    }
}

appendTable(params.Actor_Template.COMPONENTS,{component})
component = {}
