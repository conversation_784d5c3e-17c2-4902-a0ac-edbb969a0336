<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-0.20684354519461"/>
			<AngleLocal type="number" value="-0.20684354519461"/>
			<Lenght type="number" value="0.062208164483309"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Foot_L"/>
			<Pos type="vector" x="0.26769188046455" y="0.028436286374927" z="0"/>
			<PosEnd type="vector" x="0.32989963889122" y="0.028660863637924" z="0"/>
			<PosLocal type="vector" x="0.26769188046455" y="0.028436286374927" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp280"/>
				<Element index="2" type="string" value="MyApp281"/>
				<Element index="3" type="string" value="MyApp282"/>
				<Element index="4" type="string" value="MyApp283"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A3"/>
			<UID type="string" value="MyApp278"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="2">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-179.81397643429"/>
			<AngleLocal type="number" value="-179.81397643429"/>
			<Lenght type="number" value="0.069169975817204"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Foot_R"/>
			<Pos type="vector" x="0.33416658639908" y="0.058978721499443" z="0"/>
			<PosEnd type="vector" x="0.26499697566032" y="0.059203296899796" z="0"/>
			<PosLocal type="vector" x="0.33416658639908" y="0.058978721499443" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp292"/>
				<Element index="2" type="string" value="MyApp293"/>
				<Element index="3" type="string" value="MyApp294"/>
				<Element index="4" type="string" value="MyApp295"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A3"/>
			<UID type="string" value="MyApp279"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A3.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Feet01"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp281"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp278"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-0.10882902145386" y="0.012414669618011" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.0036100929137319" y="-0.99999344348907" z="0"/>
			<PosUV type="vector" x="0.26096668839455" y="0.015997257083654" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A3"/>
			<UID type="string" value="MyApp280"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp280"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp278"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="-0.10983359068632" y="-0.0080841919407248" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.0036100929137319" y="0.99999344348907" z="0"/>
			<PosUV type="vector" x="0.26083019375801" y="0.036495760083199" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A3"/>
			<UID type="string" value="MyApp281"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp283"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp278"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="1.0590053796768" y="0.013013805262744" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.0036100929137319" y="-0.99999344348907" z="0"/>
			<PosUV type="vector" x="0.33361721038818" y="0.015660393983126" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A3"/>
			<UID type="string" value="MyApp282"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp282"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp278"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="1.0651677846909" y="-0.0065855975262821" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.0036100929137319" y="0.99999344348907" z="0"/>
			<PosUV type="vector" x="0.33392980694771" y="0.035261053591967" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A3"/>
			<UID type="string" value="MyApp283"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="5">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp293"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp279"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-0.079837083816528" y="0.0092009082436562" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.0032467180863023" y="0.99999475479126" z="0"/>
			<PosUV type="vector" x="0.33971875905991" y="0.068161651492119" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A3"/>
			<UID type="string" value="MyApp292"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="6">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp292"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp279"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="-0.082620479166508" y="-0.01174707710743" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.0032467180863023" y="-0.99999475479126" z="0"/>
			<PosUV type="vector" x="0.33984327316284" y="0.047213152050972" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A3"/>
			<UID type="string" value="MyApp293"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="7">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp295"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp279"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="1.1053273677826" y="0.0112928096205" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.0032467180863023" y="0.99999475479126" z="0"/>
			<PosUV type="vector" x="0.25774818658829" y="0.0705197006464" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A3"/>
			<UID type="string" value="MyApp294"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="8">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp294"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp279"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="1.0959135293961" y="-0.012573271989822" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.0032467180863023" y="-0.99999475479126" z="0"/>
			<PosUV type="vector" x="0.25832185149193" y="0.046651631593704" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A3"/>
			<UID type="string" value="MyApp295"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A3"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="157.8798828125" y="40.************" z="0"/>
	<zoomSize type="vector" x="1235.166015625" y="1235.1661376953" z="1.1663510799408"/>
</root>
