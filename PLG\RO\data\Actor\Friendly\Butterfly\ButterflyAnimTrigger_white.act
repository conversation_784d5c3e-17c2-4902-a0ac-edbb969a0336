params =
{
    
NAME = "Actor_Template",
Actor_Template =
    {
        SCALE = vector2dNew(3.0,2.0),
        RANK = 0,		  

        COMPONENTS =
        {
            {
                NAME="AnimatedComponent_Template",
                AnimatedComponent_Template =
                {
                    animationPath = "Actor/Friendly/Butterfly/animation/",
                    defaultAnimation = "UNTRIGGER",
                    animSet=
                    {
						SubAnimSet_Template =
                        {
		                    animations={
								{NAME="SubAnim_Template",SubAnim_Template={friendlyName="Stand",name="stand.anm",loop=1}},
								{NAME="SubAnim_Template",SubAnim_Template={friendlyName="Takeoff",name="takeoff.anm"}},
								{NAME="SubAnim_Template",SubAnim_Template={friendlyName="offstand",name="offstand.anm",loop=1}},
								{NAME="SubAnim_Template",SubAnim_Template={friendlyName="landing",name="landing.anm"}},
							},
						},
					},
					tree = 
                    {
                        AnimTree_Template =
                        {
                            nodes =
                            {
                                {
                                    NAME="AnimTreeNodePlayAnim_Template",
                                    AnimTreeNodePlayAnim_Template =
                                    {
                                        nodeName = "TRIGGER",
                                        animationName = "Offstand"
                                    }
                                },
                                {
                                    NAME="AnimTreeNodePlayAnim_Template",
                                    AnimTreeNodePlayAnim_Template =
                                    {
                                        nodeName = "UNTRIGGER",
                                        animationName = "Stand"
                                    }
                                },
                            },
                            nodeTransitions =
                            {
                                {
                                    BlendTreeTransition_Template =
                                    {
                                        from =
                                        {
                                            { VAL = "TRIGGER" },
                                        },
                                        to =
                                        {
                                            { VAL = "UNTRIGGER" },
                                        },
                                        node =
                                        {
                                            NAME="AnimTreeNodePlayAnim_Template",
                                            AnimTreeNodePlayAnim_Template =
                                            {
                                                animationName = "landing"
                                            }
                                        },
                                        blend = 4,
                                    }
                                },
                                {
                                    BlendTreeTransition_Template =
                                    {
                                        from =
                                        {
                                            { VAL = "UNTRIGGER" },
                                        },
                                        to =
                                        {
                                            { VAL = "TRIGGER" },
                                        },
                                        node =
                                        {
                                            NAME="AnimTreeNodePlayAnim_Template",
                                            AnimTreeNodePlayAnim_Template =
                                            {
                                                animationName = "Takeoff"
                                            }
                                        },
                                        blend = 4,
                                    }
                                },
                            },
                        }
                    },
                }
            },
            {
                NAME="ActorSpawnComponent_Template",
                ActorSpawnComponent_Template =
                {
					spawnActors=
					{
					    {
						    SpawnData =
						    {
							    spawnActorBoneName = "B_butterfly01",
                                actorLua = "FX/Common/Prtcl_AirFlow_Standalone.act",
						    },
						},
						{
						    SpawnData =
						    {
							    spawnActorBoneName = "B_butterfly03",
                                actorLua = "FX/Common/Prtcl_AirFlow_Standalone.act",
						    },				
						}
					},
				},
			},	
            {
                NAME = "LinkComponent_Template",
                LinkComponent_Template =
                {
                },
            },
            {
                NAME = "PhantomDetectorComponent_Template",
                PhantomDetectorComponent_Template =
                {
 					Shape =
					{
						Name="PhysShapePolygon",
						PhysShapePolygon =
						{
							Points={
								{VAL=vector2dNew(-1.0,1.0)},
								{VAL=vector2dNew(1.0,1.0)},
								{VAL=vector2dNew(1.0,-1.0)},
								{VAL=vector2dNew(-1.0,-1.0)},
							},
						}
					},
                },
            },
            {
                NAME="TriggerComponent_Template",
                TriggerComponent_Template =
                {
                    triggerOnWind = 1,
					TriggerOnce = 0,
                    TriggerSelf = 1,
                    TriggerChildren = 1,
                    TriggerActivator = 0,
                    ResetOnCheckpoint = 1,
                    onEnterEvent = 
                    {
                        NAME="EventTrigger",
                        EventTrigger = 
                        {
                            activated = 1,
                        }
                    },
                    onExitEvent = 
                    {
                        NAME="EventTrigger",
                        EventTrigger = 
                        {
                            activated = 0,
                        }
                    },
				},
			},	
            {
                NAME="AnimTriggeredComponent_Template",
                AnimTriggeredComponent_Template =
                {
					TriggeredAction = "TRIGGER",
					UntriggeredAction = "UNTRIGGER",		
				},
			},				
        },
    }
}

includeReference("Actor/Friendly/Butterfly/Components/Butterfly_sound.ilu")
includeReference("Actor/Friendly/Butterfly/Components/Butterfly_fxController.ilu")
