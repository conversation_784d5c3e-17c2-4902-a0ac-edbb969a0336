includeReference("Actor/Friendly/RedWizard/RedWizard_Fakir/RedWizard_Fakir_Vaseless.ilu")

component =
{
    NAME = "StickToPolylinePhysComponent_Template",
    StickToPolylinePhysComponent_Template =
    {
        physGravityMultiplier = 3.000000,
        physForce2Speed = 0.016667,
        physUnstickMaxAngle = 44.690697,
        physAngularSpeedMinAngular = 149.541992,
        physTransferSpeedLossMinAngle = 5.729577,
        physTransferSpeedLossMaxAngle = 45.836617,
        physDefaultStickMax = 299.999908,
    },
}
appendTable(params.Actor_Template.COMPONENTS,{component})
