includeReference("Actor/Includes/helpers.ilu")
includeReference("Actor/Includes/gameplay_types.ilu")

act_defaultColor = getValue(act_defaultColor, Color.White)
    
params =
{
    
NAME = "Actor_Template",
Actor_Template =
    {
        SCALE = vector2dNew(5.000000,5.000000),
        COMPONENTS =
        {
            {
                NAME = "PhantomComponent_Template",
                PhantomComponent_Template =
                {
                    polyline = "L_Break",
                    collisionGroup = 8,
                    -- drawDebug = 1,
                },
            },
            {
                NAME = "Ray_StackableComponent_Template",
                Ray_StackableComponent_Template =
                {
                    attachBone = "B_Snap_Object",
                    polyline = "L_Break",
                    polylineMaterial = "GameMaterial/Stackable.gmt",
                },
            },
            {
                NAME = "Ray_BreakableAIComponent_Template",
                Ray_BreakableAIComponent_Template =
                {
                    registerToAIManager = 1,
                    damageLevels =
                    {
                        {
                            VAL = 5,
                        },
                        {
                            VAL = 25,
                        },
                        {
                            VAL = 50,
                        },
                        {
                            VAL = 100,
                        },
                    },
                    health = 100,
                    destructionStageCount = 5,
                    destructionStages =
                    {
                        {DestructionStage =
                        {
                            rumble = "Step00_Rumble",
                            destroy = "Step00_Destroy",
                        }},
                        {DestructionStage =
                        {
                            rumble = "Step01_Rumble",
                            destroy = "Step01_Destroy",
                        }},
                        {DestructionStage =
                        {
                            rumble = "Step02_Rumble",
                            destroy = "Step02_Destroy",
                        }},
                        {DestructionStage =
                        {
                            rumble = "Step03_Rumble",
                            destroy = "Step03_Destroy",
                        }},
                        {DestructionStage =
                        {
                            rumble = "Step04_Rumble",
                            destroy = "Step04_Destroy",
                        }},
                    },
                },
            },
            {
                NAME = "AnimLightComponent_Template",
                AnimLightComponent_Template =
                {
                    defaultColor = act_defaultColor,
                    animationPath = "Actor/Breakable/BreakTotem/BreakTotem_Destructible/Animation/",
                    animSet =
                    {
                        SubAnimSet_Template =
                        {
                            animations =
                            {
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "Idle",              -- not a step
                                        name = "Destruction.anm",
                                        markerStart = "MRK_Step00_Start",
                                        markerStop = "MRK_Step00_Stop",
                                    },
                                },
                                
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "Step00_Rumble",
                                        name = "Destruction.anm",
                                        markerStart = "MRK_Step01_Rumble_Start",
                                        markerStop = "MRK_Step01_Rumble_Stop",
                                    },
                                },
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "Step00_Destroy",
                                        name = "Destruction.anm",
                                        markerStart = "MRK_Step01_to_Step02_Start",
                                        markerStop = "MRK_Step01_to_Step02_Stop",
                                    },
                                },

                                -- {
                                    -- SubAnim_Template =
                                    -- {
                                        -- friendlyName = "Step02_Idle",
                                        -- name = "Destruction.anm",
                                        -- markerStart = "MRK_Step02_Start",
                                        -- markerStop = "MRK_Step02_Stop",
                                    -- },
                                -- },
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "Step01_Rumble",
                                        name = "Destruction.anm",
                                        markerStart = "MRK_Step03_Rumble_Start",
                                        markerStop = "MRK_Step03_Rumble_Stop",
                                    },
                                },
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "Step01_Destroy",
                                        name = "Destruction.anm",
                                        markerStart = "MRK_Step03_to_Step04_Start",
                                        markerStop = "MRK_Step03_to_Step04_Stop",
                                    },
                                },

                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "Step02_Rumble",
                                        name = "Destruction.anm",
                                        markerStart = "MRK_Step05_Rumble_Start",
                                        markerStop = "MRK_Step05_Rumble_Stop",
                                    },
                                },
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "Step02_Destroy",
                                        name = "Destruction.anm",
                                        markerStart = "MRK_Step05_to_Step06_Start",
                                        markerStop = "MRK_Step05_to_Step06_Stop",
                                    },
                                },

                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "Step03_Rumble",
                                        name = "Destruction.anm",
                                        markerStart = "MRK_Step07_Rumble_Start",
                                        markerStop = "MRK_Step07_Rumble_Stop",
                                    },
                                },
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "Step03_Destroy",
                                        name = "Destruction.anm",
                                        markerStart = "MRK_Step07_to_Step08_Start",
                                        markerStop = "MRK_Step07_to_Step08_Stop",
                                    },
                                },

                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "Step04_Rumble",
                                        name = "Destruction.anm",
                                        markerStart = "MRK_Step09_Rumble_Start",
                                        markerStop = "MRK_Step09_Rumble_Stop",
                                    },
                                },
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "Step04_Destroy",
                                        name = "Destruction.anm",
                                        markerStart = "MRK_Step09_to_Step10_Start",
                                        markerStop = "MRK_Step09_to_Step10_Stop",
                                    },
                                },

                            },
                        },
                    },
                },
            },
            {
                NAME="RenderSimpleAnimComponent_Template",
                RenderSimpleAnimComponent_Template =
                {
                }
            },
        },
    },
    ActorEditorParams =
    {
    },
}
