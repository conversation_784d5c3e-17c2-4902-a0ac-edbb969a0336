<?xml version="1.0" ?>
<root>
	<AnimsList>
		<Element index="1" type="string" value="Actor/Friendly/Fish/animation/Death.anm"/>
		<Element index="2" type="string" value="Actor/Friendly/Fish/animation/FishLum_Empty.anm"/>
		<Element index="3" type="string" value="Actor/Friendly/Fish/animation/FishLum_Rope.anm"/>
		<Element index="4" type="string" value="Actor/Friendly/Fish/animation/Fish_Hit.anm"/>
		<Element index="5" type="string" value="Actor/Friendly/Fish/animation/fishAnim1.anm"/>
		<Element index="6" type="string" value="Actor/Friendly/Fish/animation/fishAnim2.anm"/>
	</AnimsList>
	<PatchBankList>
		<fish type="string" value="Actor/Friendly/Fish/animation/Fish.tga"/>
		<fish_B type="string" value="Actor/Friendly/Fish/animation/Fish_B.tga"/>
	</PatchBankList>
	<Scale type="number" value="1"/>
	<SceneVersion type="number" value="20"/>
	<Squeleton type="string" value="Actor/Friendly/Fish/animation/Fish.skl"/>
	<UseDataFolder type="boolean" value="true"/>
	<UseRelative type="boolean" value="true"/>
</root>
