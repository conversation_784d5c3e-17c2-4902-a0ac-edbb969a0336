<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="0.26711142745646"/>
			<AngleLocal type="number" value="0.26711142745646"/>
			<Lenght type="number" value="0.078879080712795"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_03_R"/>
			<Pos type="vector" x="0.8466317653656" y="0.052294716238976" z="0"/>
			<PosEnd type="vector" x="0.92550998926163" y="0.051926985383034" z="0"/>
			<PosLocal type="vector" x="0.8466317653656" y="0.052294716238976" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="D739MyApp725"/>
				<Element index="2" type="string" value="D739MyApp726"/>
				<Element index="3" type="string" value="D739MyApp727"/>
				<Element index="4" type="string" value="D739MyApp728"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A13"/>
			<UID type="string" value="D739MyApp724"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A13.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Cloak02_Fx_R"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D739MyApp726"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D739MyApp724"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-0.043995767831802" y="0.038878805935383" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.0046619568020105" y="-0.99998909235001" z="0"/>
			<PosUV type="vector" x="0.84298020601273" y="0.013432510197163" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A13"/>
			<UID type="string" value="D739MyApp725"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D739MyApp725"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D739MyApp724"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="-0.22454734146595" y="-0.050505954772234" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.0046619568020105" y="0.99998909235001" z="0"/>
			<PosUV type="vector" x="0.82915532588959" y="0.10288269817829" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A13"/>
			<UID type="string" value="D739MyApp726"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D739MyApp728"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D739MyApp724"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="1.1592782735825" y="0.041002161800861" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.0046619568020105" y="-0.99998909235001" z="0"/>
			<PosUV type="vector" x="0.93788242340088" y="0.010866694152355" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A13"/>
			<UID type="string" value="D739MyApp727"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D739MyApp727"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D739MyApp724"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="0.89666658639908" y="-0.050053868442774" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.0046619568020105" y="0.99998909235001" z="0"/>
			<PosUV type="vector" x="0.91759258508682" y="0.10201831161976" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A13"/>
			<UID type="string" value="D739MyApp728"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A13"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="131.33435058594" y="114.52886962891" z="0"/>
	<zoomSize type="vector" x="1508.6552734375" y="1508.6552734375" z="1.3603743314743"/>
</root>
