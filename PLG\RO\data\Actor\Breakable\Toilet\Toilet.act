includeReference("Actor/Includes/helpers.ilu")
includeReference("Actor/Includes/gameplay_types.ilu")
params =
{
    
NAME = "Actor_Template",
Actor_Template =
    {
        SCALE = vector2dNew(5.000000,5.000000),
        COMPONENTS =
        {
            {
                NAME = "AnimatedComponent_Template",
                AnimatedComponent_Template =
                {
                    defaultAnimation = "IDLE",
                    useBase=0,
                    animSet =
                    {
                        SubAnimSet_Template =
                        {
                            animations =
                            {
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "Step0",
                                        name = "Actor/Breakable/Toilet/Animation/ToiletAnim1.anm",
                                        loop = 1,
                                        markerStart="MRK_Step0_Start",
                                        markerStop="MRK_Step0_Stop",
                                    },
                                },
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "Step01",
                                        name = "Actor/Breakable/Toilet/Animation/ToiletAnim1.anm",
                                        loop = 1,
                                        markerStart="MRK_Step01_Start",
                                        markerStop="MRK_Step01_Stop",
                                    },
                                },
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "Step02",
                                        name = "Actor/Breakable/Toilet/Animation/ToiletAnim1.anm",
                                        loop = 1,
                                        markerStart="MRK_Step02_Start",
                                        markerStop="MRK_Step02_Stop",
                                    },
                                },
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "Step03",
                                        name = "Actor/Breakable/Toilet/Animation/ToiletAnim1.anm",
                                        loop = 1,
                                        markerStart="MRK_Step03_Start",
                                        markerStop="MRK_Step03_Stop",
                                    },
                                },
                                -- {
                                    -- SubAnim_Template =
                                    -- {
                                        -- friendlyName = "death",
                                        -- name = "Actor/Breakable/Toilet/Animation/ToiletAnim1.anm",
                                        -- loop = 1,
                                        -- markerStart="MRK_Step03_Start",
                                        -- markerStop="MRK_Step03_Stop",
                                    -- },
                                -- },
                                -- {
                                    -- SubAnim_Template =
                                    -- {
                                        -- friendlyName = "ReceiveHit_Step0",
                                        -- name = "Actor/Breakable/Toilet/Animation/ToiletAnim1.anm",
                                        -- markerStart="MRK_Step0_Start",
                                        -- markerStop="MRK_Step0_Stop",
                                    -- },
                                -- },
                                -- {
                                    -- SubAnim_Template =
                                    -- {
                                        -- friendlyName = "ReceiveHit_Step01",
                                        -- name = "Actor/Breakable/Toilet/Animation/ToiletAnim1.anm",
                                        -- markerStart="MRK_Step01_Start",
                                        -- markerStop="MRK_Step01_Stop",
                                    -- },
                                -- },
                                -- {
                                    -- SubAnim_Template =
                                    -- {
                                        -- friendlyName = "ReceiveHit_Step02",
                                        -- name = "Actor/Breakable/Toilet/Animation/ToiletAnim1.anm",
                                        -- markerStart="MRK_Step02_Start",
                                        -- markerStop="MRK_Step02_Stop",
                                    -- },
                                -- },
                                -- {
                                    -- SubAnim_Template =
                                    -- {
                                        -- friendlyName = "ReceiveHit_Step03",
                                        -- name = "Actor/Breakable/Toilet/Animation/ToiletAnim1.anm",
                                        -- markerStart="MRK_Step03_Start",
                                        -- markerStop="MRK_Step03_Stop",
                                    -- },
                                -- },
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "Hit_To_Step01",
                                        name = "Actor/Breakable/Toilet/Animation/ToiletAnim1.anm",
                                        loop = 0,
                                        markerStart="MRK_Hit_To_Step01_Start",
                                        markerStop="MRK_Hit_To_Step01_Stop",
                                    },
                                },
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "Hit_To_Step02",
                                        name = "Actor/Breakable/Toilet/Animation/ToiletAnim1.anm",
                                        loop = 0,
                                        markerStart="MRK_Hit_To_Step02_Start",
                                        markerStop="MRK_Hit_To_Step02_Stop",
                                    },
                                },
								{
                                    SubAnim_Template =
                                    {
                                        friendlyName = "Hit_To_Step03",
                                        name = "Actor/Breakable/Toilet/Animation/ToiletAnim1.anm",
                                        loop = 0,
                                        markerStart="MRK_Hit_To_Step03_Start",
                                        markerStop="MRK_Hit_To_Step03_Stop",
                                    },
                                },
                            },
                        },
                    },
                    inputs =
                    {
                        {InputDesc={name="Health", varType=AnimInputTypes.uint}},             -- Used to play random animations
                    },
                    tree =  
                    {
                        AnimTree_Template =
                        {
                            nodes =
                            {
                                -- {
                                    -- NAME="AnimTreeNodePlayAnim_Template",
                                    -- AnimTreeNodePlayAnim_Template =
                                    -- {
                                        -- nodeName = "RECEIVEHIT",
                                        -- animationName = ""
                                    -- }
                                -- },
                                {
                                    NAME="BlendTreeNodeChooseBranch_Template",
                                    BlendTreeNodeChooseBranch_Template =
                                    {
                                        nodeName = "IDLE",
                                        leafs =
                                        {
                                            {
                                                NAME="AnimTreeNodePlayAnim_Template",
                                                AnimTreeNodePlayAnim_Template =
                                                {
                                                    nodeName = "Step03",
													animationName = "Step03",
                                                }
                                            },
                                            {
                                                NAME="AnimTreeNodePlayAnim_Template",
                                                AnimTreeNodePlayAnim_Template =
                                                {
                                                    nodeName = "Step02",
													animationName = "Step02",
                                                }
                                            },
                                            {
                                                NAME="AnimTreeNodePlayAnim_Template",
                                                AnimTreeNodePlayAnim_Template =
                                                {
                                                    nodeName = "Step01",
													animationName = "Step01",
                                                }
                                            },
                                            {
                                                NAME="AnimTreeNodePlayAnim_Template",
                                                AnimTreeNodePlayAnim_Template =
                                                {
                                                    nodeName = "Step0",
													animationName = "Step0",
                                                }
                                            },
                                        },
                                        leafsCriterias =
                                        {
                                            {
                                                BlendLeaf =
                                                {
                                                    -- phase 1
                                                    criterias =
                                                    {
                                                        {CriteriaDesc={name="Health",eval="<=",value=25}},
                                                    },
                                                }
                                            },
                                            {
                                                BlendLeaf =
                                                {
                                                    -- phase 1
                                                    criterias =
                                                    {
                                                        {CriteriaDesc={name="Health",eval="<=",value=50}},
                                                    },
                                                }
                                            },
                                            {
                                                BlendLeaf =
                                                {
                                                    -- phase 1
                                                    criterias =
                                                    {
                                                        {CriteriaDesc={name="Health",eval="<=",value=75}},
                                                    },
                                                }
                                            },
                                            {
                                                BlendLeaf =
                                                {
                                                }
                                            },
                                        },
                                    }
                                },
                                {
                                    NAME="AnimTreeNodePlayAnim_Template",
                                    AnimTreeNodePlayAnim_Template =
                                    {
                                        nodeName = "Death",
                                        animationName = "Step03"
                                    }
                                },
                                {
                                    NAME="BlendTreeNodeChooseBranch_Template",
                                    BlendTreeNodeChooseBranch_Template =
                                    {
                                        nodeName = "ReceiveHit",
                                        leafs =
                                        {
                                            {
                                                NAME="AnimTreeNodePlayAnim_Template",
                                                AnimTreeNodePlayAnim_Template =
                                                {
                                                    nodeName = "Hit_To_Step03",
													animationName = "Hit_To_Step03",
                                                }
                                            },
                                            {
                                                NAME="AnimTreeNodePlayAnim_Template",
                                                AnimTreeNodePlayAnim_Template =
                                                {
                                                    nodeName = "Hit_To_Step02",
													animationName = "Hit_To_Step02",
                                                }
                                            },
                                            {
                                                NAME="AnimTreeNodePlayAnim_Template",
                                                AnimTreeNodePlayAnim_Template =
                                                {
                                                    nodeName = "Hit_To_Step01",
													animationName = "Hit_To_Step01",
                                                }
                                            },
                                        },
                                        leafsCriterias =
                                        {

                                            {
                                                BlendLeaf =
                                                {
                                                    -- phase 1
                                                    criterias =
                                                    {
                                                        {CriteriaDesc={name="Health",eval="<=",value=25}},
                                                    },
                                                }
                                            },
                                            {
                                                BlendLeaf =
                                                {
                                                    -- phase 1
                                                    criterias =
                                                    {
                                                        {CriteriaDesc={name="Health",eval="<=",value=50}},
                                                    },
                                                }
                                            },
                                            {
                                                BlendLeaf =
                                                {
                                                    -- phase 1
                                                    criterias =
                                                    {
                                                        {CriteriaDesc={name="Health",eval="<=",value=75}},
                                                    },
                                                }
                                            },
                                            {
                                                BlendLeaf =
                                                {
                                                }
                                            },
                                        },
                                    }
                                },
                            },
                        }
                    }
                },
            },
            {
                NAME = "SimpleAIComponent_Template",
                SimpleAIComponent_Template =
                {
                    faction = Faction.Neutral,
                    damageLevels =
                    {
                        {
                            VAL = 25,
                        },
                        {
                            VAL = 50,
                        },
                        {
                            VAL = 75,
                        },
						{
                            VAL = 4,
                        },
                    },
                    genericBehavior =
                    {
                        NAME = "AIPlayActionsBehavior_Template",
                        AIPlayActionsBehavior_Template =
                        { 
                            actions = 
                            {
                                {
                                    NAME="AIPlayAnimAction_Template",
                                    AIPlayAnimAction_Template =
                                    {
                                        action = "idle",
                                    }
                                },
                            }
                        },
                    },
                    receiveHitBehavior =
                    {
                        {
                            NAME="Ray_AIReceiveHitBehavior_Template",
                            Ray_AIReceiveHitBehavior_Template =
                            {
                                receiveHits =
                                {
                                    -- all types, all levels
                                    {
                                        ReceiveHitData =
                                        {
                                            action = 
                                            {
                                                NAME="Ray_AIGround_ReceiveNormalHitAction_Template",
                                                Ray_AIGround_ReceiveNormalHitAction_Template =
                                                {
                                                    faceHitDir=0,
                                                    canBlockHits=1,
                                                    isInterruptible=0,
                                                }
                                            },
                                        }
                                    },
                                },
                            }
                        },
                    },
                    deathBehavior =
                    {
                        NAME = "AIPlayActionsBehavior_Template",
                        AIPlayActionsBehavior_Template =
                        { 
                            actions = 
                            {
                                {
                                    NAME="AIPlayAnimAction_Template",
                                    AIPlayAnimAction_Template =
                                    {
                                        action = "death",
                                    }
                                },
                            }
                        },
                    },
                },
            },
            {
                NAME = "PhantomComponent_Template",
                PhantomComponent_Template =
                {
                    collisionGroup = CollisionGroup.Character,
                    -- drawDebug = 1,
                    shape =
                    {
                        NAME = "PhysShapePolygon",
                        PhysShapePolygon = 
                        { 
                            Points =
                            {
                                { VAL = vector2dNew(-0.6,-0.5) }, 
                                { VAL = vector2dNew(-0.6,3.3) },
                                { VAL = vector2dNew(0.6,3.3) },
                                { VAL = vector2dNew(0.6,-0.5) },
                            },
						}
                    },
                },
            },
            {
                NAME = "StickToPolylinePhysComponent_Template",
                StickToPolylinePhysComponent_Template =
                {
                    physRadius = 0.1,
                },
            },
            {
                NAME="PolylineComponent_Template",
                PolylineComponent_Template=
                {
                    polylines =
                    {
                        {
                            PolyData =
                            {
                                friction = 1.0,
		                        speedLoss = 1.0,
		                        usePhantom = 0,
                                bones =
                                {
                                    {PolylineBoneData ={Name = "b1"},},
                                    {PolylineBoneData ={Name = "b2"},},
                                    {PolylineBoneData ={Name = "b3"},},
                                    {PolylineBoneData ={Name = "b4"},},
                                },
                            },
	                    },
                    }
                }
            },
			{
				NAME="PushedComponent_Template",
				PushedComponent_Template =
				{
				}
			},
            {
                NAME="RenderSimpleAnimComponent_Template",
                RenderSimpleAnimComponent_Template =
                {
                }
            },
        },
    },
    ActorEditorParams =
    {
    },
}
includeReference("World/Common/Components/CharacterDebuggerComponent.ilu")
