<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="18.087016696241"/>
			<AngleLocal type="number" value="18.087016696241"/>
			<Lenght type="number" value="0.16264875233173"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_01_L"/>
			<Pos type="vector" x="0.29744726419449" y="0.81265777349472" z="0"/>
			<PosEnd type="vector" x="0.45205891132355" y="0.76216167211533" z="0"/>
			<PosLocal type="vector" x="0.29744726419449" y="0.81265777349472" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp2470"/>
				<Element index="2" type="string" value="MyApp2471"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A34"/>
			<UID type="string" value="MyApp2468"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="2">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-1.10475051147"/>
			<AngleLocal type="number" value="-19.191767207711"/>
			<Lenght type="number" value="0.18272769451141"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_02_L"/>
			<ParentUID type="string" value="MyApp2468"/>
			<Pos type="vector" x="0.45205891132355" y="0.76216167211533" z="0"/>
			<PosEnd type="vector" x="0.63475263118744" y="0.76568472385406" z="0"/>
			<PosLocal type="vector" x="0" y="2.3370603230433e-009" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp2476"/>
				<Element index="2" type="string" value="MyApp2477"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A34"/>
			<UID type="string" value="MyApp2469"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A34.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Arm08_L"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2471"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2468"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-0.01240321714431" y="0.021831119433045" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.31046104431152" y="-0.95058614015579" z="0"/>
			<PosUV type="vector" x="0.28875187039375" y="0.79253172874451" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A34"/>
			<UID type="string" value="MyApp2470"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2470"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2468"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="-0.061140734702349" y="-0.020011086016893" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.31046104431152" y="0.95058614015579" z="0"/>
			<PosUV type="vector" x="0.29420685768127" y="0.83476740121841" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A34"/>
			<UID type="string" value="MyApp2471"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2477"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2469"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.099833786487579" y="0.99500417709351" z="0"/>
					<Pos type="vector" x="1.0299445390701" y="0.051970839500427" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.080631211400032" y="-0.9967440366745" z="0"/>
			<PosUV type="vector" x="0.64122533798218" y="0.71382904052734" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A34"/>
			<UID type="string" value="MyApp2476"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2476"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2469"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.11030147969723" y="-0.9938982129097" z="0"/>
					<Pos type="vector" x="1.0181819200516" y="-0.021818062290549" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.12944367527962" y="0.99158680438995" z="0"/>
			<PosUV type="vector" x="0.63765370845795" y="0.78756278753281" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A34"/>
			<UID type="string" value="MyApp2477"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A34"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="-416.4091796875" y="-1353.8833007813" z="0"/>
	<zoomSize type="vector" x="2603.1303710938" y="2603.1303710938" z="2.5175337791443"/>
</root>
