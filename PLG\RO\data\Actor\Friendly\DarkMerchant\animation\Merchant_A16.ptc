<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="4.8845932908818"/>
			<AngleLocal type="number" value="4.8845932908818"/>
			<Lenght type="number" value="0.30353233218193"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_01_R"/>
			<Pos type="vector" x="0.025501711294055" y="0.45571079850197" z="0"/>
			<PosEnd type="vector" x="0.32793167233467" y="0.42986533045769" z="0"/>
			<PosLocal type="vector" x="0.025501711294055" y="0.45571079850197" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="D782D779D776D736D647MyApp578"/>
				<Element index="2" type="string" value="D782D779D776D736D647MyApp579"/>
				<Element index="3" type="string" value="D782D779D776D736D647MyApp580"/>
				<Element index="4" type="string" value="D782D779D776D736D647MyApp581"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="D782D779D776D736D647MyApp576"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="2">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-108.33964624091"/>
			<AngleLocal type="number" value="-113.22423953179"/>
			<Lenght type="number" value="0.099736139178276"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_04_R"/>
			<ParentCut type="boolean" value="true"/>
			<ParentUID type="string" value="D782D779D776D736D647MyApp576"/>
			<Pos type="vector" x="0.43935975432396" y="0.21290385723114" z="0"/>
			<PosEnd type="vector" x="0.40797784924507" y="0.30757421255112" z="0"/>
			<PosLocal type="vector" x="0.12949746847153" y="-0.20668551325798" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp834"/>
				<Element index="2" type="string" value="MyApp835"/>
				<Element index="3" type="string" value="MyApp836"/>
				<Element index="4" type="string" value="MyApp837"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="MyApp793"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="3">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-83.033066042903"/>
			<AngleLocal type="number" value="-87.************"/>
			<Lenght type="number" value="0.10932342708111"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_02_R"/>
			<ParentCut type="boolean" value="true"/>
			<ParentUID type="string" value="D782D779D776D736D647MyApp576"/>
			<Pos type="vector" x="0.36959367990494" y="0.22632025182247" z="0"/>
			<PosEnd type="vector" x="0.38285422325134" y="0.33483648300171" z="0"/>
			<PosLocal type="vector" x="0.058842360973358" y="-0.19925835728645" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp838"/>
				<Element index="2" type="string" value="MyApp839"/>
				<Element index="3" type="string" value="MyApp840"/>
				<Element index="4" type="string" value="MyApp841"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="D793MyApp793"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="4">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-98.************"/>
			<AngleLocal type="number" value="-103.25045868289"/>
			<Lenght type="number" value="0.035304117947817"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_06_R"/>
			<ParentCut type="boolean" value="true"/>
			<ParentUID type="string" value="D782D779D776D736D647MyApp576"/>
			<Pos type="vector" x="0.50820499658585" y="0.21147294342518" z="0"/>
			<PosEnd type="vector" x="0.50306844711304" y="0.24640139937401" z="0"/>
			<PosLocal type="vector" x="0.1982145011425" y="-0.20224913954735" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp1023"/>
				<Element index="2" type="string" value="MyApp1024"/>
				<Element index="3" type="string" value="MyApp1025"/>
				<Element index="4" type="string" value="MyApp1026"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="MyApp997"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="5">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-112.7938104137"/>
			<AngleLocal type="number" value="-117.67840370458"/>
			<Lenght type="number" value="0.14791209995747"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_03_R"/>
			<ParentCut type="boolean" value="true"/>
			<ParentUID type="string" value="D782D779D776D736D647MyApp576"/>
			<Pos type="vector" x="0.42577087879181" y="0.26634928584099" z="0"/>
			<PosEnd type="vector" x="0.36846736073494" y="0.40271019935608" z="0"/>
			<PosLocal type="vector" x="0.11140710115433" y="-0.15459127724171" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp999"/>
				<Element index="2" type="string" value="MyApp1000"/>
				<Element index="3" type="string" value="MyApp1001"/>
				<Element index="4" type="string" value="MyApp1002"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="MyApp998"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="6">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-108.14492366223"/>
			<AngleLocal type="number" value="-113.02951695311"/>
			<Lenght type="number" value="0.099288947880268"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_05_R"/>
			<ParentCut type="boolean" value="true"/>
			<ParentUID type="string" value="D782D779D776D736D647MyApp576"/>
			<Pos type="vector" x="0.44638603925705" y="0.25143086910248" z="0"/>
			<PosEnd type="vector" x="0.41546532511711" y="0.34578236937523" z="0"/>
			<PosLocal type="vector" x="0.13321769237518" y="-0.16770014166832" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp1052"/>
				<Element index="2" type="string" value="MyApp1053"/>
				<Element index="3" type="string" value="MyApp1054"/>
				<Element index="4" type="string" value="MyApp1055"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="MyApp1051"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="7">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-75.096509553124"/>
			<AngleLocal type="number" value="-79.************"/>
			<Lenght type="number" value="0.049917727708817"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_07_R"/>
			<ParentCut type="boolean" value="true"/>
			<ParentUID type="string" value="D782D779D776D736D647MyApp576"/>
			<Pos type="vector" x="0.49648368358612" y="0.22964343428612" z="0"/>
			<PosEnd type="vector" x="0.50932210683823" y="0.27788195014" z="0"/>
			<PosLocal type="vector" x="0.18498855829239" y="-0.18514269590378" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp1085"/>
				<Element index="2" type="string" value="MyApp1086"/>
				<Element index="3" type="string" value="MyApp1087"/>
				<Element index="4" type="string" value="MyApp1088"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="MyApp1084"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A16.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Cloak04_R"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D782D779D776D736D647MyApp579"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D782D779D776D736D647MyApp576"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-0.024676652625203" y="0.012825198471546" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.085148975253105" y="-0.99636822938919" z="0"/>
			<PosUV type="vector" x="0.016946699470282" y="0.44356995820999" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="D782D779D776D736D647MyApp578"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D782D779D776D736D647MyApp578"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D782D779D776D736D647MyApp576"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="-0.084543704986572" y="-0.1681517213583" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.085148975253105" y="0.99636822938919" z="0"/>
			<PosUV type="vector" x="0.014251108281314" y="0.6254369020462" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="D782D779D776D736D647MyApp579"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D782D779D776D736D647MyApp581"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D782D779D776D736D647MyApp576"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.0052631106227636" y="0.99998617172241" z="0"/>
					<Pos type="vector" x="1.0959086418152" y="0.015419788658619" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.079903803765774" y="-0.99680262804031" z="0"/>
			<PosUV type="vector" x="0.35562434792519" y="0.41202273964882" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="D782D779D776D736D647MyApp580"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D782D779D776D736D647MyApp580"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D782D779D776D736D647MyApp576"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="0.91595256328583" y="-0.17189729213715" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.085148975253105" y="0.99636822938919" z="0"/>
			<PosUV type="vector" x="0.31715008616447" y="0.60331058502197" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="D782D779D776D736D647MyApp581"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="5">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp835"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp793"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.38941872119904" y="0.92106086015701" z="0"/>
					<Pos type="vector" x="-0.04923465102911" y="0.011197591200471" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99680858850479" y="-0.079828262329102" z="0"/>
			<PosUV type="vector" x="0.45153367519379" y="0.21176610887051" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="MyApp834"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="6">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp834"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp793"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.20897404849529" y="-0.9779212474823" z="0"/>
					<Pos type="vector" x="-0.041675060987473" y="-0.012198909185827" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.86249709129333" y="-0.50606197118759" z="0"/>
			<PosUV type="vector" x="0.42908829450607" y="0.20512008666992" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="MyApp835"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="7">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp837"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp793"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.29552057385445" y="0.95533645153046" z="0"/>
					<Pos type="vector" x="0.71352404356003" y="0.050705567002296" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99979829788208" y="0.020085394382477" z="0"/>
			<PosUV type="vector" x="0.4650981426239" y="0.29640790820122" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="MyApp836"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="8">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp836"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp793"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.01052547711879" y="-0.99994468688965" z="0"/>
					<Pos type="vector" x="1.1286319494247" y="-0.010293337516487" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.94584363698959" y="-0.32462269067764" z="0"/>
			<PosUV type="vector" x="0.39417061209679" y="0.31651306152344" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="MyApp837"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="9">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp839"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D793MyApp793"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-0.095507629215717" y="0.015595585107803" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99261629581451" y="-0.12129643559456" z="0"/>
			<PosUV type="vector" x="0.38380762934685" y="0.21406443417072" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="MyApp838"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="10">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp838"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D793MyApp793"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.3105643093586" y="-0.95055246353149" z="0"/>
					<Pos type="vector" x="-0.02184921503067" y="-0.015535838901997" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.98120415210724" y="-0.19297257065773" z="0"/>
			<PosUV type="vector" x="0.35388281941414" y="0.22583369910717" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="MyApp839"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="11">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp841"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D793MyApp793"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="1.0722773075104" y="0.013069726526737" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99261629581451" y="-0.12129643559456" z="0"/>
			<PosUV type="vector" x="0.3967858850956" y="0.34109443426132" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="MyApp840"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="12">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp840"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D793MyApp793"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.10506927967072" y="-0.99446493387222" z="0"/>
					<Pos type="vector" x="1.2294598817825" y="-0.085780218243599" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.99986666440964" y="0.016331568360329" z="0"/>
			<PosUV type="vector" x="0.30075013637543" y="0.37014144659042" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="MyApp841"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="13">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp1000"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp998"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.3894185423851" y="0.92106086015701" z="0"/>
					<Pos type="vector" x="0.55520623922348" y="0.0068428656086326" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.9999977350235" y="-0.0021731853485107" z="0"/>
			<PosUV type="vector" x="0.40026408433914" y="0.34470874071121" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="MyApp999"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="14">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp999"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp998"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="0.56311875581741" y="-0.0048883995041251" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.92190510034561" y="-0.3874160349369" z="0"/>
			<PosUV type="vector" x="0.38899555802345" y="0.34124282002449" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="MyApp1000"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="15">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp1002"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp998"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.38456523418427" y="0.92309778928757" z="0"/>
					<Pos type="vector" x="1.0450905561447" y="0.040001630783081" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99999535083771" y="0.0030902326107025" z="0"/>
			<PosUV type="vector" x="0.40276122093201" y="0.42435604333878" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="MyApp1001"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="16">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp1001"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp998"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="1.0845196247101" y="-0.008957558311522" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.92190510034561" y="-0.3874160349369" z="0"/>
			<PosUV type="vector" x="0.35536608099937" y="0.41076505184174" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="MyApp1002"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="17">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp1024"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp997"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-0.22262968122959" y="0.013021354563534" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.98935908079147" y="0.14549431204796" z="0"/>
			<PosUV type="vector" x="0.52223134040833" y="0.************" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="MyApp1023"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="18">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp1023"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp997"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="-0.036758545786142" y="-0.011557673104107" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.98935908079147" y="-0.14549431204796" z="0"/>
			<PosUV type="vector" x="0.49695912003517" y="0.20850744843483" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="MyApp1024"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="19">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp1026"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp997"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="0.93679541349411" y="0.020964438095689" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.98935908079147" y="0.14549431204796" z="0"/>
			<PosUV type="vector" x="0.52413445711136" y="0.24724397063255" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="MyApp1025"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="20">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp1025"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp997"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="1.2177263498306" y="-0.0084525644779205" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.98935908079147" y="-0.14549431204796" z="0"/>
			<PosUV type="vector" x="0.49358746409416" y="0.25277644395828" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="MyApp1026"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="21">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp1053"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp1051"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.47942554950714" y="0.87758260965347" z="0"/>
					<Pos type="vector" x="0.46405437588692" y="0.028304323554039" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.98324555158615" y="-0.18228653073311" z="0"/>
			<PosUV type="vector" x="0.45893394947052" y="0.30402967333794" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="MyApp1052"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="22">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp1052"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp1051"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.01578894071281" y="-0.99987536668777" z="0"/>
					<Pos type="vector" x="0.55879455804825" y="-0.0023772167041898" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.94523638486862" y="-0.32638645172119" z="0"/>
			<PosUV type="vector" x="0.42684870958328" y="0.30341365933418" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="MyApp1053"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="23">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp1055"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp1051"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="1.0326467752457" y="0.056443464010954" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.95027184486389" y="0.31142148375511" z="0"/>
			<PosUV type="vector" x="0.46809250116348" y="0.36644035577774" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="MyApp1054"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="24">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp1054"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp1051"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="1.1174182891846" y="-0.0067582759074867" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.95027184486389" y="-0.31142148375511" z="0"/>
			<PosUV type="vector" x="0.40541246533394" y="0.************" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="MyApp1055"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="25">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp1086"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp1084"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="0.41124913096428" y="0.0098529830574989" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.96636039018631" y="-0.25719165802002" z="0"/>
			<PosUV type="vector" x="0.51128500699997" y="0.24694737792015" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="MyApp1085"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="26">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp1085"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp1084"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="0.43000355362892" y="-0.01252182200551" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.96636039018631" y="0.25719165802002" z="0"/>
			<PosUV type="vector" x="0.48990365862846" y="0.25360667705536" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="MyApp1086"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="27">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp1088"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp1084"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="1.1570584774017" y="0.010390882380307" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.96636039018631" y="-0.25719165802002" z="0"/>
			<PosUV type="vector" x="0.52137982845306" y="0.28278577327728" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="MyApp1087"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="28">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp1087"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp1084"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="1.079650759697" y="-0.019169576466084" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.96636039018631" y="0.25719165802002" z="0"/>
			<PosUV type="vector" x="0.49181997776031" y="0.28665444254875" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A16"/>
			<UID type="string" value="MyApp1088"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A16"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="294.5" y="0" z="0"/>
	<zoomSize type="vector" x="1121" y="1121" z="1"/>
</root>
