<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="90"/>
			<AngleLocal type="number" value="90"/>
			<Lenght type="number" value="0.015382617712021"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Glasses"/>
			<Pos type="vector" x="0.86615145206451" y="0.35990732908249" z="0"/>
			<PosEnd type="vector" x="0.86615145206451" y="0.34452471137047" z="0"/>
			<PosLocal type="vector" x="0.86615145206451" y="0.35990732908249" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp480"/>
				<Element index="2" type="string" value="MyApp481"/>
				<Element index="3" type="string" value="MyApp482"/>
				<Element index="4" type="string" value="MyApp483"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A8"/>
			<UID type="string" value="MyApp479"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A8.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Glasses01"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp481"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp479"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-0.84615468978882" y="0.024263737723231" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.99999994039536" y="0" z="0"/>
			<PosUV type="vector" x="0.84188771247864" y="0.37292340397835" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A8"/>
			<UID type="string" value="MyApp480"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp480"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp479"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="-0.94230794906616" y="-0.02368432097137" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99999994039536" y="0" z="0"/>
			<PosUV type="vector" x="0.88983577489853" y="0.37440249323845" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A8"/>
			<UID type="string" value="MyApp481"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp483"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp479"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="0.88461601734161" y="0.023967919871211" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.99999994039536" y="0" z="0"/>
			<PosUV type="vector" x="0.84218353033066" y="0.34629961848259" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A8"/>
			<UID type="string" value="MyApp482"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp482"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp479"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="0.95397889614105" y="-0.023378370329738" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99999994039536" y="0" z="0"/>
			<PosUV type="vector" x="0.8895298242569" y="0.34523263573647" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A8"/>
			<UID type="string" value="MyApp483"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A8"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="86.************" y="-248.6328125" z="0"/>
	<zoomSize type="vector" x="1767.5545654297" y="1767.5549316406" z="1.5866739749908"/>
</root>
