component = 
{
    NAME="FxBankComponent_Template",
    FxBankComponent_Template=
    {
        matTableList =
        {
            {
                MatTable=
                {
                    name="FX_ground",
                    matFxList = 
                    {
                        {
                            MatFx=
                            {
                                material="__default__",
                                fx="FX_ground_rock"
                            }
                        },
                        {
                            MatFx=
                            {
                                material="GameMaterial/Ground_Grass.gmt",
                                fx="FX_ground_grass"
                            }
                        },
                        {
                            MatFx=
                            {
                                material="GameMaterial/Wall_Rock.gmt",
                                fx="FX_ground_rock"
                            }
                        },
                    },
                },
            },
        },
        Fx = 
        { 
		}
    }
}
includeReference("Actor/Enemy/Common/FX/FX_hit_weak.ilu")
includeReference("Actor/Enemy/Common/FX/FX_explosion.ilu")

appendTable(params.Actor_Template.COMPONENTS,{component})
