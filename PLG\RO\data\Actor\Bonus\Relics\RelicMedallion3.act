includeReference("Actor/Includes/gameplay_types.ilu")

params =
{
    
NAME = "Actor_Template",
Actor_Template =
    {
        SCALE = vector2dNew(1.0,1.0),
        COMPONENTS =
        {
            {
                NAME="Ray_FixedAIComponent_Template",
                Ray_FixedAIComponent_Template=
                {
					genericBehavior =
						{NAME="Ray_AIRelicMedallionBehavior_Template",
						Ray_AIRelicMedallionBehavior_Template =
						{
							maxRelicCount = 3,
						}}
                }
            },
		
		
            {
                NAME="AnimatedComponent_Template",
                AnimatedComponent_Template =
                {
					animationPath = "actor/bonus/relics/animation/",
					draw2D = 1,
					renderRank = 1,
					relativeWidth = 0.1,      -- % of screen width
					defaultAnimation = "Appear",
					
                    animSet=
                    {
						SubAnimSet_Template =
                        {
							animations=
							{ 
								{SubAnim_Template={friendlyName="Appear",name="Stand.anm",loop=0}},
								{SubAnim_Template={friendlyName="Idle",name="Stand.anm",loop=0, markerStart="MRK_Stand_Stop", markerStop="MRK_Stand_Stop"}},

								{SubAnim_Template={friendlyName="add_Arrival_Rock_01",name="add_Arrival_Rock_01.anm",loop=0}},
								{SubAnim_Template={friendlyName="add_Arrival_Rock_02",name="add_Arrival_Rock_02.anm",loop=0}},
								{SubAnim_Template={friendlyName="add_Arrival_Rock_03",name="add_Arrival_Rock_03.anm",loop=0}},

								{SubAnim_Template={friendlyName="add_Rock_01",name="add_Rock_01.anm",loop=0}},
								{SubAnim_Template={friendlyName="add_Rock_02",name="add_Rock_02.anm",loop=0}},
								{SubAnim_Template={friendlyName="add_Rock_03",name="add_Rock_03.anm",loop=0}},
							},
						},
					},
					
			        inputs =
					{
						{InputDesc={name="medallionAppears", varType=AnimInputTypes.float}},

						{InputDesc={name="r1Presence", varType=AnimInputTypes.float}},
						{InputDesc={name="r2Presence", varType=AnimInputTypes.float}},
						{InputDesc={name="r3Presence", varType=AnimInputTypes.float}},
						{InputDesc={name="r4Presence", varType=AnimInputTypes.float}},
						{InputDesc={name="r5Presence", varType=AnimInputTypes.float}},
						
						{InputDesc={name="r1Appears", varType=AnimInputTypes.float}},
						{InputDesc={name="r2Appears", varType=AnimInputTypes.float}},
						{InputDesc={name="r3Appears", varType=AnimInputTypes.float}},
						{InputDesc={name="r4Appears", varType=AnimInputTypes.float}},
						{InputDesc={name="r5Appears", varType=AnimInputTypes.float}},
					},
										
					tree = 
					{
						AnimTree_Template =
						{
							nodes =
							{													
								--Idle
								{NAME="AnimTreeNodePlayAnim_Template",
								AnimTreeNodePlayAnim_Template =
								{
									nodeName = "Idle",
									animationName = "Idle",
								}},
							
								--AppearAndStand
								{
									NAME="BlendTreeNodeAddBranch_Template",
									BlendTreeNodeAddBranch_Template =
									{
										nodeName = "Appear",
										leafs =
										{
											--medallion appears
											{NAME="AnimTreeNodePlayAnim_Template",
											AnimTreeNodePlayAnim_Template =
											{
												animationName = "Appear",
											}},
											--r1
											{NAME="AnimTreeNodePlayAnim_Template",
											AnimTreeNodePlayAnim_Template =
											{
												animationName = "add_Rock_01",
											}},
											--r2
											{NAME="AnimTreeNodePlayAnim_Template",
											AnimTreeNodePlayAnim_Template =
											{
												animationName = "add_Rock_02",
											}},
											--r3
											{NAME="AnimTreeNodePlayAnim_Template",
											AnimTreeNodePlayAnim_Template =
											{
												animationName = "add_Rock_03",
											}},


											--r1
											{NAME="AnimTreeNodePlayAnim_Template",
											AnimTreeNodePlayAnim_Template =
											{
												animationName = "add_Arrival_Rock_01",
											}},
											--r2
											{NAME="AnimTreeNodePlayAnim_Template",
											AnimTreeNodePlayAnim_Template =
											{
												animationName = "add_Arrival_Rock_02",
											}},
											--r3
											{NAME="AnimTreeNodePlayAnim_Template",
											AnimTreeNodePlayAnim_Template =
											{
												animationName = "add_Arrival_Rock_03",
											}},

											
										},
										
										weights =
										{
											--{BlendTreeBranchWeight={}},
											{BlendTreeBranchWeight={proceduralWeight = {ProceduralInputData={input="medallionAppears", min=0, max=1}}}},
											
											{BlendTreeBranchWeight={proceduralWeight = {ProceduralInputData={input="r1Presence", min=0, max=1}}}},
											{BlendTreeBranchWeight={proceduralWeight = {ProceduralInputData={input="r2Presence", min=0, max=1}}}},
											{BlendTreeBranchWeight={proceduralWeight = {ProceduralInputData={input="r3Presence", min=0, max=1}}}},
											
											{BlendTreeBranchWeight={proceduralWeight = {ProceduralInputData={input="r1Appears", min=0, max=1}}}},
											{BlendTreeBranchWeight={proceduralWeight = {ProceduralInputData={input="r2Appears", min=0, max=1}}}},
											{BlendTreeBranchWeight={proceduralWeight = {ProceduralInputData={input="r3Appears", min=0, max=1}}}},											
										}
									}
								}
								
								--
							}
						}
					}					

                }
            },
        }
    }
}
