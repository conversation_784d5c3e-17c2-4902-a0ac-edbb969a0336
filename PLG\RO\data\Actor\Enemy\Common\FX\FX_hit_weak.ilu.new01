fx =
{ 
	{
		FxDescriptor=
		{
			name = "FX_hit_weak_01",
			texture	= "FX/Common/shockwave_atlas.tga", 
			gen=
			{
				ITF_ParticleGenerator =
				{
					useAnim = 1, 
					useuvrandom = 0,
					animstart = 0,
					animend =  8,
					AnimUVfreq = 0, 
					params =
					{
						ParticleGeneratorParameters =
						{
							maxParticles         = 1, 
							emitParticlesCount   = 1,  
							velNorm              = 0,
							grav                 = vectorNew(0.0,0.0,0.0),
							acc                  = vectorNew(0.0,0.0,0.0), 
							velocityVar          = 0.0,
							friction             = 1.0,
							freq                 = 0.001,  
							emitInterval         = 1, 
							initAngle            = 360,
							angleDelta           = 360,
							angularSpeed         = 0, 
							angularSpeedDelta    = 0,
							timeTarget           = 0.0,
							startTime            = 1.0,
							stopTime             = 1.00,
							pivot               = vector2dNew(0.0,0.0), 
							uniformscale = 1,
							genBox               =
							{
								AABB =
								{
									MIN = vector2dNew(-0.0,-0.0), 
									MAX = vector2dNew(0.5,0.5),
								}
							},
							boundingBox          =
							{
								AABB =
								{
									MIN = vector2dNew(0.0,-1.0),
									MAX = vector2dNew(0.5,0.5),
								}
							},
							nbPhase              = 2,
							phases =
							{
								{
									ParPhase =
									{
										phaseTime   = 0.1,
										animstart = 6,
										animend =  6,
										colorMin    = "0xffffffff",
										colorMax    = "0xffffffff",
										sizeMin     = vector2dNew(1,1), 
										sizeMax     = vector2dNew(1,1), 
									}
								},
								{
									ParPhase =
									{
										phaseTime   = 0.0,
										animstart = 6,
										animend =  6,
--										Blendtonextphase = 0,
										colorMin    = "0x00ff9000",
										colorMax    = "0x00ff9000",
										sizeMin     = vector2dNew(4.0,4.0),
										sizeMax     = vector2dNew(4.0,4.0),  
									}
								},

								-- {
									-- ParPhase =
									-- {
										-- phaseTime   = 0.05, 
										-- animstart = 5,
										-- animend =  5,
										-- colorMin    = "0xffffffff",
										-- colorMax    = "0xffffffff",
										-- sizeMin     = vector2dNew(1.2,1.2),
										-- sizeMax     = vector2dNew(1.2,1.2), 
									-- }
								-- },
								-- {
									-- ParPhase =
									-- {
										-- phaseTime   = 0.0,
										-- animstart = 5,
										-- animend =  5,
										-- Blendtonextphase = 0,
										-- colorMin    = "0xffffffff",
										-- colorMax    = "0xffffffff",
										-- sizeMin     = vector2dNew(1.4,1.4), 
										-- sizeMax     = vector2dNew(1.4,1.4), 
									-- }
								-- },

								-- {
									-- ParPhase =
									-- {
										-- phaseTime   = 0.05, 
										-- animstart = 6,
										-- animend =  6,  
										-- colorMin    = "0xffffffff",
										-- colorMax    = "0xffffffff",  
										-- sizeMin     = vector2dNew(1.4,1.4),
										-- sizeMax     = vector2dNew(1.4,1.4),   
									-- }
								-- },
								-- {
									-- ParPhase =
									-- {
										-- phaseTime   = 0.0, 
										-- animstart = 6,
										-- animend =  6,  
										-- Blendtonextphase = 0,
										-- colorMin    = "0xffffffff",
										-- colorMax    = "0xffffffff", 
										-- sizeMin     = vector2dNew(1.6,1.6),
										-- sizeMax     = vector2dNew(1.6,1.6), 
									-- }
								-- },
								-- {
									-- ParPhase =
									-- {
										-- phaseTime   = 0.0, 
										-- animstart = 8,
										-- animend =  8,  
										-- Blendtonextphase = 0,
										-- colorMin    = "0xffffffff",
										-- colorMax    = "0xffffffff",
										-- sizeMin     = vector2dNew(1.6,1.6),
										-- sizeMax     = vector2dNew(1.6,1.6), 
									-- }
								-- },

							},
							renderPrio           = 1,
							--GFX_BLEND_UNKNOWN = 0,,
							--GFX_BLEND_COPY = 1,
							--GFX_BLEND_ALPHA = 2,
							--GFX_BLEND_ALPHAPREMULT = 3,
							--GFX_BLEND_ALPHADEST = 4,
							--GFX_BLEND_ALPHADESTPREMULT = 5,
							--GFX_BLEND_ADD = 6,
							--GFX_BLEND_ADDALPHA = 7,
							--GFX_BLEND_SUBALPHA = 8,
							--GFX_BLEND_SUB = 9,
							--GFX_BLEND_MUL = 10 ,
							--GFX_BLEND_ALPHAMUL = 11,
							--GFX_BLEND_IALPHAMUL = 12 ,
							--GFX_BLEND_IALPHA = 13,
							--GFX_BLEND_IALPHAPREMULT = 14,
							--GFX_BLEND_IALPHADEST = 15,
							--GFX_BLEND_IALPHADESTPREMULT = 16,
							--GFX_BLEND_MUL2X = 17,
							blendMode          = 7,
							--PARGEN_GEN_POINTS = 0
							--PARGEN_GEN_RECTANGLE = 1
							--PARGEN_GEN_CIRCLE = 2
							genGenType         = 0,
							--PARGEN_MODE_FOLLOW  = 0
							--PARGEN_MODE_COMPLEX = 1
							genMode            = 1,
						}
					},
				}
			},
		}
	},
	{
		FxDescriptor=
		{
			name = "FX_hit_weak_02",
			texture	= "FX/Common/shockwave_atlas.tga", 
			gen=
			{
				ITF_ParticleGenerator =
				{
					useAnim = 0, 
					useuvrandom = 0,
					animstart = 0,
					animend =  8,
					AnimUVfreq = 0,
					params =
					{
						ParticleGeneratorParameters =
						{
							maxParticles         = 1,
							emitParticlesCount   = 1,  
							velNorm              = 0,
							grav                 = vectorNew(0.0,0.0,0.0),
							acc                  = vectorNew(0.0,0.0,0.0),   
							velocityVar          = 0.0,
							friction             = 1.0,
							freq                 = 0.01,      
							emitInterval         = 1,
							initAngle            = 360,
							angleDelta           = 360,
							angularSpeed         = 0, 
							angularSpeedDelta    = 0,
							timeTarget           = 0.0,
							startTime            = 1.0,
							stopTime             = 1.00,
							uniformscale = 1,
							genBox               =
							{
								AABB =
								{
									MIN = vector2dNew(-0.0,-0.0), 
									MAX = vector2dNew(0.5,0.5),
								}
							},
							boundingBox          =
							{
								AABB =
								{
									MIN = vector2dNew(0.0,-1.0),   
									MAX = vector2dNew(0.5,0.5),  
								}
							}, 
							nbPhase              = 2,
							phases =
							{
								{
									ParPhase =
									{
										phaseTime   = 0.1,     
										animstart = 5,
										animend =  5,

										colorMin    = "0xffff9000",
										colorMax    = "0xffff9000",
										sizeMin     = vector2dNew(0.5,0.5),
										sizeMax     = vector2dNew(0.5,0.5),     
									}
								},
								{
									ParPhase =
									{
										phaseTime   = 0.0,   
										animstart = 5,
										animend =  5,

										colorMin    = "0x00ff9000",
										colorMax    = "0x00ff9000",
										sizeMin     = vector2dNew(3,3),
										sizeMax     = vector2dNew(3,3),      
									}
								},

								{
									ParPhase =
									{
										phaseTime   = 0.0, 
										animstart = 5,
										animend =  5,

										colorMin    = "0x00ffffff",
										colorMax    = "0x00ffffff",
										sizeMin     = vector2dNew(4,4),
										sizeMax     = vector2dNew(4,4),      
									}
								},
							},
							renderPrio           = 0,
							--GFX_BLEND_UNKNOWN = 0,,
							--GFX_BLEND_COPY = 1,
							--GFX_BLEND_ALPHA = 2,
							--GFX_BLEND_ALPHAPREMULT = 3,
							--GFX_BLEND_ALPHADEST = 4,
							--GFX_BLEND_ALPHADESTPREMULT = 5,
							--GFX_BLEND_ADD = 6,
							--GFX_BLEND_ADDALPHA = 7,
							--GFX_BLEND_SUBALPHA = 8,
							--GFX_BLEND_SUB = 9,
							--GFX_BLEND_MUL = 10 ,
							--GFX_BLEND_ALPHAMUL = 11,
							--GFX_BLEND_IALPHAMUL = 12 ,
							--GFX_BLEND_IALPHA = 13,
							--GFX_BLEND_IALPHAPREMULT = 14,
							--GFX_BLEND_IALPHADEST = 15,
							--GFX_BLEND_IALPHADESTPREMULT = 16,
							--GFX_BLEND_MUL2X = 17,
							blendMode          = 7,
							--PARGEN_GEN_POINTS = 0
							--PARGEN_GEN_RECTANGLE = 1
							--PARGEN_GEN_CIRCLE = 2
							genGenType         = 0,
							--PARGEN_MODE_FOLLOW  = 0
							--PARGEN_MODE_COMPLEX = 1
							genMode            = 1,
						}
					},
				}
			},
		}
	},
	{
		FxDescriptor=
		{
			name = "FX_hit_weak_03",
			texture	= "FX/Common/Hit_atlas_01.tga", 
			angleOffset = 3.14,
			gen=
			{
				ITF_ParticleGenerator =
				{
					useAnim = 0,
					useuvrandom = 0,
					animstart = 0,
					animend =  8,
					AnimUVfreq = 0,
					
					params =
					{
						ParticleGeneratorParameters =
						{
							maxParticles         = 10,
							emitParticlesCount   = 10,  
							velNorm              = 0.1, 
							grav                 = vectorNew(0.0,0.0,0.0),
							acc                  = vectorNew(0.0,0.0,0.0),   
							velocityVar          = 0.1,
							friction             = 0.75,
							freq                 = 0.001,      
							emitInterval         = 1,
							initAngle            = 0,
							angleDelta           = 0,
							angularSpeed         = 0, 
							angularSpeedDelta    = 0,
							timeTarget           = 0.0,
							startTime            = 1.0,
							stopTime             = 1.00,
							pivot               = vector2dNew(0.0,-0.5), 
							uniformscale = 0,
							orientDir = 2,
							genBox               =
							{
								AABB =
								{
									MIN = vector2dNew(-0.0,-0.0), 
									MAX = vector2dNew(0.5,0.5),
								}
							},
							boundingBox          =
							{
								AABB =
								{
									MIN = vector2dNew(0.0,-1.0),   
									MAX = vector2dNew(0.5,0.5),  
								}
							}, 
							nbPhase              = 2,
							phases =
							{
								{
									ParPhase =
									{
										phaseTime   = 0.1,   
										animstart = 3,
										animend =  3,

										colorMin    = "0xffffffff", 
										colorMax    = "0xffffffff",
										sizeMin     = vector2dNew(0.05,0.0),
										sizeMax     = vector2dNew(0.1,0.0),     
									}
								},
								{
									ParPhase =
									{
										phaseTime   = 0.0, 
										animstart = 3,
										animend =  3,

										colorMin    = "0xffffffff", 
										colorMax    = "0xffffffff",
										sizeMin     = vector2dNew(0.0,0.5),
										sizeMax     = vector2dNew(0.0,1.0),     
									}
								},
							},
							renderPrio           = 1,
							--GFX_BLEND_UNKNOWN = 0,,
							--GFX_BLEND_COPY = 1,
							--GFX_BLEND_ALPHA = 2,
							--GFX_BLEND_ALPHAPREMULT = 3,
							--GFX_BLEND_ALPHADEST = 4,
							--GFX_BLEND_ALPHADESTPREMULT = 5,
							--GFX_BLEND_ADD = 6,
							--GFX_BLEND_ADDALPHA = 7,
							--GFX_BLEND_SUBALPHA = 8,
							--GFX_BLEND_SUB = 9,
							--GFX_BLEND_MUL = 10 ,
							--GFX_BLEND_ALPHAMUL = 11,
							--GFX_BLEND_IALPHAMUL = 12 ,
							--GFX_BLEND_IALPHA = 13,
							--GFX_BLEND_IALPHAPREMULT = 14,
							--GFX_BLEND_IALPHADEST = 15,
							--GFX_BLEND_IALPHADESTPREMULT = 16,
							--GFX_BLEND_MUL2X = 17,
							blendMode          = 2,
							--PARGEN_GEN_POINTS = 0
							--PARGEN_GEN_RECTANGLE = 1
							--PARGEN_GEN_CIRCLE = 2
							genGenType         = 2,
							circleradius         = 1.0,
							innercircleradius         =  0.8,
							-- genangmin = -120,
							-- genangmax = -30,

							--PARGEN_MODE_FOLLOW  = 0
							--PARGEN_MODE_COMPLEX = 1
							genMode            = 1,
						}
					},
				}
			},
		}
	},
	{
		FxDescriptor=
		{
			name = "FX_hit_weak_04a",
			texture	= "FX/Common/Hit_atlas_01.tga", 
			angleOffset = 2.5,
			gen=
			{
				ITF_ParticleGenerator =
				{
					useAnim = 0,
					useuvrandom = 0,
					animstart = 0,
					animend =  8,
					AnimUVfreq = 0,
					
					params =
					{
						ParticleGeneratorParameters =
						{
							maxParticles         = 10,
							emitParticlesCount   = 10,  
							velNorm              = 10, 
							grav                 = vectorNew(0.0,0.0,0.0),
							acc                  = vectorNew(0.0,0.0,0.0),   
							velocityVar          = 20.0,
							friction             = 0.75,
							freq                 = 0.001,      
							emitInterval         = 1,
							initAngle            = 360,
							angleDelta           = 360,
							angularSpeed         = -180, 
							angularSpeedDelta    = 0,
							timeTarget           = 0.0,
							startTime            = 1.0,
							stopTime             = 1.00,
							uniformscale = 1,
							orienttodir = 0,
							genBox               =
							{
								AABB =
								{
									MIN = vector2dNew(-0.0,-0.0), 
									MAX = vector2dNew(0.5,0.5),
								}
							},
							boundingBox          =
							{
								AABB =
								{
									MIN = vector2dNew(0.0,-1.0),   
									MAX = vector2dNew(0.5,0.5),  
								}
							}, 
							nbPhase              = 2,
							phases =
							{
								{
									ParPhase =
									{
										phaseTime   = 0.2,   
										animstart = 3,
										animend =  3,

										colorMin    = "0xffffffff", 
										colorMax    = "0xffffffff",
										sizeMin     = vector2dNew(0.25,0.25),
										sizeMax     = vector2dNew(0.5,0.5),     
									}
								},
								{
									ParPhase =
									{
										phaseTime   = 0.0, 
										animstart = 3,
										animend =  3,

										colorMin    = "0xffffffff", 
										colorMax    = "0xffffffff",
										sizeMin     = vector2dNew(0.0,0.0),
										sizeMax     = vector2dNew(0.0,0.0),     
									}
								},
							},
							renderPrio           = 1,
							--GFX_BLEND_UNKNOWN = 0,,
							--GFX_BLEND_COPY = 1,
							--GFX_BLEND_ALPHA = 2,
							--GFX_BLEND_ALPHAPREMULT = 3,
							--GFX_BLEND_ALPHADEST = 4,
							--GFX_BLEND_ALPHADESTPREMULT = 5,
							--GFX_BLEND_ADD = 6,
							--GFX_BLEND_ADDALPHA = 7,
							--GFX_BLEND_SUBALPHA = 8,
							--GFX_BLEND_SUB = 9,
							--GFX_BLEND_MUL = 10 ,
							--GFX_BLEND_ALPHAMUL = 11,
							--GFX_BLEND_IALPHAMUL = 12 ,
							--GFX_BLEND_IALPHA = 13,
							--GFX_BLEND_IALPHAPREMULT = 14,
							--GFX_BLEND_IALPHADEST = 15,
							--GFX_BLEND_IALPHADESTPREMULT = 16,
							--GFX_BLEND_MUL2X = 17,
							blendMode          = 2,
							--PARGEN_GEN_POINTS = 0
							--PARGEN_GEN_RECTANGLE = 1
							--PARGEN_GEN_CIRCLE = 2
							genGenType         = 0,
							--PARGEN_MODE_FOLLOW  = 0
							--PARGEN_MODE_COMPLEX = 1
							genMode            = 1,
						}
					},
				}
			},
		}
	},

}
appendTable(component.FxBankComponent.Fx,fx)
