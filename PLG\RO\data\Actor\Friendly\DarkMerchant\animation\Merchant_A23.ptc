<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="89.************"/>
			<AngleLocal type="number" value="89.************"/>
			<Lenght type="number" value="0.053428821265697"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Pelvis"/>
			<Pos type="vector" x="0.45432418584824" y="0.68486738204956" z="0"/>
			<PosEnd type="vector" x="0.45465195178986" y="0.63143956661224" z="0"/>
			<PosLocal type="vector" x="0.45432418584824" y="0.68486738204956" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="D2007MyApp385"/>
				<Element index="2" type="string" value="D2007MyApp386"/>
				<Element index="3" type="string" value="D2007MyApp387"/>
				<Element index="4" type="string" value="D2007MyApp388"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A23"/>
			<UID type="string" value="D2007MyApp383"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="2">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="88.************"/>
			<AngleLocal type="number" value="-0.7342559040932"/>
			<Lenght type="number" value="0.067550905048847"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Spine"/>
			<ParentUID type="string" value="D2007MyApp383"/>
			<Pos type="vector" x="0.45465195178986" y="0.63143956661224" z="0"/>
			<PosEnd type="vector" x="0.45593196153641" y="0.56390076875687" z="0"/>
			<PosLocal type="vector" x="0" y="0" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="D2007MyApp389"/>
				<Element index="2" type="string" value="D2007MyApp390"/>
				<Element index="3" type="string" value="MyApp2079"/>
				<Element index="4" type="string" value="MyApp2078"/>
				<Element index="5" type="string" value="MyApp2073"/>
				<Element index="6" type="string" value="MyApp2072"/>
				<Element index="7" type="string" value="D2007MyApp391"/>
				<Element index="8" type="string" value="D2007MyApp392"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A23"/>
			<UID type="string" value="D2007MyApp384"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="3">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-173.27895837524"/>
			<AngleLocal type="number" value="-173.27895837524"/>
			<Lenght type="number" value="0.073308818042278"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Foot_R"/>
			<Pos type="vector" x="0.33749091625214" y="0.63970422744751" z="0"/>
			<PosEnd type="vector" x="0.26468589901924" y="0.64828395843506" z="0"/>
			<PosLocal type="vector" x="0.33749091625214" y="0.63970422744751" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp2086"/>
				<Element index="2" type="string" value="MyApp2087"/>
				<Element index="3" type="string" value="MyApp2088"/>
				<Element index="4" type="string" value="MyApp2089"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A23"/>
			<UID type="string" value="MyApp2085"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A23.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Body05"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2007MyApp386"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2007MyApp383"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-0.31931689381599" y="0.056198853999376" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.99998116493225" y="-0.0061346278525889" z="0"/>
			<PosUV type="vector" x="0.39802172780037" y="0.70158302783966" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A23"/>
			<UID type="string" value="D2007MyApp385"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2007MyApp385"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2007MyApp383"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="-0.18716812133789" y="-0.087465278804302" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99998116493225" y="0.0061346278525889" z="0"/>
			<PosUV type="vector" x="0.54172646999359" y="0.69540393352509" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A23"/>
			<UID type="string" value="D2007MyApp386"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2007MyApp388"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2007MyApp383"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="0.027662316337228" y="0.056745201349258" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.99998116493225" y="-0.0061346278525889" z="0"/>
			<PosUV type="vector" x="0.39758911728859" y="0.68304133415222" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A23"/>
			<UID type="string" value="D2007MyApp387"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2007MyApp387"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2007MyApp383"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="0.14923971891403" y="-0.085074253380299" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99998116493225" y="0.0061346278525889" z="0"/>
			<PosUV type="vector" x="0.53944575786591" y="0.67741572856903" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A23"/>
			<UID type="string" value="D2007MyApp388"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="5">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2007MyApp390"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2007MyApp384"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-0.17025493085384" y="0.067488260567188" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.99982047080994" y="-0.018948812037706" z="0"/>
			<PosUV type="vector" x="0.38695788383484" y="0.64165955781937" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A23"/>
			<UID type="string" value="D2007MyApp389"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="6">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2007MyApp389"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2007MyApp384"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="-0.11030923575163" y="-0.081992737948895" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99982047080994" y="0.018948812037706" z="0"/>
			<PosUV type="vector" x="0.5364887714386" y="0.64044338464737" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A23"/>
			<UID type="string" value="D2007MyApp390"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="7">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2007MyApp392"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2007MyApp384"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="3.6725521087646" y="0.035459510982037" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.99982047080994" y="-0.018948812037706" z="0"/>
			<PosUV type="vector" x="0.42389971017838" y="0.38272789120674" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A23"/>
			<UID type="string" value="D2007MyApp391"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="8">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2007MyApp391"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2007MyApp384"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="3.8875935077667" y="-0.035809565335512" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99982047080994" y="0.018948812037706" z="0"/>
			<PosUV type="vector" x="0.49543124437332" y="0.36955472826958" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A23"/>
			<UID type="string" value="D2007MyApp392"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="9">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2073"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2007MyApp384"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.91880983114243" y="-0.3947007060051" z="0"/>
					<Pos type="vector" x="1.7445095777512" y="-0.068511784076691" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.41204020380974" y="-0.91116577386856" z="0"/>
			<PosUV type="vector" x="0.52538442611694" y="0.51491570472717" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A23"/>
			<UID type="string" value="MyApp2072"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="10">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2072"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2007MyApp384"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.37181776762009" y="0.92830580472946" z="0"/>
					<Pos type="vector" x="1.5409640073776" y="0.058629024773836" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.92109364271164" y="-0.38934129476547" z="0"/>
			<PosUV type="vector" x="0.39800587296486" y="0.52625375986099" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A23"/>
			<UID type="string" value="MyApp2073"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="11">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2079"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2007MyApp384"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.35774344205856" y="-0.93381994962692" z="0"/>
					<Pos type="vector" x="1.4171382188797" y="-0.15063391625881" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.92687344551086" y="0.37537398934364" z="0"/>
			<PosUV type="vector" x="0.6070728302002" y="0.53858208656311" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A23"/>
			<UID type="string" value="MyApp2078"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="12">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2078"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2007MyApp384"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.053921192884445" y="0.99854516983032" z="0"/>
					<Pos type="vector" x="1.0099662542343" y="0.14407646656036" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.9973441362381" y="-0.072832755744457" z="0"/>
			<PosUV type="vector" x="0.31189405918121" y="0.56049758195877" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A23"/>
			<UID type="string" value="MyApp2079"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="13">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2087"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2085"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.10506887733936" y="0.99446493387222" z="0"/>
					<Pos type="vector" x="-0.65680032968521" y="0.055612348020077" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.012040853500366" y="0.99992740154266" z="0"/>
			<PosUV type="vector" x="0.39181789755821" y="0.68929922580719" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A23"/>
			<UID type="string" value="MyApp2086"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="14">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2086"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2085"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.************" y="-0.91265916824341" z="0"/>
					<Pos type="vector" x="-0.52562063932419" y="-0.006768190767616" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.29909899830818" y="-0.9542219042778" z="0"/>
			<PosUV type="vector" x="0.37496662139893" y="0.62847286462784" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A23"/>
			<UID type="string" value="MyApp2087"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="15">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2089"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2085"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.11030143499374" y="0.99389815330505" z="0"/>
					<Pos type="vector" x="1.************" y="0.038220174610615" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.0067779198288918" y="0.99997693300247" z="0"/>
			<PosUV type="vector" x="0.24633106589317" y="0.68893164396286" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A23"/>
			<UID type="string" value="MyApp2088"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="16">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2088"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2085"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.************" y="-0.91265916824341" z="0"/>
					<Pos type="vector" x="0.96917587518692" y="-0.05189348384738" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.29909899830818" y="-0.9542219042778" z="0"/>
			<PosUV type="vector" x="0.26085665822029" y="0.59648263454437" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A23"/>
			<UID type="string" value="MyApp2089"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A23"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="9.0292358398438" y="-456.48278808594" z="0"/>
	<zoomSize type="vector" x="1772.15234375" y="1772.1522216797" z="1.7580876350403"/>
</root>
