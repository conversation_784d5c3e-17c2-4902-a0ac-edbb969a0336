<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-0"/>
			<AngleLocal type="number" value="-0"/>
			<Lenght type="number" value="0.039626240730286"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="Root"/>
			<Pos type="vector" x="0.81885188817978" y="0.98621904850006" z="-0"/>
			<PosEnd type="vector" x="0.85847812891006" y="0.98621904850006" z="-0"/>
			<PosLocal type="vector" x="0.81885188817978" y="0.98621904850006" z="-0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp2426"/>
				<Element index="2" type="string" value="MyApp2427"/>
				<Element index="3" type="string" value="MyApp5158"/>
				<Element index="4" type="string" value="MyApp5157"/>
				<Element index="5" type="string" value="MyApp5162"/>
				<Element index="6" type="string" value="MyApp5161"/>
				<Element index="7" type="string" value="MyApp2428"/>
				<Element index="8" type="string" value="MyApp2429"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A29"/>
			<UID type="string" value="MyApp2425"/>
			<Zorder type="number" value="-0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="2">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-90"/>
			<AngleLocal type="number" value="-90"/>
			<Lenght type="number" value="0.024053573608398"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_RockBack"/>
			<Pos type="vector" x="0.94159597158432" y="0.69767266511917" z="-0"/>
			<PosEnd type="vector" x="0.94159597158432" y="0.72172623872757" z="-0"/>
			<PosLocal type="vector" x="0.94159597158432" y="0.69767266511917" z="-0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp5167"/>
				<Element index="2" type="string" value="MyApp5168"/>
				<Element index="3" type="string" value="MyApp5169"/>
				<Element index="4" type="string" value="MyApp5170"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A29"/>
			<UID type="string" value="MyApp5166"/>
			<Zorder type="number" value="-0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A29.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Rock"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="-0"/>
			<BrotherUID type="string" value="MyApp2427"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="-0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2425"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.99471181631088" y="-0.10270638763905" z="-0"/>
					<Pos type="vector" x="-4.5330438613892" y="-0.014717817306519" z="-0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.99471181631088" y="0.10270638763905" z="-0"/>
			<PosUV type="vector" x="0.63922441005707" y="1.0009368658066" z="-0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A29"/>
			<UID type="string" value="MyApp2426"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="-0"/>
			<BrotherUID type="string" value="MyApp2426"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="-0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2425"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.99991005659103" y="0.013413725420833" z="-0"/>
					<Pos type="vector" x="4.5590839385986" y="-0.01180624961853" z="-0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99991005659103" y="-0.013413725420833" z="-0"/>
			<PosUV type="vector" x="0.99951124191284" y="0.99802529811859" z="-0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A29"/>
			<UID type="string" value="MyApp2427"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="-0"/>
			<BrotherUID type="string" value="MyApp2429"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="-0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2425"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.98063623905182" y="0.19583821296692" z="-0"/>
					<Pos type="vector" x="-1.9811843633652" y="0.27802270650864" z="-0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.98063623905182" y="-0.19583821296692" z="-0"/>
			<PosUV type="vector" x="0.7403450012207" y="0.70819634199142" z="-0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A29"/>
			<UID type="string" value="MyApp2428"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="-0"/>
			<BrotherUID type="string" value="MyApp2428"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="-0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2425"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.86572289466858" y="0.50052374601364" z="-0"/>
					<Pos type="vector" x="1.3713226318359" y="0.27857822179794" z="-0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.86572289466858" y="-0.50052374601364" z="-0"/>
			<PosUV type="vector" x="0.87319225072861" y="0.70764082670212" z="-0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A29"/>
			<UID type="string" value="MyApp2429"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="5">
			<Angle type="number" value="-0"/>
			<BrotherUID type="string" value="MyApp5158"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="-0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2425"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.99421232938766" y="0.10743395239115" z="-0"/>
					<Pos type="vector" x="4.5302324295044" y="0.042856574058533" z="-0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99421232938766" y="-0.10743395239115" z="-0"/>
			<PosUV type="vector" x="0.99836796522141" y="0.94336247444153" z="-0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A29"/>
			<UID type="string" value="MyApp5157"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="6">
			<Angle type="number" value="-0"/>
			<BrotherUID type="string" value="MyApp5157"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="-0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2425"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.************" y="0.54311323165894" z="-0"/>
					<Pos type="vector" x="-4.5350470542908" y="0.042036652565002" z="-0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.************" y="-0.54311323165894" z="-0"/>
			<PosUV type="vector" x="0.63914501667023" y="0.94418239593506" z="-0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A29"/>
			<UID type="string" value="MyApp5158"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="7">
			<Angle type="number" value="-0"/>
			<BrotherUID type="string" value="MyApp5162"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="-0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2425"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.92347645759583" y="0.38365516066551" z="-0"/>
					<Pos type="vector" x="3.1625182628632" y="0.1434553861618" z="-0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.92347645759583" y="-0.38365516066551" z="-0"/>
			<PosUV type="vector" x="0.94417059421539" y="0.84276366233826" z="-0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A29"/>
			<UID type="string" value="MyApp5161"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="8">
			<Angle type="number" value="-0"/>
			<BrotherUID type="string" value="MyApp5161"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="-0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2425"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.97345173358917" y="0.22889249026775" z="-0"/>
					<Pos type="vector" x="-3.0291538238525" y="0.090289533138275" z="-0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.97345173358917" y="-0.22889249026775" z="-0"/>
			<PosUV type="vector" x="0.69881790876389" y="0.89592951536179" z="-0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A29"/>
			<UID type="string" value="MyApp5162"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="9">
			<Angle type="number" value="-0"/>
			<BrotherUID type="string" value="MyApp5168"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="-0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp5166"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.31056699156761" y="0.95055145025253" z="-0"/>
					<Pos type="vector" x="-0.36341041326523" y="0.067705571651459" z="-0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.95055145025253" y="0.31056699156761" z="-0"/>
			<PosUV type="vector" x="1.0093015432358" y="0.68893134593964" z="-0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A29"/>
			<UID type="string" value="MyApp5167"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="10">
			<Angle type="number" value="-0"/>
			<BrotherUID type="string" value="MyApp5167"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="-0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp5166"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.38941836357117" y="-0.92106103897095" z="-0"/>
					<Pos type="vector" x="-0.26303672790527" y="-0.063145220279694" z="-0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.92106103897095" y="0.38941836357117" z="-0"/>
			<PosUV type="vector" x="0.87845075130463" y="0.69134569168091" z="-0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A29"/>
			<UID type="string" value="MyApp5168"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="11">
			<Angle type="number" value="-0"/>
			<BrotherUID type="string" value="MyApp5170"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="-0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp5166"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.31056705117226" y="0.95055150985718" z="-0"/>
					<Pos type="vector" x="3.8839802742004" y="0.034172177314758" z="-0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.95055150985718" y="0.31056705117226" z="-0"/>
			<PosUV type="vector" x="0.97576814889908" y="0.79109627008438" z="-0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A29"/>
			<UID type="string" value="MyApp5169"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="12">
			<Angle type="number" value="-0"/>
			<BrotherUID type="string" value="MyApp5169"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="-0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp5166"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.38456529378891" y="-0.92309784889221" z="-0"/>
					<Pos type="vector" x="3.8616709709167" y="-0.01753956079483" z="-0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.92309784889221" y="0.38456529378891" z="-0"/>
			<PosUV type="vector" x="0.92405641078949" y="0.79055964946747" z="-0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A29"/>
			<UID type="string" value="MyApp5170"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A29"/>
	<ZoomOrigin type="vector" x="-0" y="-0" z="-0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="303" y="-0" z="-0"/>
	<zoomSize type="vector" x="1034" y="1034" z="1"/>
</root>
