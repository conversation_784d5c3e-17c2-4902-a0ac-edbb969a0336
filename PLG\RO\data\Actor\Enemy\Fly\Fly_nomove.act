includeReference("Actor/Includes/helpers.ilu")

params =
{
    
NAME = "Actor_Template",
Actor_Template =
    {
        SCALE = vector2dNew(3.0,3.0),
        RANK = 0,
        COMPONENTS =
        {
        }
    }
}
    
    includeReference("Actor/Enemy/Fly/Components/Fly_AIComponent_nomove.ilu")
    includeReference("Actor/Enemy/Common/Components/ParticlePhysComponent.ilu")
    includeReference("Actor/Enemy/Fly/Components/Fly_AnimatedComponent.ilu")
	includeReference("Actor/Enemy/Fly/Components/Fly_sound.ilu")
	includeReference("Actor/Enemy/Fly/Components/Fly_fxController.ilu")
    includeReference("Actor/Enemy/Common/Components/FxBankComponent.ilu")
