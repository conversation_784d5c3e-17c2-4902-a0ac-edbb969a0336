

DESCRIPTOR = 
{
	{
		SoundDescriptor_Template=
		{
			name="MRK_Unsquashed",
			WwiseEventGuid="A900DC1D-D48E-4693-AA19-3D7D4DEE2447",
			volume=-16,
			category="NPC_CAT_01",
			--limitCategory="Fakir_Squash",
			limitMode=LimiterMode.RejectNew,
			maxInstances=3,
			params =
			{
				SoundParams =
				{
					numChannels = 1,
					loop = 0,
					playMode = 1,
					randomVolMin = 0.000000,
					randomVolMax = 0.000000,
					randomPitchMin = 0.9,
					randomPitchMax = 1.1,
					fadeInTime = 0.00000,
					fadeOutTime = 0.000000,
					modifiers=
					{
				
						{
							NAME="SpatializedPanning",
							SpatializedPanning=
							{
								widthMin = 0.5,
								widthMax = 2.0,
							}
						},
						{
							NAME="ScreenRollOff",
							ScreenRollOff=
							{
								distanceMin = 0.5,
								distanceMax = 2.0,
							}
						},
						
					}
				},
				
			},
			files=
			{
				{ VAL = "Sound/200_Characters/210_Common/Fakir/Sfx_Fakir_UnSquashed_01.wav", },
				{ VAL = "Sound/200_Characters/210_Common/Fakir/Sfx_Fakir_UnSquashed_02.wav", },
				{ VAL = "Sound/200_Characters/210_Common/Fakir/Sfx_Fakir_UnSquashed_03.wav", },
				
			}
		}
	},			
}

appendTable(component.SoundComponent_Template.soundList,DESCRIPTOR)
