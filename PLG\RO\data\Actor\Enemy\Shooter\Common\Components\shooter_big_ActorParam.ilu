includeReference("Actor/Includes/helpers.ilu")
includeReference("Actor/Includes/gameplay_types.ilu")

Shooter_components =
{
	NAME = "Ray_ShooterActorParameterComponent_Template",
	Ray_ShooterActorParameterComponent_Template =
	{
		vacuumData =
		{
			NAME = "Ray_VacuumData_Template",
			Ray_VacuumData_Template =
			{
				vacuumMinDuration = 0.7,
				vacuumMaxDuration = 1.1 ,
			},
		},
		
		playerEjectBehavior = 
		{
			NAME = "Ray_AIShooterEjectedBehavior_Template", 
			Ray_AIShooterEjectedBehavior_Template =
			{
				destroyOnEjectActionsEnd = 1,
				ejectAction = 
				{
					NAME = "Ray_AIShooterProjectileAction_Template", 
					Ray_AIShooterProjectileAction_Template =
					{
						debugName = "ShooterProjectile",
						basicBullet = 
						{
							Ray_BasicBullet_Template =
							{
								rotationSpeed = -15.0,
								lifetime = 10.0,
								faction = Faction.Friendly,
								hitType = 1, 
								numHits = 50,--5
								destroyOnEnvironment = 0,
								checkEnvironment = 1,
							},
						},
					},
				},				
			},		
		},
		
		--[[playerVacuumBehavior =
		{
			NAME = "AIPlayActionsBehavior_Template",
			AIPlayActionsBehavior_Template =
			{ 
				actions = 
				{
					{
						NAME="AIIdleAction_Template",
						AIIdleAction_Template =
						{
							action = "Idle_wind",
						}
					},
				}
			},
		},]]--
	},
}

appendTable(params.Actor_Template.COMPONENTS,{Shooter_components})
Shooter_components = {}
