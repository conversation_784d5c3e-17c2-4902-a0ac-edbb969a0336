<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-0.22669113747287"/>
			<AngleLocal type="number" value="-0.22669113747287"/>
			<Lenght type="number" value="0.011517197825015"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Bonus_03"/>
			<Pos type="vector" x="0.91539126634598" y="0.12958039343357" z="0"/>
			<PosEnd type="vector" x="0.9269083738327" y="0.1296259611845" z="0"/>
			<PosLocal type="vector" x="0.91539126634598" y="0.12958039343357" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="D1712D1710MyApp1699"/>
				<Element index="2" type="string" value="D1712D1710MyApp1700"/>
				<Element index="3" type="string" value="D1712D1710MyApp1701"/>
				<Element index="4" type="string" value="D1712D1710MyApp1702"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A21"/>
			<UID type="string" value="D1712D1710MyApp1698"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A21.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Bonus_03"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D1712D1710MyApp1700"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D1712D1710MyApp1698"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.29552021622658" y="0.9553365111351" z="0"/>
					<Pos type="vector" x="-2.2099018096924" y="0.0087572587653995" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.29173809289932" y="-0.95649820566177" z="0"/>
			<PosUV type="vector" x="0.88997423648834" y="0.12072250247002" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A21"/>
			<UID type="string" value="D1712D1710MyApp1699"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D1712D1710MyApp1699"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D1712D1710MyApp1698"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.19350872933865" y="-0.98109865188599" z="0"/>
					<Pos type="vector" x="-1.1812771558762" y="-0.045266002416611" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.18962548673153" y="0.98185652494431" z="0"/>
			<PosUV type="vector" x="0.90160727500916" y="0.17479221522808" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A21"/>
			<UID type="string" value="D1712D1710MyApp1700"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D1712D1710MyApp1702"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D1712D1710MyApp1698"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.29552021622658" y="0.9553365111351" z="0"/>
					<Pos type="vector" x="1.0888849496841" y="0.019677685573697" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.29173809289932" y="-0.95649820566177" z="0"/>
			<PosUV type="vector" x="0.9280099272728" y="0.10995247960091" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A21"/>
			<UID type="string" value="D1712D1710MyApp1701"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D1712D1710MyApp1701"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D1712D1710MyApp1698"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.19866932928562" y="-0.9800665974617" z="0"/>
					<Pos type="vector" x="2.3083209991455" y="-0.033404760062695" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.19479015469551" y="0.98084497451782" z="0"/>
			<PosUV type="vector" x="0.94184428453445" y="0.16309008002281" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A21"/>
			<UID type="string" value="D1712D1710MyApp1702"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A21"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="-9048.83984375" y="-991.53063964844" z="0"/>
	<zoomSize type="vector" x="10679.793945313" y="10679.79296875" z="10.056303024292"/>
</root>
