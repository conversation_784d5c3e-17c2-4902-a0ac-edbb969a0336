DESCRIPTOR = 
{
    {

                SoundDescriptor_Template=
                {
                    name="<PERSON>aki<PERSON><PERSON><PERSON>",
                    WwiseEventGuid="E8D50424-077F-4455-85DD-CB06C15C5645",
                    volume=-22,
                    category="NPC_CAT_01",
					--limitCategory="",
					limitMode=LimiterMode.RejectNew,
					maxInstances=3,
                    params =
					{
						SoundParams =
						{
							numChannels = 1,
							loop = 0,
							playMode = 1,
							randomVolMin = 0.000000,
							randomVolMax = 0.000000,
							randomPitchMin = 0.9,
							randomPitchMax = 1.1,
							fadeInTime = 0.00000,
							fadeOutTime = 0.000000,
							modifiers=
							{
						
								{
									NAME="SpatializedPanning",
									SpatializedPanning=
									{
										widthMin = 0.5,
										widthMax = 2.0,
									}
								},
								{
									NAME="ScreenRollOff",
									ScreenRollOff=
									{
										distanceMin = 0.5,
										distanceMax = 2.0,
									}
								},
								
							}
						},
						
					},
                    files=
                    {
                        
						{
                            VAL="Sound/200_Characters/210_Common/Fakir/Sfx_Fakir_<PERSON>_Iddle_Squash_01.wav",
                        },
						{
                            VAL="Sound/200_Characters/210_Common/Fakir/Sfx_Fakir_Beard_Iddle_Squash_02.wav",
                        },
						{
                            VAL="Sound/200_Characters/210_Common/Fakir/Sfx_Fakir_Beard_Iddle_Squash_03.wav",
                        },
                    }
                }
				},
}

appendTable(component.SoundComponent_Template.soundList,DESCRIPTOR)
