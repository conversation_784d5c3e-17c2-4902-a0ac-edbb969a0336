includeReference("Actor/Includes/helpers.ilu")
includeReference("Actor/Includes/gameplay_types.ilu")

Shooter_components =
{
	NAME = "Ray_ShooterActorParameterComponent_Template",
	Ray_ShooterActorParameterComponent_Template =
	{
		vacuumData =
		{
			NAME = "Ray_VacuumData_Template",
			Ray_VacuumData_Template =
			{
				vacuumMinDuration = 0.3,--0.5,
				vacuumMaxDuration = 0.6,--0.9,
			},
		},
		
		playerEjectBehavior = 
		{
			NAME = "Ray_AIShooterEjectedBehavior_Template", 
			Ray_AIShooterEjectedBehavior_Template =
			{
				destroyOnEjectActionsEnd = 1,
				ejectAction = 
				{
					NAME = "Ray_AIShooterProjectileAction_Template", 
					Ray_AIShooterProjectileAction_Template =
					{
						debugName = "ShooterProjectile",
						action = "Eject",
						basicBullet = 
						{
							NAME = "Ray_BasicBullet_Template", 
							Ray_BasicBullet_Template =
							{
								--rotationSpeed = -15.0,
								lifetime = 10.0,
								faction = Faction.Friendly,
								hitType = 1, 
								numHits = 2,
							},
						},
					},
				},				
			},		
		},
	},	
	
}

appendTable(params.Actor_Template.COMPONENTS,{Shooter_components})
Shooter_components = {}
