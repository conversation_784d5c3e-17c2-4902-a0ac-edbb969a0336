<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-90"/>
			<AngleLocal type="number" value="-90"/>
			<Lenght type="number" value="0.033445600420237"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Hair_01_L"/>
			<Pos type="vector" x="0.21207600831985" y="0.097902342677116" z="0"/>
			<PosEnd type="vector" x="0.21207600831985" y="0.13134793937206" z="0"/>
			<PosLocal type="vector" x="0.21207600831985" y="0.097902342677116" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="D2516MyApp2505"/>
				<Element index="2" type="string" value="D2516MyApp2506"/>
				<Element index="3" type="string" value="D2516MyApp2507"/>
				<Element index="4" type="string" value="D2516MyApp2508"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A37"/>
			<UID type="string" value="D2516MyApp2504"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A37.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Fx_Flash_02"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2516MyApp2506"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2516MyApp2504"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.09983341395855" y="0.99500417709351" z="0"/>
					<Pos type="vector" x="-1.5220258235931" y="0.044643223285675" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99500417709351" y="-0.09983341395855" z="0"/>
			<PosUV type="vector" x="0.25671923160553" y="0.046997282654047" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A37"/>
			<UID type="string" value="D2516MyApp2505"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2516MyApp2505"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2516MyApp2504"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="-1.4596878290176" y="-0.04890938103199" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-1" y="0" z="0"/>
			<PosUV type="vector" x="0.16316662728786" y="0.049082212150097" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A37"/>
			<UID type="string" value="D2516MyApp2506"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2516MyApp2508"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2516MyApp2504"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="1.3871288299561" y="0.051836848258972" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="1" y="0" z="0"/>
			<PosUV type="vector" x="0.26391285657883" y="0.14429569244385" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A37"/>
			<UID type="string" value="D2516MyApp2507"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2516MyApp2507"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2516MyApp2504"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="1.5092114210129" y="-0.049689397215843" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-1" y="0" z="0"/>
			<PosUV type="vector" x="0.16238661110401" y="0.14837881922722" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A37"/>
			<UID type="string" value="D2516MyApp2508"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A37"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="42.************" y="78.************" z="0"/>
	<zoomSize type="vector" x="3742.0319824219" y="3742.0324707031" z="3.6050405502319"/>
</root>
