params =
{
    
NAME = "Actor_Template",
Actor_Template =
    {
        SCALE = vector2dNew(1.500000,1.500000),
		zForced = -0.024000,
        useZForced = 1,
        RANK = -1.000000,
        COMPONENTS =
        {
            {
                NAME = "AnimatedComponent_Template",
                AnimatedComponent_Template =
                {
				   patchHLevel = 8,
                    patchVLevel = 2,
                    animationPath = "actor/friendly/redwizard/redwizard_fakir/animation/",
                    animSet =
                    {
                        SubAnimSet_Template =
                        {
                            animations =
                            {
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "Idle",
                                        name = "Stand.anm",
                                        loop = 1,
                                    },
                                },
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "IdleHang",
                                        name = "Boucle_reaction_Hang.anm",
                                        loop = 1,
                                        markerStart = "MRK_BoucleHang_Start",
                                        markerStop = "MRK_BoucleHang_Stop",
                                    },
                                },
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "OnHang",
                                        name = "Boucle_reaction_Hang.anm",
                                        markerStart = "MRK_Hang_Start",
                                        markerStop = "MRK_Hang_Stop",
                                    },
                                },
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "OnUnhang",
                                        name = "Boucle_reaction_Hang.anm",
                                        markerStart = "MRK_NoHang_Start",
                                        markerStop = "MRK_NoHang_Stop",
                                    },
                                },
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "ReceiveHit",
                                        name = "Boucle_reaction_Hang.anm",
                                        markerStart = "MRK_Hang_Start",
                                        markerStop = "MRK_NoHang_Stop",
                                    },
                                },
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "ReceiveHit_Hang",
                                        name = "Boucle_reaction_Hang.anm",
                                        markerStart = "MRK_BoucleHang_Start",
                                        markerStop = "MRK_BoucleHang_Stop",
                                    },
                                },
                            },
                        },
                    },
                    defaultAnimation = "IDLE",
                    inputs =
                    {
                        {
                           InputDesc=
                            {
                                name = "WeightOnPolyLine",
                                varType = 0,
                            },
                        },
                    },
                    tree =
                    {
                        AnimTree_Template =
                        {
                            nodes =
                            {
                                {
                                    NAME = "BlendTreeNodeChooseBranch_Template",
                                    BlendTreeNodeChooseBranch_Template =
                                    {
                                        nodeName = "IDLE",
                                        leafs =
                                        {
                                            {
                                                NAME = "AnimTreeNodePlayAnim_Template",
                                                AnimTreeNodePlayAnim_Template =
                                                {
                                                    nodeName = "IdleNormal",
                                                    animationName = "Idle",
                                                },
                                            },
                                            {
                                                NAME = "AnimTreeNodePlayAnim_Template",
                                                AnimTreeNodePlayAnim_Template =
                                                {
                                                    nodeName = "IdleHang",
                                                    animationName = "IdleHang",
                                                },
                                            },
                                        },
                                        leafsCriterias =
                                        {
                                            {
                                                BlendLeaf =
                                                {
                                                    criterias =
                                                    {
                                                        {
                                                            CriteriaDesc =
                                                            {
                                                                name = "WeightOnPolyLine",
                                                                eval = "<",
                                                                value = 0.5,
                                                            },
                                                        },
                                                    },
                                                },
                                            },
                                            {
                                                BlendLeaf =
                                                {
                                                },
                                            },
                                        },
                                    },
                                },
                                {
                                    NAME = "BlendTreeNodeChooseBranch_Template",
                                    BlendTreeNodeChooseBranch_Template =
                                    {
                                        nodeName = "RECEIVEHIT",
                                        leafs =
                                        {
                                            {
                                                NAME = "AnimTreeNodePlayAnim_Template",
                                                AnimTreeNodePlayAnim_Template =
                                                {
                                                    animationName = "ReceiveHit",
                                                },
                                            },
                                            {
                                                NAME = "AnimTreeNodePlayAnim_Template",
                                                AnimTreeNodePlayAnim_Template =
                                                {
                                                    animationName = "ReceiveHit_Hang",
                                                },
                                            },
                                        },
                                        leafsCriterias =
                                        {
                                            {
                                                BlendLeaf =
                                                {
                                                    criterias =
                                                    {
                                                        {
                                                            CriteriaDesc =
                                                            {
                                                                name = "WeightOnPolyLine",
                                                                eval = "<",
                                                                value = 0.5,
                                                            },
                                                        },
                                                    },
                                                },
                                            },
                                            {
                                                BlendLeaf =
                                                {
                                                },
                                            },
                                        },
                                    },
                                },
                            },
                            nodeTransitions =
                            {
                                {
                                    BlendTreeTransition_Template =
                                    {
                                        from =
                                        {
                                            {
                                                VAL = "IdleNormal",
                                            },
                                        },
                                        to =
                                        {
                                            {
                                                VAL = "IdleHang",
                                            },
                                        },
                                        node =
                                        {
                                            NAME = "AnimTreeNodePlayAnim_Template",
                                            AnimTreeNodePlayAnim_Template =
                                            {
                                                nodeName = "OnHang",
                                                animationName = "OnHang",
                                            },
                                        },
                                    },
                                },
                                {
                                    BlendTreeTransition_Template =
                                    {
                                        from =
                                        {
                                            {
                                                VAL = "IdleHang",
                                            },
                                        },
                                        to =
                                        {
                                            {
                                                VAL = "IdleNormal",
                                            },
                                        },
                                        node =
                                        {
                                            NAME = "AnimTreeNodePlayAnim_Template",
                                            AnimTreeNodePlayAnim_Template =
                                            {
                                                nodeName = "OnUnhang",
                                                animationName = "OnUnhang",
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            },
            {
                NAME = "Ray_SwingRopeComponent_Template",
                Ray_SwingRopeComponent_Template =
                {
                    polylineParams =
                    {
                        {
                            PolylineParameters =
                            {
                                speedLoss = 1.000000,
                                weightMultiplier = 0.000000,
                                landSpeedMultiplier = 10.00000,
                                hitForceMultiplier = 200.000000,
                                impulseMultiplier = 1.200000,
                                gameMaterial = "gamematerial/Liana_Fakir.gmt",
                                environment = 0,
								usePhantom=1,
                                polylines =
                                {
                                    {
                                        VAL = "L_Beard",
                                    },
                                },
                            },
                        },
                    },
                    softPlatformParticles =
                    {
						{
                            BodyData =
                            {
                                bone = "B_WizFak_Beard_02",
                                static = 1,
                            },
                        },
                        {
                            BodyData =
                            {
                                bone = "B_WizFak_Beard_03",
                            },
                        },
                        {
                            BodyData =
                            {
                                bone = "B_WizFak_Beard_04",
                            },
                        },
                        {
                            BodyData =
                            {
                                bone = "B_WizFak_Beard_05",
                            },
                        },
                        {
                            BodyData =
                            {
                                bone = "B_WizFak_Beard_05*",
                            },
                        },
                    },
                    softPlatformConstraints =
                    {
						{
                            ConstraintData =
                            {
                                bodyA = "B_WizFak_Beard_02",
                                bodyB = "B_WizFak_Beard_03",
                                minAngle = 180,
                                stiff = 50,
                                damp = 40,
                            },
                        },
                        {
                            ConstraintData =
                            {
                                bodyA = "B_WizFak_Beard_03",
                                bodyB = "B_WizFak_Beard_04",
                                stiff = 0.000000,
                            },
                        },
                        {
                            ConstraintData =
                            {
                                bodyA = "B_WizFak_Beard_04",
                                bodyB = "B_WizFak_Beard_05",
                                stiff = 0.000000,
                            },
                        },
                        {
                            ConstraintData =
                            {
                                bodyA = "B_WizFak_Beard_05",
                                bodyB = "B_WizFak_Beard_05*",
                                stiff = 0.000000,
                            },
                        },
                    },
					stiffGravityMultiplier = 4.000000,
					stiffImpulseMultiplier = 2.500000,
					stiffSwingSpeedFriction = 0.900000,
                },
            },
            {
                NAME = "PlayAnimOnWeightChangeComponent_Template",
                PlayAnimOnWeightChangeComponent_Template =
                {
                    listenToTrigger = 0,
                    listenToWeight = 1,
                },
            },
            {
                NAME = "RenderSimpleAnimComponent_Template",
                RenderSimpleAnimComponent_Template =
                {
                },
            },
            -- {
                -- NAME = "PhantomComponent_Template",
                -- PhantomComponent_Template =
                -- {
                    -- shape =
                    -- {
                        -- NAME = "PhysShapeCircle",
                        -- PhysShapeCircle =
                        -- {
                            -- Radius = 1.000000,
                        -- },
                    -- },
                    -- offset = vector2dNew(0.000000,0.500000),
                    -- collisionGroup = 4,
                -- },
            -- },
            {
                NAME = "PlayAnimOnEventReceiveComponent_Template",
                PlayAnimOnEventReceiveComponent_Template =
                {
                    listenEvents =
                    {
                        {
                            NAME = "PunchStim",
                            PunchStim =
                            {
                            },
                        },
                    },
                    idleAnim = "Idle",
                    eventAnim = "RECEIVEHIT",
                },
            },
        },
    },
    ActorEditorParams =
    {
    },
}


includeReference("Actor/Friendly/RedWizard/Components/RedWizard_fxController.ilu")
includeReference("Actor/Friendly/RedWizard/Components/RedWizard_Sound.ilu")
