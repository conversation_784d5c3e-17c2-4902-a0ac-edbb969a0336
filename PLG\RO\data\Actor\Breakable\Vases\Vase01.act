includeReference("Actor/Includes/helpers.ilu")
includeReference("Actor/Includes/gameplay_types.ilu")
params =
{
    
NAME = "Actor_Template",
Actor_Template =
    {
        SCALE = vector2dNew(1.5,1.5),
        COMPONENTS =
        {
            {
                NAME = "AnimatedComponent_Template",
                AnimatedComponent_Template =
                {
                    defaultAnimation = "IDLE",
                    useBase=0,
                    animSet =
                    {
                        SubAnimSet_Template =
                        {
                            animations =
                            {
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "Idle",
                                        name = "Actor/Breakable/Vases/Animation/vaseAnim1.anm",
                                        loop = 1,
										markerStart="MRK_Idle_Start",
                                        markerStop="MRK_Idle_Stop",
                                    },
                                },
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "Hit",
                                        name = "Actor/Breakable/Vases/Animation/vaseAnim1.anm",
										loop = 0,
										markerStart="MRK_Hit_Start",
                                        markerStop="MRK_Hit_Stop",
                                    },
                                },   
								{
                                    SubAnim_Template =
                                    {
                                        friendlyName = "Death",
                                        name = "Actor/Breakable/Vases/Animation/vaseAnim1.anm",
										loop = 0,
										markerStart="MRK_Hit_Start",
                                        markerStop="MRK_Hit_Stop",
                                    },
                                },   
                            },
                        },
                    },
                    inputs =
                    {
                        {InputDesc={name="Health", varType=AnimInputTypes.uint}},             -- Used to play random animations
                    },
                    tree =  
                    {
                        AnimTree_Template =
                        {
                            nodes =
                            {
                                -- {
                                    -- NAME="AnimTreeNodePlayAnim_Template",
                                    -- AnimTreeNodePlayAnim_Template =
                                    -- {
                                        -- nodeName = "RECEIVEHIT",
                                        -- animationName = ""
                                    -- }
                                -- },
                                {
                                    NAME="AnimTreeNodePlayAnim_Template",
                                    AnimTreeNodePlayAnim_Template =
                                    {
                                        nodeName = "Idle",
                                        animationName = "Idle"
                                    }
                                },
                                {
                                    NAME="AnimTreeNodePlayAnim_Template",
                                    AnimTreeNodePlayAnim_Template =
                                    {
                                        nodeName = "Death",
                                        animationName = "Death"
                                    }
                                },
                                {
                                    NAME="BlendTreeNodeChooseBranch_Template",
                                    BlendTreeNodeChooseBranch_Template =
                                    {
                                        nodeName = "ReceiveHit",
                                        leafs =
                                        {
                                            {
                                                NAME="AnimTreeNodePlayAnim_Template",
                                                AnimTreeNodePlayAnim_Template =
                                                {
                                                    nodeName = "Hit",
													animationName = "Hit",
                                                }
                                            },
                                            
                                        },
                                        leafsCriterias =
                                        {

                                            {
                                                BlendLeaf =
                                                {
                                                    -- phase 1
                                                    criterias =
                                                    {
                                                        {CriteriaDesc={name="Health",eval="<=",value=100}},
                                                    },
                                                }
                                            },  
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            },
			{
                NAME = "SimpleAIComponent_Template",
                SimpleAIComponent_Template =
                {
                    faction = Faction.Enemy,
                    damageLevels =
                    {
                        {
                            VAL = 100,
                        },
                        {
                            VAL = 100,
                        },
                        {
                            VAL = 100,
                        },
						{
                            VAL = 100,
                        },
                    },
                    genericBehavior =
                    {
                        NAME = "AIPlayActionsBehavior_Template",
                        AIPlayActionsBehavior_Template =
                        { 
                            actions = 
                            {
                                {
                                    NAME="AIPlayAnimAction_Template",
                                    AIPlayAnimAction_Template =
                                    {
                                        action = "idle",
                                    }
                                },
                            }
                        },
                    },
                    receiveHitBehavior =
                    {
                        {
                            NAME="Ray_AIReceiveHitBehavior_Template",
                            Ray_AIReceiveHitBehavior_Template =
                            {
                                receiveHits =
                                {
                                    -- all types, all levels
                                    {
                                        ReceiveHitData =
                                        {
                                            action = 
                                            {
                                                NAME="Ray_AIGround_ReceiveNormalHitAction_Template",
                                                Ray_AIGround_ReceiveNormalHitAction_Template =
                                                {
                                                    faceHitDir=0,
                                                    canBlockHits=1,
                                                    isInterruptible=0,
                                                }
                                            },
                                        }
                                    },
                                },
                            }
                        },
                    },
                    deathBehavior =
                    {
                        NAME = "AIPlayActionsBehavior_Template",
                        AIPlayActionsBehavior_Template =
                        { 
                            actions = 
                            {
                                {
                                    NAME="AIPlayAnimAction_Template",
                                    AIPlayAnimAction_Template =
                                    {
                                        action = "death",
                                    }
                                },
                            }
                        },
                    },
                },
            },
            {
                NAME = "PhantomComponent_Template",
                PhantomComponent_Template =
                {
                    collisionGroup = CollisionGroup.Character,
                    -- drawDebug = 1,
                    shape =
                    {
                        NAME = "PhysShapePolygon",
                        PhysShapePolygon = 
                        { 
                            Points =
                            {
                                { VAL = vector2dNew(-0.6,-0.5) }, 
                                { VAL = vector2dNew(-0.6,1.0) },
                                { VAL = vector2dNew(0.6,1.0) },
                                { VAL = vector2dNew(0.6,-0.5) },
                            },
						}
                    },
                },
            },
			{
				NAME="PushedComponent_Template",
				PushedComponent_Template =
				{
				}
			},
			{
			    NAME="StickToPolylinePhysComponent_Template",
                StickToPolylinePhysComponent_Template=
                {
                    physRadius = 0,
                    physWeight = 0.1,
                    physFriction = 0.5,                             -- The object friction
                    -- physUnstickMinAngle = 2,                        -- Minimum angle between speed and edge to unstick from edge
                    -- physUnstickMaxAngle = 23,                       -- Maximum angle between speed and edge to unstick from edge
                    -- physUnstickMinAngleSpeed = 50,                  -- Speed to unstick from edge at minimum angle between edge and speed
                    -- physUnstickMaxAngleSpeed = 6,                   -- Speed to unstick from edge at maximum angle between edge and speed
                    -- physAngularSpeedMinLinear = 5,                  -- Minimum linear speed to translate to angular speed
                    -- physAngularSpeedMaxLinear = 35,                 -- Maximum linear speed to translate to angular speed
                    -- physAngularSpeedMinAngular = 100,               -- Angular speed at minimum linear speed
                    -- physAngularSpeedMaxAngular = 500,               -- Angular speed at maximum linear speed
                    -- physAngularAirMultiplier = 8,                   -- Multiplier to angular speed when in air
                    -- physDefaultStickMin = 190,                      -- min angle for default sticky area (0-360)
                    -- physDefaultStickMax = 345,                      -- max angle for default sticky area (0-360)
                }
            },
            {
                NAME="Ray_CharacterDebuggerComponent_Template",
                Ray_CharacterDebuggerComponent_Template=
                {
                }
            },
        },
    },
}
