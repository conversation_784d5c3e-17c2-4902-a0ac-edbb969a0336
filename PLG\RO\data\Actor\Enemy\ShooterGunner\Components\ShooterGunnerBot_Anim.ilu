includeReference("Actor/Includes/gameplay_types.ilu")

component = {
NAME="AnimatedComponent_Template",
AnimatedComponent_Template = 
{
    posOffset = vector2dNew(0.0, -4 ),
    
    animationPath = "World/6_MountainWorld/Enemy/TuretSky/Animation/",
    defaultAnimation = "IDLE",
    
    animSet = {SubAnimSet_Template =
    {
        animations=
        {
            {SubAnim_Template={ friendlyName="Aim", name="Aim.anm", }},
			{SubAnim_Template={ friendlyName="stand", name="Stand.anm", }},
            {SubAnim_Template={ friendlyName="Shoot_add", name="Add_Shoot.anm",  playRate = 1.0, }},
			{SubAnim_Template={ friendlyName="Stand_add", name="Add_Cannon_Stand.anm",  playRate = 1.0, }},
            -- {SubAnim_Template={ friendlyName="ReceiveHit", name="Receive_Hit.anm", markerStart="MRK_ReceiveHit_Start", markerStop="MRK_ReceiveHit_Stop", }},
            -- {SubAnim_Template={ friendlyName="ReceiveHitToIdle", name="Receive_Hit.anm", markerStart="MRK_ReceiveHit_To_Stand_Start", markerStop="MRK_ReceiveHit_To_Stand_Stop", }},
            -- {SubAnim_Template={ friendlyName="Death", name="death.anm", }},
        },
    }},
    
    inputs =
    {
        {InputDesc={name="RandomN", varType=AnimInputTypes.float}},              -- Used to play random animations
        {InputDesc={name="ReceivedHitLevel", varType=AnimInputTypes.uint}},      -- Level of the last received hit
        {InputDesc={name="ReceivedHitType", varType=AnimInputTypes.uint}},       -- Type of the hit being received
        {InputDesc={name="Stunned", varType=AnimInputTypes.bool}},               -- If the character is stunned
        {InputDesc={name="AimCursor", varType=AnimInputTypes.float}},            -- If the character is stunned
    },
    
    tree = 
    {
        AnimTree_Template =
        {
            nodes =
            {
                -- {NAME="AnimTreeNodePlayAnim_Template",
				-- AnimTreeNodePlayAnim_Template =
				-- {
					-- nodeName = "IDLE",
					-- animationName = "Aim",
				-- }},
				-- {NAME="AnimTreeNodePlayAnim_Template",
				-- AnimTreeNodePlayAnim_Template =
				-- {
					-- nodeName = "SHOOT",
					-- animationName = "Shoot_add",
				-- }},
				
				{NAME="BlendTreeNodeAddBranch_Template",
                BlendTreeNodeAddBranch_Template =
                {
                    nodeName = "IDLE",
                    leafs =
                    {
                        {NAME="AnimTreeNodePlayAnim_Template",
                        AnimTreeNodePlayAnim_Template =
                        {
                            animationName = "Stand_add",
                        }}, 
                        {NAME="AnimTreeNodePlayAnim_Template",
                        AnimTreeNodePlayAnim_Template =
                        {
                            animationName = "Aim",
                            proceduralInput =
                                {ProceduralInputData =
                                {
                                    input = "AimCursor",
                                }},
                        }},
                    },
                }},
				
				-- {NAME="AnimTreeNodePlayAnim_Template",
				-- AnimTreeNodePlayAnim_Template =
				-- {
					-- nodeName = "SHOOT",
					-- animationName = "Shoot",
				-- }},
                
                {NAME="BlendTreeNodeAddBranch_Template",
                BlendTreeNodeAddBranch_Template =
                {
                    nodeName = "SHOOT",
                    leafs =
                    {
                        {NAME="AnimTreeNodePlayAnim_Template",
                        AnimTreeNodePlayAnim_Template =
                        {
                            animationName = "Shoot_add",
                        }},						
                        {NAME="AnimTreeNodePlayAnim_Template",
                        AnimTreeNodePlayAnim_Template =
                        {
                            animationName = "Aim",
                            proceduralInput =
                                {ProceduralInputData =
                                {
                                    input = "AimCursor",
                                }},
                        }},
                    },
                }},
				
				{NAME="AnimTreeNodePlayAnim_Template",
				AnimTreeNodePlayAnim_Template =
				{
					nodeName = "RECEIVEHIT",
					animationName = "stand",
				}},
				{NAME="AnimTreeNodePlayAnim_Template",
				AnimTreeNodePlayAnim_Template =
				{
					nodeName = "DEATH",
					animationName = "stand",
				}},
                
                -- {NAME="BlendTreeNodeAddBranch_Template",
                -- BlendTreeNodeAddBranch_Template =
                -- {
                    -- nodeName = "RECEIVEHIT",
                    -- leafs =
                    -- {
                        -- {NAME="AnimTreeNodePlayAnim_Template",
                        -- AnimTreeNodePlayAnim_Template =
                        -- {
                            -- animationName = "ReceiveHit",
                        -- }},
                        -- {NAME="AnimTreeNodePlayAnim_Template",
                        -- AnimTreeNodePlayAnim_Template =
                        -- {
                            -- animationName = "Aim",
                            -- proceduralInput =
                                -- {ProceduralInputData =
                                -- {
                                    -- input = "AimCursor",
                                -- }},
                        -- }},
                    -- },
                -- }},
                
                -- {NAME="BlendTreeNodeAddBranch_Template",
                -- BlendTreeNodeAddBranch_Template =
                -- {
                    -- nodeName = "DEATH",
                    -- leafs =
                    -- {
                        -- {NAME="AnimTreeNodePlayAnim_Template",
                        -- AnimTreeNodePlayAnim_Template =
                        -- {
                            -- animationName = "Death",
                        -- }},
                        -- {NAME="AnimTreeNodePlayAnim_Template",
                        -- AnimTreeNodePlayAnim_Template =
                        -- {
                            -- animationName = "Aim",
                            -- proceduralInput =
                                -- {ProceduralInputData =
                                -- {
                                    -- input = "AimCursor",
                                -- }},
                        -- }},
                    -- },
                -- }},
            },
            nodeTransitions =
            {
                -- {BlendTreeTransition_Template =
                -- {
                    -- from =
                    -- {
                        -- { VAL = "RECEIVEHIT" },
                    -- },
                    -- to =
                    -- {
                        -- { VAL = "IDLE" },
                    -- },
                    -- node =
                        -- {NAME="AnimTreeNodePlayAnim_Template",
                        -- AnimTreeNodePlayAnim_Template =
                        -- {
                            -- animationName = "ReceiveHitToIdle",
                        -- }},
                -- }},
            },
        }
    },
}}

appendTable(params.Actor_Template.COMPONENTS,{component})
component = {}

