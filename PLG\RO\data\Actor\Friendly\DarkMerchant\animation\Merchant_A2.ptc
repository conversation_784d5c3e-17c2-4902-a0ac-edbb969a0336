<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="90"/>
			<AngleLocal type="number" value="90"/>
			<Lenght type="number" value="0.025968194007874"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_01_L"/>
			<ParentCut type="boolean" value="true"/>
			<Pos type="vector" x="0.078964173793793" y="0.27792885899544" z="0"/>
			<PosEnd type="vector" x="0.078964173793793" y="0.25196066498756" z="0"/>
			<PosLocal type="vector" x="0.078964173793793" y="0.27792885899544" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp164"/>
				<Element index="2" type="string" value="MyApp165"/>
				<Element index="3" type="string" value="MyApp166"/>
				<Element index="4" type="string" value="MyApp167"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A2"/>
			<UID type="string" value="MyApp162"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="2">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-109.17301524194"/>
			<AngleLocal type="number" value="-199.17301524194"/>
			<Lenght type="number" value="0.11999984830618"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_02_L"/>
			<ParentCut type="boolean" value="false"/>
			<ParentUID type="string" value="MyApp162"/>
			<Pos type="vector" x="0.07835315912962" y="0.28220596909523" z="0"/>
			<PosEnd type="vector" x="0.038942590355873" y="0.39554956555367" z="0"/>
			<PosLocal type="vector" x="-0.030245304107666" y="-0.00061101466417313" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp348"/>
				<Element index="2" type="string" value="MyApp349"/>
				<Element index="3" type="string" value="MyApp350"/>
				<Element index="4" type="string" value="MyApp351"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A2"/>
			<UID type="string" value="MyApp163"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="3">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-95.8955153877"/>
			<AngleLocal type="number" value="-185.8955153877"/>
			<Lenght type="number" value="0.11302489042282"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_03_L"/>
			<ParentCut type="boolean" value="true"/>
			<ParentUID type="string" value="MyApp162"/>
			<Pos type="vector" x="0.17642131447792" y="0.26845809817314" z="0"/>
			<PosEnd type="vector" x="0.16481199860573" y="0.38088518381119" z="0"/>
			<PosLocal type="vector" x="-0.016497433185577" y="0.097457140684128" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp208"/>
				<Element index="2" type="string" value="MyApp209"/>
				<Element index="3" type="string" value="MyApp210"/>
				<Element index="4" type="string" value="MyApp211"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A2"/>
			<UID type="string" value="MyApp196"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="4">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-89.70953428233"/>
			<AngleLocal type="number" value="-0.60007259131001"/>
			<Lenght type="number" value="0.102349370718"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_03_R"/>
			<ParentCut type="boolean" value="true"/>
			<ParentUID type="string" value="D199D196MyApp196"/>
			<Pos type="vector" x="0.20902982354164" y="0.27609914541245" z="0"/>
			<PosEnd type="vector" x="0.20954869687557" y="0.37844720482826" z="0"/>
			<PosLocal type="vector" x="-0.045501679182053" y="0.075172767043114" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp212"/>
				<Element index="2" type="string" value="MyApp213"/>
				<Element index="3" type="string" value="MyApp214"/>
				<Element index="4" type="string" value="MyApp215"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A2"/>
			<UID type="string" value="D196MyApp196"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="5">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-89.10946169102"/>
			<AngleLocal type="number" value="-179.10946169102"/>
			<Lenght type="number" value="0.063512496650219"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_01_R"/>
			<ParentCut type="boolean" value="true"/>
			<ParentUID type="string" value="MyApp162"/>
			<Pos type="vector" x="0.2839135825634" y="0.25692215561867" z="0"/>
			<PosEnd type="vector" x="0.28490069508553" y="0.32042700052261" z="0"/>
			<PosLocal type="vector" x="-0.0049614906311035" y="0.20494940876961" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp216"/>
				<Element index="2" type="string" value="MyApp217"/>
				<Element index="3" type="string" value="MyApp218"/>
				<Element index="4" type="string" value="MyApp219"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A2"/>
			<UID type="string" value="D199D196MyApp196"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="6">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-89.50810698487"/>
			<AngleLocal type="number" value="-0.39864529385002"/>
			<Lenght type="number" value="0.063512496650219"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_02_R"/>
			<ParentCut type="boolean" value="true"/>
			<ParentUID type="string" value="D199D196MyApp196"/>
			<Pos type="vector" x="0.24955752491951" y="0.28776162862778" z="0"/>
			<PosEnd type="vector" x="0.25010278820992" y="0.35127177834511" z="0"/>
			<PosLocal type="vector" x="-0.033210717141628" y="0.034831222146749" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp220"/>
				<Element index="2" type="string" value="MyApp221"/>
				<Element index="3" type="string" value="MyApp222"/>
				<Element index="4" type="string" value="MyApp223"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A2"/>
			<UID type="string" value="D202D199D196MyApp196"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A2.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Cloak00"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp165"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp162"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.45571169257164" y="-0.89012748003006" z="0"/>
					<Pos type="vector" x="1.2852947711945" y="-0.042569003999233" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.89012748003006" y="-0.45571169257164" z="0"/>
			<PosUV type="vector" x="0.12153317779303" y="0.24455207586288" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A2"/>
			<UID type="string" value="MyApp164"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp164"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp162"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.54229962825775" y="0.84018522500992" z="0"/>
					<Pos type="vector" x="1.1441180706024" y="0.031265210360289" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.84018522500992" y="-0.54229962825775" z="0"/>
			<PosUV type="vector" x="0.047698963433504" y="0.24821817874908" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A2"/>
			<UID type="string" value="MyApp165"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp167"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp162"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.17849840223789" y="-0.98394030332565" z="0"/>
					<Pos type="vector" x="0.010028140619397" y="-0.054706528782845" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.98394030332565" y="0.17849840223789" z="0"/>
			<PosUV type="vector" x="0.13367070257664" y="0.27766844630241" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A2"/>
			<UID type="string" value="MyApp166"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp166"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp162"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.26978680491447" y="0.96292012929916" z="0"/>
					<Pos type="vector" x="0.011568282730877" y="0.04141553491354" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.96292012929916" y="-0.26978680491447" z="0"/>
			<PosUV type="vector" x="0.037548638880253" y="0.27762845158577" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A2"/>
			<UID type="string" value="MyApp167"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="5">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp209"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp196"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-0.049794610589743" y="0.0079867318272591" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99471086263657" y="0.10271468013525" z="0"/>
			<PosUV type="vector" x="0.18494388461113" y="0.26368018984795" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A2"/>
			<UID type="string" value="MyApp208"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="6">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp208"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp196"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.21925677359104" y="-0.97566717863083" z="0"/>
					<Pos type="vector" x="-0.014569298364222" y="-0.024241285398602" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.94798582792282" y="-0.31831243634224" z="0"/>
			<PosUV type="vector" x="0.15247738361359" y="0.26433017849922" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A2"/>
			<UID type="string" value="MyApp209"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="7">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp211"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp196"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="1.1215987205505" y="0.0069614201784134" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99471086263657" y="0.10271468013525" z="0"/>
			<PosUV type="vector" x="0.17032492160797" y="0.39527121186256" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A2"/>
			<UID type="string" value="MyApp210"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="8">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp210"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp196"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.31556561589241" y="-0.94890385866165" z="0"/>
					<Pos type="vector" x="1.2028373479843" y="-0.067961357533932" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.91147172451019" y="-0.41136288642883" z="0"/>
			<PosUV type="vector" x="0.094855293631554" y="0.39670896530151" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A2"/>
			<UID type="string" value="MyApp211"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="9">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp213"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D196MyApp196"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-0.057591035962105" y="0.010099903680384" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99998712539673" y="-0.0050696288235486" z="0"/>
			<PosUV type="vector" x="0.21909971535206" y="0.27015361189842" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A2"/>
			<UID type="string" value="MyApp212"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="10">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp212"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D196MyApp196"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.10506887733936" y="-0.99446493387222" z="0"/>
					<Pos type="vector" x="-0.051925871521235" y="-0.011612800881267" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.99498480558395" y="-0.10002595186234" z="0"/>
			<PosUV type="vector" x="0.1973902285099" y="0.27084350585938" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A2"/>
			<UID type="string" value="MyApp213"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="11">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp215"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D196MyApp196"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="1.0607005357742" y="0.0083156749606133" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99998712539673" y="-0.0050696288235486" z="0"/>
			<PosUV type="vector" x="0.21789576113224" y="0.38461762666702" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A2"/>
			<UID type="string" value="MyApp214"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="12">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp214"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D196MyApp196"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.10506887733936" y="-0.99446493387222" z="0"/>
					<Pos type="vector" x="1.1301593780518" y="-0.024405363947153" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.99498480558395" y="-0.10002595186234" z="0"/>
			<PosUV type="vector" x="0.18521118164063" y="0.39189249277115" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A2"/>
			<UID type="string" value="MyApp215"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="13">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp217"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D199D196MyApp196"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.10506888478994" y="0.99446499347687" z="0"/>
					<Pos type="vector" x="-0.15578316152096" y="0.02789905294776" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99597781896591" y="0.089600197970867" z="0"/>
			<PosUV type="vector" x="0.3116554915905" y="0.24659556150436" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A2"/>
			<UID type="string" value="MyApp216"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="14">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp216"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D199D196MyApp196"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="-0.11653352528811" y="-0.017636884003878" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.99987918138504" y="0.015542015433311" z="0"/>
			<PosUV type="vector" x="0.26616379618645" y="0.24979582428932" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A2"/>
			<UID type="string" value="MyApp217"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="15">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp219"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D199D196MyApp196"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.10506888478994" y="0.99446499347687" z="0"/>
					<Pos type="vector" x="1.0095545053482" y="0.016085376963019" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99597781896591" y="0.089600197970867" z="0"/>
			<PosUV type="vector" x="0.30099356174469" y="0.32078376412392" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A2"/>
			<UID type="string" value="MyApp218"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="16">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp218"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D199D196MyApp196"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="1.1332458257675" y="-0.01398512814194" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.99987918138504" y="0.015542015433311" z="0"/>
			<PosUV type="vector" x="0.27104878425598" y="0.32910612225533" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A2"/>
			<UID type="string" value="MyApp219"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="17">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp221"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D202D199D196MyApp196"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-0.081168457865715" y="0.0077418875880539" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99996316432953" y="-0.0085851354524493" z="0"/>
			<PosUV type="vector" x="0.2572548687458" y="0.28254014253616" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A2"/>
			<UID type="string" value="MyApp220"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="18">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp220"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D202D199D196MyApp196"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="-0.062392171472311" y="-0.017268920317292" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.99996316432953" y="0.0085851354524493" z="0"/>
			<PosUV type="vector" x="0.23225522041321" y="0.28394734859467" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A2"/>
			<UID type="string" value="MyApp221"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="19">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp223"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D202D199D196MyApp196"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="1.1651805639267" y="0.010627597570419" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99996316432953" y="-0.0085851354524493" z="0"/>
			<PosUV type="vector" x="0.2608200609684" y="0.************" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A2"/>
			<UID type="string" value="MyApp222"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="20">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp222"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D202D199D196MyApp196"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="1.2670065164566" y="-0.017904022708535" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.99996316432953" y="0.0085851354524493" z="0"/>
			<PosUV type="vector" x="0.23234501481056" y="0.36838310956955" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A2"/>
			<UID type="string" value="MyApp223"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="21">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp349"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp163"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.31056702136993" y="0.95055156946182" z="0"/>
					<Pos type="vector" x="0.90810459852219" y="0.029133154079318" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.79582858085632" y="0.60552209615707" z="0"/>
			<PosUV type="vector" x="0.070081412792206" y="0.3947017788887" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A2"/>
			<UID type="string" value="MyApp348"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="22">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp348"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp163"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="0.99373269081116" y="-0.029339116066694" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.94453114271164" y="-0.32842180132866" z="0"/>
			<PosUV type="vector" x="0.011477880179882" y="0.38520359992981" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A2"/>
			<UID type="string" value="MyApp349"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="23">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp351"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp163"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="1.0056113004684" y="0.027785811573267" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.94453114271164" y="0.32842180132866" z="0"/>
			<PosUV type="vector" x="0.064966008067131" y="0.40531104803085" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A2"/>
			<UID type="string" value="MyApp350"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="24">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp350"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp163"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="1.1042215824127" y="-0.028820702806115" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.94453114271164" y="-0.32842180132866" z="0"/>
			<PosUV type="vector" x="0.0076131075620651" y="0.39789706468582" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A2"/>
			<UID type="string" value="MyApp351"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A2"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="872.07867431641" y="-27.************" z="0"/>
	<zoomSize type="vector" x="2144.7709960938" y="2144.7697753906" z="1.9480202198029"/>
</root>
