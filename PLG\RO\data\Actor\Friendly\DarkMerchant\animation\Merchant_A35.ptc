<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="18.087016696241"/>
			<AngleLocal type="number" value="18.087016696241"/>
			<Lenght type="number" value="0.16264875233173"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_01_R"/>
			<Pos type="vector" x="0.29629480838776" y="0.81278121471405" z="0"/>
			<PosEnd type="vector" x="0.45090645551682" y="0.76228511333466" z="0"/>
			<PosLocal type="vector" x="0.29629480838776" y="0.81278121471405" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="D2501MyApp2470"/>
				<Element index="2" type="string" value="D2501MyApp2471"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A35"/>
			<UID type="string" value="D2501MyApp2468"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="2">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-1.10475051147"/>
			<AngleLocal type="number" value="-19.191767207711"/>
			<Lenght type="number" value="0.18272769451141"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_02_R"/>
			<ParentUID type="string" value="D2501MyApp2468"/>
			<Pos type="vector" x="0.45090645551682" y="0.76228511333466" z="0"/>
			<PosEnd type="vector" x="0.63360017538071" y="0.76580816507339" z="0"/>
			<PosLocal type="vector" x="0" y="2.3370603230433e-009" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="D2501MyApp2476"/>
				<Element index="2" type="string" value="D2501MyApp2477"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A35"/>
			<UID type="string" value="D2501MyApp2469"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A35.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Arm08_R"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2501MyApp2471"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2501MyApp2468"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-0.01240321714431" y="0.021831119433045" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.31046104431152" y="-0.95058614015579" z="0"/>
			<PosUV type="vector" x="0.28759941458702" y="0.79265516996384" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A35"/>
			<UID type="string" value="D2501MyApp2470"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2501MyApp2470"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2501MyApp2468"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="-0.061140734702349" y="-0.020011086016893" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.31046104431152" y="0.95058614015579" z="0"/>
			<PosUV type="vector" x="0.29305440187454" y="0.83489084243774" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A35"/>
			<UID type="string" value="D2501MyApp2471"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2501MyApp2477"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2501MyApp2469"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.099833786487579" y="0.99500417709351" z="0"/>
					<Pos type="vector" x="1.0299445390701" y="0.051970839500427" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.080631211400032" y="-0.9967440366745" z="0"/>
			<PosUV type="vector" x="0.64007288217545" y="0.71395248174667" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A35"/>
			<UID type="string" value="D2501MyApp2476"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2501MyApp2476"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2501MyApp2469"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.11030147969723" y="-0.9938982129097" z="0"/>
					<Pos type="vector" x="1.0181819200516" y="-0.021818062290549" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.12944367527962" y="0.99158680438995" z="0"/>
			<PosUV type="vector" x="0.63650125265121" y="0.78768622875214" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A35"/>
			<UID type="string" value="D2501MyApp2477"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A35"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="135.55212402344" y="-1000.3579101563" z="0"/>
	<zoomSize type="vector" x="2120.2646484375" y="2120.265625" z="2.0505473613739"/>
</root>
