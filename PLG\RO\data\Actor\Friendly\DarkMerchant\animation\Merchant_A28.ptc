<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="88.175878091242"/>
			<AngleLocal type="number" value="88.175878091242"/>
			<Lenght type="number" value="0.051536865532398"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Pelvis"/>
			<Pos type="vector" x="0.45390972495079" y="0.86349129676819" z="0"/>
			<PosEnd type="vector" x="0.45555022358894" y="0.81198054552078" z="0"/>
			<PosLocal type="vector" x="0.45390972495079" y="0.86349129676819" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp2320"/>
				<Element index="2" type="string" value="MyApp2321"/>
				<Element index="3" type="string" value="MyApp2345"/>
				<Element index="4" type="string" value="MyApp2344"/>
				<Element index="5" type="string" value="MyApp2322"/>
				<Element index="6" type="string" value="MyApp2323"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A28"/>
			<UID type="string" value="MyApp2318"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A28.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Body07"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2321"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2318"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-0.38188278675079" y="0.11395813524723" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.99949324131012" y="-0.031831555068493" z="0"/>
			<PosUV type="vector" x="0.33938285708427" y="0.87953490018845" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A28"/>
			<UID type="string" value="MyApp2320"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2320"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2318"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.094595186412334" y="-0.99551582336426" z="0"/>
					<Pos type="vector" x="-0.22420175373554" y="-0.13179470598698" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99802243709564" y="-0.062858432531357" z="0"/>
			<PosUV type="vector" x="0.58526986837387" y="0.8792353272438" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A28"/>
			<UID type="string" value="MyApp2321"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2323"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2318"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.88146102428436" y="0.47225677967072" z="0"/>
					<Pos type="vector" x="1.3274520635605" y="0.032893486320972" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.44395917654037" y="-0.89604699611664" z="0"/>
			<PosUV type="vector" x="0.4232105910778" y="0.7940661907196" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A28"/>
			<UID type="string" value="MyApp2322"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2322"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2318"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.93203926086426" y="-0.36235752701759" z="0"/>
					<Pos type="vector" x="1.3807498216629" y="-0.050277952104807" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.39184215664864" y="-0.92003256082535" z="0"/>
			<PosUV type="vector" x="0.50642734766006" y="0.79396826028824" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A28"/>
			<UID type="string" value="MyApp2323"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="5">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2345"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2318"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.029169805347919" y="-0.99957448244095" z="0"/>
					<Pos type="vector" x="0.70370155572891" y="-0.13171701133251" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99999648332596" y="0.0026629865169525" z="0"/>
			<PosUV type="vector" x="0.58671438694" y="0.83143585920334" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A28"/>
			<UID type="string" value="MyApp2344"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="6">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2344"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2318"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.60981792211533" y="0.79254162311554" z="0"/>
					<Pos type="vector" x="0.7930114865303" y="0.11461570858955" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.77272856235504" y="-0.63473671674728" z="0"/>
			<PosUV type="vector" x="0.34065303206444" y="0.81899428367615" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A28"/>
			<UID type="string" value="MyApp2345"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A28"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="-404.69543457031" y="-1354.3227539063" z="0"/>
	<zoomSize type="vector" x="2231.8598632813" y="2231.8588867188" z="2.1584711074829"/>
</root>
