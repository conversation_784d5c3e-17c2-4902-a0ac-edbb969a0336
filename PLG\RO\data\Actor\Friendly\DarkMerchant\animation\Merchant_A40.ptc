<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="0.032885392106342"/>
			<AngleLocal type="number" value="0.032885392106342"/>
			<Lenght type="number" value="0.044135563075542"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_01_L"/>
			<Pos type="vector" x="0.22223757207394" y="0.72256338596344" z="0"/>
			<PosEnd type="vector" x="0.2663731276989" y="0.72253805398941" z="0"/>
			<PosLocal type="vector" x="0.22223757207394" y="0.72256338596344" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="D4231MyApp3775"/>
				<Element index="2" type="string" value="D4231MyApp3776"/>
				<Element index="3" type="string" value="D4231MyApp3777"/>
				<Element index="4" type="string" value="D4231MyApp3778"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A40"/>
			<UID type="string" value="D4231D3086MyApp2319"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="2">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="0.25435943176941"/>
			<AngleLocal type="number" value="0.22147403966307"/>
			<Lenght type="number" value="0.25385054945946"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_02_L"/>
			<ParentCut type="boolean" value="false"/>
			<ParentUID type="string" value="D4231D3086MyApp2319"/>
			<Pos type="vector" x="0.2663731276989" y="0.72253805398941" z="0"/>
			<PosEnd type="vector" x="0.52022117376328" y="0.72141110897064" z="0"/>
			<PosLocal type="vector" x="0" y="0" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="D4231MyApp3654"/>
				<Element index="2" type="string" value="D4231MyApp3655"/>
				<Element index="3" type="string" value="D4231MyApp3656"/>
				<Element index="4" type="string" value="D4231MyApp3657"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A40"/>
			<UID type="string" value="D4231MyApp3653"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A40.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Arm07_L"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D4231MyApp3655"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D4231MyApp3653"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="0.054826028645039" y="0.024208569899201" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.0044394037686288" y="-0.99999016523361" z="0"/>
			<PosUV type="vector" x="0.28018313646317" y="0.69826793670654" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A40"/>
			<UID type="string" value="D4231MyApp3654"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D4231MyApp3654"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D4231MyApp3653"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="0.056193239986897" y="-0.019403079524636" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.0044394037686288" y="0.99999016523361" z="0"/>
			<PosUV type="vector" x="0.28072381019592" y="0.74187761545181" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A40"/>
			<UID type="string" value="D4231MyApp3655"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D4231MyApp3657"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D4231MyApp3653"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="1.0284087657928" y="0.02433025278151" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.0044394037686288" y="-0.99999016523361" z="0"/>
			<PosUV type="vector" x="0.52732467651367" y="0.69704908132553" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A40"/>
			<UID type="string" value="D4231MyApp3656"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D4231MyApp3656"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D4231MyApp3653"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="1.037348151207" y="-0.017362715676427" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.0044394037686288" y="0.99999016523361" z="0"/>
			<PosUV type="vector" x="0.52977901697159" y="0.73873156309128" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A40"/>
			<UID type="string" value="D4231MyApp3657"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="5">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D4231MyApp3776"/>
			<Color>
				<A type="number" value="0"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D4231D3086MyApp2319"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-0.28171500563622" y="0.022613750770688" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.00057395832845941" y="-0.99999982118607" z="0"/>
			<PosUV type="vector" x="0.2097909450531" y="0.69995677471161" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A40"/>
			<UID type="string" value="D4231MyApp3775"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="6">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D4231MyApp3775"/>
			<Color>
				<A type="number" value="0"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D4231D3086MyApp2319"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="-0.26118829846382" y="-0.019426288083196" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.00057395832845941" y="0.99999982118607" z="0"/>
			<PosUV type="vector" x="0.21072103083134" y="0.74199628829956" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A40"/>
			<UID type="string" value="D4231MyApp3776"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="7">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D4231MyApp3778"/>
			<Color>
				<A type="number" value="0"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D4231D3086MyApp2319"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="0.25064495205879" y="0.02263948507607" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.00057395832845941" y="-0.99999982118607" z="0"/>
			<PosUV type="vector" x="0.23328693211079" y="0.69991755485535" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A40"/>
			<UID type="string" value="D4231MyApp3777"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="8">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D4231MyApp3777"/>
			<Color>
				<A type="number" value="0"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D4231D3086MyApp2319"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="0.2322142124176" y="-0.019334837794304" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.00057395832845941" y="0.99999982118607" z="0"/>
			<PosUV type="vector" x="0.23249757289886" y="0.74189233779907" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A40"/>
			<UID type="string" value="D4231MyApp3778"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A40"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="16.************" y="-578.61853027344" z="0"/>
	<zoomSize type="vector" x="1778.6627197266" y="1778.6617431641" z="1.5866750478745"/>
</root>
