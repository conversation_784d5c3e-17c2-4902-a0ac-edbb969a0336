<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="12.************"/>
			<AngleLocal type="number" value="12.************"/>
			<Lenght type="number" value="0.082618400454521"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_01_L"/>
			<Pos type="vector" x="0.83079528808594" y="0.54055136442184" z="0"/>
			<PosEnd type="vector" x="0.91143608093262" y="0.52258294820786" z="0"/>
			<PosLocal type="vector" x="0.83079528808594" y="0.54055136442184" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="D2117MyApp578"/>
				<Element index="2" type="string" value="D2117MyApp579"/>
				<Element index="3" type="string" value="D2117MyApp580"/>
				<Element index="4" type="string" value="D2117MyApp581"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A24"/>
			<UID type="string" value="D2117MyApp576"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="2">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-179.86644338388"/>
			<AngleLocal type="number" value="-179.86644338388"/>
			<Lenght type="number" value="0.071705028414726"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_01_R"/>
			<Pos type="vector" x="0.82459253072739" y="0.54081416130066" z="0"/>
			<PosEnd type="vector" x="0.75288772583008" y="0.54098129272461" z="0"/>
			<PosLocal type="vector" x="0.82459253072739" y="0.54081416130066" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="D2117MyApp582"/>
				<Element index="2" type="string" value="D2117MyApp583"/>
				<Element index="3" type="string" value="D2117MyApp584"/>
				<Element index="4" type="string" value="D2117MyApp585"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A24"/>
			<UID type="string" value="D2117MyApp577"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="3">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-138.72624059571"/>
			<AngleLocal type="number" value="-151.28771732981"/>
			<Lenght type="number" value="0.095459163188934"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_02_L"/>
			<ParentCut type="boolean" value="true"/>
			<ParentUID type="string" value="D2117MyApp576"/>
			<Pos type="vector" x="0.58927297592163" y="0.39456629753113" z="0"/>
			<PosEnd type="vector" x="0.51752907037735" y="0.45753663778305" z="0"/>
			<PosLocal type="vector" x="-0.28660959005356" y="-0.19501864910126" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="D2117MyApp604"/>
				<Element index="2" type="string" value="D2117MyApp605"/>
				<Element index="3" type="string" value="D2117MyApp606"/>
				<Element index="4" type="string" value="D2117MyApp607"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A24"/>
			<UID type="string" value="D2117MyApp602"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="4">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-96.************"/>
			<AngleLocal type="number" value="83.************"/>
			<Lenght type="number" value="0.10874492675066"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_02_R"/>
			<ParentCut type="boolean" value="true"/>
			<ParentUID type="string" value="D2117MyApp577"/>
			<Pos type="vector" x="0.64012783765793" y="0.25189009308815" z="0"/>
			<PosEnd type="vector" x="0.62766218185425" y="0.35991817712784" z="0"/>
			<PosLocal type="vector" x="0.11208567023277" y="0.28935328125954" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="D2117MyApp624"/>
				<Element index="2" type="string" value="D2117MyApp625"/>
				<Element index="3" type="string" value="D2117MyApp626"/>
				<Element index="4" type="string" value="D2117MyApp627"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A24"/>
			<UID type="string" value="D2117MyApp603"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="5">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-1.************"/>
			<AngleLocal type="number" value="136.81284367907"/>
			<Lenght type="number" value="0.10462049394846"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_03_L"/>
			<ParentCut type="boolean" value="false"/>
			<ParentUID type="string" value="D2117MyApp602"/>
			<Pos type="vector" x="0.53010249137878" y="0.45013442635536" z="0"/>
			<PosEnd type="vector" x="0.63466465473175" y="0.45362758636475" z="0"/>
			<PosLocal type="vector" x="-0.014332681894302" y="-0.0027308796998113" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp2128"/>
				<Element index="2" type="string" value="MyApp2129"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A24"/>
			<UID type="string" value="MyApp2127"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="6">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="153.92430304301"/>
			<AngleLocal type="number" value="250.50670687158"/>
			<Lenght type="number" value="0.088837824761868"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_03_R"/>
			<ParentCut type="boolean" value="false"/>
			<ParentUID type="string" value="D2117MyApp603"/>
			<Pos type="vector" x="0.62058228254318" y="0.3152804672718" z="0"/>
			<PosEnd type="vector" x="0.54078686237335" y="0.27623108029366" z="0"/>
			<PosLocal type="vector" x="-0.043531872332096" y="0.01215014141053" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp2177"/>
				<Element index="2" type="string" value="MyApp2178"/>
				<Element index="3" type="string" value="MyApp2179"/>
				<Element index="4" type="string" value="MyApp2180"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A24"/>
			<UID type="string" value="MyApp2176"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A24.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Cloak05"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2117MyApp579"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2117MyApp576"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.010526126250625" y="0.99994468688965" z="0"/>
					<Pos type="vector" x="-0.046811856329441" y="0.0086378324776888" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.20720063149929" y="-0.97829854488373" z="0"/>
			<PosUV type="vector" x="0.82514172792435" y="0.53296142816544" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A24"/>
			<UID type="string" value="D2117MyApp578"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2117MyApp578"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2117MyApp576"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="-0.43626847863197" y="-0.14975316822529" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.2174868285656" y="0.97606325149536" z="0"/>
			<PosUV type="vector" x="0.82818359136581" y="0.69455897808075" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A24"/>
			<UID type="string" value="D2117MyApp579"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2117MyApp581"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2117MyApp576"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="2.0607447624207" y="0.01036124676466" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.2174868285656" y="-0.97606325149536" z="0"/>
			<PosUV type="vector" x="0.99472194910049" y="0.49340981245041" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A24"/>
			<UID type="string" value="D2117MyApp580"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2117MyApp580"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2117MyApp576"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="1.9774289131165" y="-0.14697752892971" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.2174868285656" y="0.97606325149536" z="0"/>
			<PosUV type="vector" x="1.0222223997116" y="0.64847946166992" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A24"/>
			<UID type="string" value="D2117MyApp581"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="5">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2117MyApp583"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2117MyApp577"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-0.088086307048798" y="0.15347415208817" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.0023308196105063" y="0.99999725818634" z="0"/>
			<PosUV type="vector" x="0.83126646280289" y="0.69427317380905" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A24"/>
			<UID type="string" value="D2117MyApp582"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="6">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2117MyApp582"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2117MyApp577"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.20898514986038" y="-0.97791886329651" z="0"/>
					<Pos type="vector" x="-0.0473288372159" y="-0.0087182335555553" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.20670521259308" y="-0.97840321063995" z="0"/>
			<PosUV type="vector" x="0.82796591520309" y="0.53208804130554" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A24"/>
			<UID type="string" value="D2117MyApp583"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="7">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2117MyApp585"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2117MyApp577"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.74604666233063" y="0.66589373350143" z="0"/>
					<Pos type="vector" x="2.************" y="0.062535248696804" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.74449253082275" y="0.66763079166412" z="0"/>
			<PosUV type="vector" x="0.65597385168076" y="0.6037425994873" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A24"/>
			<UID type="string" value="D2117MyApp584"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="8">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2117MyApp584"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2117MyApp577"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.20898514986038" y="-0.97791886329651" z="0"/>
					<Pos type="vector" x="2.0780355930328" y="-0.066273376345634" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.20670521259308" y="-0.97840321063995" z="0"/>
			<PosUV type="vector" x="0.67543292045593" y="0.4748882651329" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A24"/>
			<UID type="string" value="D2117MyApp585"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="9">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2117MyApp605"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2117MyApp602"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.99166482686996" y="-0.12884446978569" z="0"/>
					<Pos type="vector" x="-0.037430573254824" y="-0.0095896571874619" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.66030883789063" y="-0.7509942650795" z="0"/>
			<PosUV type="vector" x="0.58563250303268" y="0.38500201702118" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A24"/>
			<UID type="string" value="D2117MyApp604"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="10">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2117MyApp604"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2117MyApp602"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.34536868333817" y="-0.93846708536148" z="0"/>
					<Pos type="vector" x="0.99831694364548" y="-0.0093509471043944" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.87863433361053" y="-0.4774954020977" z="0"/>
			<PosUV type="vector" x="0.5114814043045" y="0.45040279626846" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A24"/>
			<UID type="string" value="D2117MyApp605"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="11">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2117MyApp607"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2117MyApp602"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.98360604047775" y="0.18033081293106" z="0"/>
					<Pos type="vector" x="-0.027347650378942" y="0.0044113760814071" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.85820192098618" y="-0.************" z="0"/>
			<PosUV type="vector" x="0.59414499998093" y="0.39615964889526" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A24"/>
			<UID type="string" value="D2117MyApp606"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="12">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2117MyApp606"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2117MyApp602"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.9990109205246" y="-0.044465996325016" z="0"/>
					<Pos type="vector" x="1.4036552906036" y="0.013257663697004" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.78015547990799" y="0.6255858540535" z="0"/>
			<PosUV type="vector" x="0.49731478095055" y="0.49291896820068" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A24"/>
			<UID type="string" value="D2117MyApp607"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="13">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2117MyApp625"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2117MyApp603"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.70626664161682" y="0.70794600248337" z="0"/>
					<Pos type="vector" x="1.0979746580124" y="0.0066856569610536" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.6223184466362" y="0.78276425600052" z="0"/>
			<PosUV type="vector" x="0.63308244943619" y="0.37126857042313" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A24"/>
			<UID type="string" value="D2117MyApp624"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="14">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2117MyApp624"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2117MyApp603"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.98264342546463" y="-0.18550498783588" z="0"/>
					<Pos type="vector" x="-0.051345903426409" y="0.008121776394546" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.071639686822891" y="-0.99743056297302" z="0"/>
			<PosUV type="vector" x="0.64883613586426" y="0.24727430939674" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A24"/>
			<UID type="string" value="D2117MyApp625"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="15">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2117MyApp627"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2117MyApp603"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.9975146651268" y="0.070460349321365" z="0"/>
					<Pos type="vector" x="1.3023263216019" y="-0.013857504352927" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.044351287186146" y="0.99901604652405" z="0"/>
			<PosUV type="vector" x="0.61012732982635" y="0.39098939299583" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A24"/>
			<UID type="string" value="D2117MyApp626"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="16">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2117MyApp626"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2117MyApp603"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.41351953148842" y="-0.91049534082413" z="0"/>
					<Pos type="vector" x="0.063479699194431" y="-0.016255540773273" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.85709077119827" y="-0.51516556739807" z="0"/>
			<PosUV type="vector" x="0.62318813800812" y="0.25688427686691" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A24"/>
			<UID type="string" value="D2117MyApp627"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="17">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2129"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2127"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.64018255472183" y="0.76822286844254" z="0"/>
					<Pos type="vector" x="1.1583195924759" y="0.079037919640541" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.61417549848557" y="-0.78916943073273" z="0"/>
			<PosUV type="vector" x="0.65385788679123" y="0.3751867711544" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A24"/>
			<UID type="string" value="MyApp2128"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="18">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2128"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2127"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="1.0465116500854" y="-0.047449111938477" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.033388867974281" y="0.99944239854813" z="0"/>
			<PosUV type="vector" x="0.63794374465942" y="0.5012127161026" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A24"/>
			<UID type="string" value="MyApp2129"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="19">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2178"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2176"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.67195254564285" y="0.74059414863586" z="0"/>
					<Pos type="vector" x="1.1049439907074" y="0.043245028704405" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.92909157276154" y="0.36985009908676" z="0"/>
			<PosUV type="vector" x="0.51340413093567" y="0.31097638607025" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A24"/>
			<UID type="string" value="MyApp2177"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="20">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2177"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2176"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.50235116481781" y="-0.86466366052628" z="0"/>
					<Pos type="vector" x="0.54542768001556" y="-0.036312744021416" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.83128881454468" y="-0.55584073066711" z="0"/>
			<PosUV type="vector" x="0.59302121400833" y="0.26136523485184" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A24"/>
			<UID type="string" value="MyApp2178"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="21">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2180"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2176"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.97959196567535" y="0.20099675655365" z="0"/>
					<Pos type="vector" x="1.1779625415802" y="0.0020853844471276" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.9682332277298" y="-0.25004935264587" z="0"/>
			<PosUV type="vector" x="0.52566963434219" y="0.27115488052368" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A24"/>
			<UID type="string" value="MyApp2179"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="22">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2179"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2176"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="0.75000029802322" y="-0.050000015646219" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.4395580291748" y="-0.89821428060532" z="0"/>
			<PosUV type="vector" x="0.58271360397339" y="0.24108269810677" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A24"/>
			<UID type="string" value="MyApp2180"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A24"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="104.48303222656" y="-164.77264404297" z="0"/>
	<zoomSize type="vector" x="1448.7310791016" y="1448.7309570313" z="1.2923560142517"/>
</root>
