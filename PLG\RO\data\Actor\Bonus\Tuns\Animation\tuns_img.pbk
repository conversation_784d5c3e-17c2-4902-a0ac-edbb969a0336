<?xml version="1.0" ?>
<root>
	<List>
		<tuns_img1>
			<BonesListT>
				<Element index="1">
					<Alpha type="number" value="1"/>
					<AngleLocal type="number" value="0.82614438737254"/>
					<Lenght type="number" value="0.23787741363049"/>
					<Name type="string" value="root"/>
					<PosLocal type="vector" x="0.49921241402626" y="0.50460159778595" z="0"/>
					<Refs>
					</Refs>
					<RefsUID>
						<Element index="1" type="string" value="MyApp2962"/>
						<Element index="2" type="string" value="MyApp2963"/>
						<Element index="3" type="string" value="MyApp2964"/>
						<Element index="4" type="string" value="MyApp2965"/>
					</RefsUID>
					<ScaleX type="number" value="1"/>
					<ScaleY type="number" value="1"/>
					<TemplateUID type="string" value="tuns_img1"/>
					<UID type="string" value="MyApp2961"/>
					<Zorder type="number" value="0"/>
					<noBegin type="boolean" value="true"/>
					<noEnd type="boolean" value="true"/>
				</Element>
			</BonesListT>
			<Name type="string" value="tuns_img1"/>
			<PatchPointList>
				<Element index="1">
					<BrotherUID type="string" value="MyApp2963"/>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="MyApp2961"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="1" z="0"/>
							<Pos type="vector" x="-2.2264034748077" y="0.5192237496376" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.014418477192521" y="-0.99989593029022" z="0"/>
					<PosUV type="vector" x="-0.037830099463463" y="-0.0069320797920227" z="0"/>
					<TemplateUID type="string" value="tuns_img1"/>
					<UID type="string" value="MyApp2962"/>
					<Zorder type="number" value="0"/>
				</Element>
				<Element index="2">
					<BrotherUID type="string" value="MyApp2962"/>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="MyApp2961"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="-1" z="0"/>
							<Pos type="vector" x="-2.2913010120392" y="-0.46803814172745" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.014418477192521" y="0.99989593029022" z="0"/>
					<PosUV type="vector" x="-0.039031274616718" y="0.98044991493225" z="0"/>
					<TemplateUID type="string" value="tuns_img1"/>
					<UID type="string" value="MyApp2963"/>
					<Zorder type="number" value="0"/>
				</Element>
				<Element index="3">
					<BrotherUID type="string" value="MyApp2965"/>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="MyApp2961"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="1" z="0"/>
							<Pos type="vector" x="2.2489869594574" y="0.50786620378494" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="-0.014418477192521" y="-0.99989593029022" z="0"/>
					<PosUV type="vector" x="1.0268173217773" y="-0.010925561189651" z="0"/>
					<TemplateUID type="string" value="tuns_img1"/>
					<UID type="string" value="MyApp2964"/>
					<Zorder type="number" value="0"/>
				</Element>
				<Element index="4">
					<BrotherUID type="string" value="MyApp2964"/>
					<Hiden type="number" value="0"/>
					<LocalData>
						<Element index="1">
							<BoneUID type="string" value="MyApp2961"/>
							<Influence type="number" value="1"/>
							<Normale type="vector" x="0" y="-1" z="0"/>
							<Pos type="vector" x="2.2112379074097" y="-0.47692465782166" z="0"/>
						</Element>
					</LocalData>
					<NormaleUV type="vector" x="0.014418477192521" y="0.99989593029022" z="0"/>
					<PosUV type="vector" x="1.0320378541946" y="0.97389256954193" z="0"/>
					<TemplateUID type="string" value="tuns_img1"/>
					<UID type="string" value="MyApp2965"/>
					<Zorder type="number" value="0"/>
				</Element>
			</PatchPointList>
			<UID type="string" value="tuns_img1"/>
		</tuns_img1>
	</List>
	<MediaWidth type="number" value="1"/>
	<Ratio type="number" value="1"/>
</root>
