<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="90.************"/>
			<AngleLocal type="number" value="90.************"/>
			<Lenght type="number" value="0.11415082961321"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Foot_L"/>
			<Pos type="vector" x="0.61216539144516" y="0.69570589065552" z="0"/>
			<PosEnd type="vector" x="0.61155819892883" y="0.5815566778183" z="0"/>
			<PosLocal type="vector" x="0.61216539144516" y="0.69570589065552" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp2439"/>
				<Element index="2" type="string" value="MyApp2440"/>
				<Element index="3" type="string" value="MyApp2441"/>
				<Element index="4" type="string" value="MyApp2442"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A30"/>
			<UID type="string" value="MyApp2438"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A30.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Fx_Wrap_01"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2440"/>
			<Color>
				<A type="number" value="200"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2438"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.10506888478994" y="0.99446499347687" z="0"/>
					<Pos type="vector" x="-0.057331591844559" y="0.070963852107525" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.99500977993011" y="-0.09977762401104" z="0"/>
			<PosUV type="vector" x="0.54123735427856" y="0.70262771844864" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A30"/>
			<UID type="string" value="MyApp2439"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2439"/>
			<Color>
				<A type="number" value="200"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2438"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.29552000761032" y="-0.95533657073975" z="0"/>
					<Pos type="vector" x="-0.051181685179472" y="-0.077109277248383" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.95375120639801" y="-0.30059748888016" z="0"/>
			<PosUV type="vector" x="0.68930464982986" y="0.70113807916641" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A30"/>
			<UID type="string" value="MyApp2440"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2442"/>
			<Color>
				<A type="number" value="200"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2438"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.20897488296032" y="0.97792100906372" z="0"/>
					<Pos type="vector" x="1.020879149437" y="0.043899163603783" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.97901874780655" y="-0.20377014577389" z="0"/>
			<PosUV type="vector" x="0.56764698028564" y="0.57940685749054" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A30"/>
			<UID type="string" value="MyApp2441"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2441"/>
			<Color>
				<A type="number" value="200"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2438"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.29552000761032" y="-0.95533657073975" z="0"/>
					<Pos type="vector" x="1.0957989692688" y="-0.042577683925629" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.95375120639801" y="-0.30059748888016" z="0"/>
			<PosUV type="vector" x="0.65407711267471" y="0.57039481401443" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A30"/>
			<UID type="string" value="MyApp2442"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A30"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="-1000.3294677734" y="-1449.775390625" z="0"/>
	<zoomSize type="vector" x="2884.3559570313" y="2884.3557128906" z="2.7895112037659"/>
</root>
