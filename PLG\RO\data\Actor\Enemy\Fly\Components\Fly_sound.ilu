includeReference("Actor/Includes/Sound/sound_base.ilu")
component =
{
    NAME="SoundComponent_Template",
    SoundComponent_Template =
    {
        soundList =
        {
            -- {
                -- SoundDescriptor_Template=
                -- {
                    -- name="MRK_FlyPaf",
                    -- volume=-25,
			        -- category="Enemy",
			        -- limitCategory="paf_fly",
					-- params=MONO_3D_RANDOM_NOVOL_SMALLPITCH_NOFADE,
                    -- files=
                    -- {
				        -- { VAL = "Sound/Enemy/Fly/Fly_paf.wav" },
                    -- }
                -- }
            -- },
            -- {
                -- SoundDescriptor_Template=
                -- {
                    -- name="MRK_FlyMove",
                    -- volume=-36,
			        -- category="Enemy",
			        -- limitCategory="move_fly",
					-- params=MONO_3D_RANDOM_NOVOL_NOPITCH_NOFADE_LOOP,
                    -- files=
                    -- {
				        -- { VAL = "Sound/Enemy/Fly/Fly_move_loop.wav", },
                    -- }
                -- }
            -- },
            
        },
    }
}

includeReference("Actor/Includes/Sound/hit_rayman.ilu")
includeReference("Actor/Includes/Sound/hit_globox.ilu")
includeReference("Actor/Includes/Sound/hit_livingstone.ilu")

appendTable(component.SoundComponent_Template.soundList,HIT_RAYMAN)
appendTable(component.SoundComponent_Template.soundList,HIT_GLOBOX)
appendTable(component.SoundComponent_Template.soundList,HIT_LIVINGSTONE)

appendTable(params.Actor_Template.COMPONENTS,{component})

component = {}
