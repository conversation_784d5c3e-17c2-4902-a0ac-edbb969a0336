<?xml version="1.0" ?>
<root>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<AngleLocal type="number" value="-179.14133431138"/>
			<Lenght type="number" value="0.12494470179081"/>
			<Name type="string" value="B_bird01"/>
			<ParentCut type="boolean" value="true"/>
			<ParentUID type="string" value="MyApp3297"/>
			<PosLocal type="vector" x="-0.12862476706505" y="-0.21539697051048" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Bird_Template1"/>
			<UID type="string" value="D3295D3293D3291D3289D3287D3285MyApp3273"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="2">
			<Alpha type="number" value="1"/>
			<AngleLocal type="number" value="1.9442272824535"/>
			<Lenght type="number" value="0.075004674494267"/>
			<Name type="string" value="root"/>
			<PosLocal type="vector" x="0.94916665554047" y="0.63249999284744" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Bird_Template1"/>
			<UID type="string" value="MyApp3297"/>
			<Zorder type="number" value="0"/>
		</Element>
		<Element index="3">
			<Alpha type="number" value="1"/>
			<AngleLocal type="number" value="-179.14133431138"/>
			<Lenght type="number" value="0.12494470179081"/>
			<Name type="string" value="B_bird02"/>
			<ParentCut type="boolean" value="true"/>
			<ParentUID type="string" value="MyApp3297"/>
			<PosLocal type="vector" x="-0.07807232439518" y="-0.16595554351807" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Bird_Template1"/>
			<UID type="string" value="D3370D3295D3293D3291D3289D3287D3285MyApp3273"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="4">
			<Alpha type="number" value="1"/>
			<AngleLocal type="number" value="-179.14133431138"/>
			<Lenght type="number" value="0.12494470179081"/>
			<Name type="string" value="B_bird03"/>
			<ParentCut type="boolean" value="true"/>
			<ParentUID type="string" value="MyApp3297"/>
			<PosLocal type="vector" x="-0.023613922297955" y="0.1187442690134" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Bird_Template1"/>
			<UID type="string" value="D3371D3295D3293D3291D3289D3287D3285MyApp3273"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="5">
			<Alpha type="number" value="1"/>
			<AngleLocal type="number" value="-179.14133431138"/>
			<Lenght type="number" value="0.12494470179081"/>
			<Name type="string" value="B_bird04"/>
			<ParentCut type="boolean" value="true"/>
			<ParentUID type="string" value="MyApp3297"/>
			<PosLocal type="vector" x="0.023032508790493" y="-0.067072734236717" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Bird_Template1"/>
			<UID type="string" value="D3372D3295D3293D3291D3289D3287D3285MyApp3273"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="6">
			<Alpha type="number" value="1"/>
			<AngleLocal type="number" value="-179.14133431138"/>
			<Lenght type="number" value="0.12494470179081"/>
			<Name type="string" value="B_bird05"/>
			<ParentCut type="boolean" value="true"/>
			<ParentUID type="string" value="MyApp3297"/>
			<PosLocal type="vector" x="0.073584891855717" y="-0.017631327733397" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Bird_Template1"/>
			<UID type="string" value="D3373D3295D3293D3291D3289D3287D3285MyApp3273"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="7">
			<Alpha type="number" value="1"/>
			<AngleLocal type="number" value="-179.14133431138"/>
			<Lenght type="number" value="0.12494470179081"/>
			<Name type="string" value="B_bird06"/>
			<ParentCut type="boolean" value="true"/>
			<ParentUID type="string" value="MyApp3297"/>
			<PosLocal type="vector" x="0.12413727492094" y="0.031810075044632" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Bird_Template1"/>
			<UID type="string" value="D3374D3295D3293D3291D3289D3287D3285MyApp3273"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="8">
			<Alpha type="number" value="1"/>
			<AngleLocal type="number" value="-179.14133431138"/>
			<Lenght type="number" value="0.12494470179081"/>
			<Name type="string" value="B_bird07"/>
			<ParentCut type="boolean" value="true"/>
			<ParentUID type="string" value="MyApp3297"/>
			<PosLocal type="vector" x="0.17468965053558" y="0.081251479685307" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Bird_Template1"/>
			<UID type="string" value="D3375D3295D3293D3291D3289D3287D3285MyApp3273"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<MainPath type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/Bird/animation/Bird_Template.ptc"/>
	<Name type="string" value="Bird_Template1"/>
	<PatchPointList>
	</PatchPointList>
	<UID type="string" value="Bird_Template1"/>
</root>
