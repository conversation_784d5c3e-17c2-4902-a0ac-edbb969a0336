<?xml version="1.0" ?>
<root>
	<AnimsList>
		<Element index="1" type="string" value="Actor/Friendly/Bird/animation/Bir_Cine.anm"/>
		<Element index="2" type="string" value="Actor/Friendly/Bird/animation/Bir_Cine_02.anm"/>
		<Element index="3" type="string" value="Actor/Friendly/Bird/animation/Bir_Fly_01_L.anm"/>
		<Element index="4" type="string" value="Actor/Friendly/Bird/animation/Bir_Fly_Back.anm"/>
		<Element index="5" type="string" value="Actor/Friendly/Bird/animation/Bir_Fly_Down_L.anm"/>
		<Element index="6" type="string" value="Actor/Friendly/Bird/animation/Bir_Fly_Down_R.anm"/>
		<Element index="7" type="string" value="Actor/Friendly/Bird/animation/Bir_Fly_Front.anm"/>
		<Element index="8" type="string" value="Actor/Friendly/Bird/animation/Bir_Fly_Straight_L.anm"/>
		<Element index="9" type="string" value="Actor/Friendly/Bird/animation/Bir_Fly_Straight_R.anm"/>
		<Element index="10" type="string" value="Actor/Friendly/Bird/animation/Bir_Fly_Up_L.anm"/>
		<Element index="11" type="string" value="Actor/Friendly/Bird/animation/Bir_Fly_Up_R.anm"/>
	</AnimsList>
	<PatchBankList>
		<birdy type="string" value="Scene/Demo/Demo_Video_Market/Actor/Bird/animation/birdy.tga"/>
		<birdy_B type="string" value="Scene/Demo/Demo_Video_Market/Actor/Bird/animation/birdy_B.tga"/>
	</PatchBankList>
	<Scale type="number" value="1"/>
	<SceneVersion type="number" value="19"/>
	<Squeleton type="string" value="Scene/Demo/Demo_Video_Market/Actor/Bird/animation/Bird_Template1.skl"/>
	<UseDataFolder type="boolean" value="true"/>
	<UseRelative type="boolean" value="true"/>
</root>
