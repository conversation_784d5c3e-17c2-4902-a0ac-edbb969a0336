includeReference("Actor/Includes/helpers.ilu")
includeReference("Actor/Includes/gameplay_types.ilu")

params =
{
    
NAME = "Actor_Template",
Actor_Template =
    {
        SCALE = vector2dNew(3.0, 3.0),
        RANK = 0,
        COMPONENTS =
        {
        }
    }
}

includeReference("Actor/Enemy/ShooterGunner/tests/ShooterGunnerActor_AI.ilu")
includeReference("Actor/Enemy/ShooterGunner/tests/ShooterGunnerActor_Anim.ilu")

includeReference("Actor/Enemy/Ant_BasicSoldier/Components/Ant_BasicSoldier_sound.ilu")
includeReference("Actor/Enemy/Ant_BasicSoldier/Components/Ant_BasicSoldier_fxController.ilu")
includeReference("Actor/Enemy/Common/Components/FxBankComponent.ilu")
