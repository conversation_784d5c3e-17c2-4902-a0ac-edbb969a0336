<?xml version="1.0" ?>
<root>
	<AnimsList>
		<Element index="1" type="string" value="Actor/Friendly/RedWizard/redwizard_fakir/animation/Boucle_Reaction_Squash.anm"/>
		<Element index="2" type="string" value="Actor/Friendly/RedWizard/redwizard_fakir/animation/Boucle_Reaction_Squash_02.anm"/>
		<Element index="3" type="string" value="Actor/Friendly/RedWizard/redwizard_fakir/animation/Boucle_reaction_Hang.anm"/>
		<Element index="4" type="string" value="Actor/Friendly/RedWizard/redwizard_fakir/animation/Boucle_reaction_Hang_02.anm"/>
		<Element index="5" type="string" value="Actor/Friendly/RedWizard/redwizard_fakir/animation/Reaction_Hang.anm"/>
		<Element index="6" type="string" value="Actor/Friendly/RedWizard/redwizard_fakir/animation/Squash.anm"/>
		<Element index="7" type="string" value="Actor/Friendly/RedWizard/redwizard_fakir/animation/Stand.anm"/>
		<Element index="8" type="string" value="Actor/Friendly/RedWizard/redwizard_fakir/animation/Stand_SitDown.anm"/>
	</AnimsList>
	<PatchBankList>
		<RedWizard_Fakir_A type="string" value="Actor/Friendly/RedWizard/redwizard_fakir/animation/RedWizard_Fakir_A.png"/>
		<RedWizard_Fakir_B type="string" value="Actor/Friendly/RedWizard/redwizard_fakir/animation/RedWizard_Fakir_B.tga"/>
		<RedWizard_Fakir_C type="string" value="Actor/Friendly/RedWizard/redwizard_fakir/animation/RedWizard_Fakir_C.tga"/>
	</PatchBankList>
	<Scale type="number" value="1"/>
	<SceneVersion type="number" value="20"/>
	<Squeleton type="string" value="Actor/Friendly/RedWizard/redwizard_fakir/animation/RedWizard_Fakir_Squeleton.skl"/>
	<UseDataFolder type="boolean" value="true"/>
	<UseRelative type="boolean" value="true"/>
</root>
