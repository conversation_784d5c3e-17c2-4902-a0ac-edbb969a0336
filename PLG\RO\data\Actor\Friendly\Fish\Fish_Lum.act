includeReference("Actor/Includes/gameplay_types.ilu")

params =
{
    
NAME = "Actor_Template",
Actor_Template =
    {
        SCALE = vector2dNew(3.0,3.0),
        STARTPAUSED = 1,
        COMPONENTS =
        {
            {
                NAME="AnimLightComponent_Template",
                AnimLightComponent_Template =
                {
                    defaultAnimation = "Idle",
                    animSet=
                    {
						SubAnimSet_Template =
                        {
		                    animations =
		                    {
		                        {
		                            SubAnim_Template =
		                            {
		                                friendlyName = "Idle",
		                                name = "Actor/friendly/Fish/Animation/fishanim2.anm",
		                                loop = 1,
		                            },
		                        },
		                        {
		                            SubAnim_Template =
		                            {
		                                friendlyName = "receiveHit",
		                                name = "Actor/friendly/Fish/Animation/fishanim2.anm",
                                        markerStart="MRK_HitReceive_Start",
                                        markerStart="MRK_HitReceive_Stop",
		                            },
		                        },
		                        {
		                            SubAnim_Template =
		                            {
		                                friendlyName = "death",
		                                name = "Actor/friendly/Fish/Animation/death.anm",
		                            },
		                        },
		                    },
						}, 
					},
                    },
                },
            {
                NAME="StickToPolylinePhysComponent_Template",
                StickToPolylinePhysComponent_Template =
                { 
                    physRadius = 0.167,                             -- The radius of the collision
                    physFriction = 1.5,                            -- The character friction
                    physUnstickMinAngle = 10,                     -- Minimum angle between speed and edge to unstick from edge
                    physUnstickMaxAngle = 40,                     -- Maximum angle between speed and edge to unstick from edge
                    physUnstickMinAngleSpeed = 10,                -- Speed to unstick from edge at minimum angle between edge and speed
                    physUnstickMaxAngleSpeed = 0,                 -- Speed to unstick from edge at maximum angle between edge and speed
                    physAngularSpeedMinLinear = 0,                -- Minimum linear speed to translate to angular speed
                    physAngularSpeedMaxLinear = 100,               -- Maximum linear speed to translate to angular speed
                    physAngularSpeedMinAngular = 100.0,           -- Angular speed at minimum linear speed
                    physAngularSpeedMaxAngular = 500.0,           -- Angular speed at maximum linear speed
                    physLandSpeedLossMinAngle=0,				     -- : Angle difference for minimum speed loss coefficient when landing on a surface
                    physLandSpeedLossMaxAngle=90,			     -- : Angle difference for maximum speed loss coefficient when landing on a surface
                    physLandSpeedLossMinLoss=0,			         -- : Loss at minimum angle difference when landing on a surface
                    physLandSpeedLossMaxLoss=0.1,			     -- : Loss at maximum angle difference when landing on a surface
                    physWeight = 1.0,                             -- Weight of rayman
                    physEjectMultiplier = 50.0,                   -- Multiplier to the force applied when we are on a moving platform that changed direction
                    physEjectThreshold = 5.0,                     -- The eject force is not applied if the speed of the platform is not bigger than this
                    physDefaultStickMin =183, 			         -- min angle for default sticky area (0-360)
                    physDefaultStickMax =355,			         -- mAX angle for default sticky area (0-360)
                }
            },
            {
                NAME="Ray_FishAIComponent",
                Ray_FishAIComponent =
                {
                    genericBehavior = 
                    {
                        NAME = "AISimplePlayAnimBehavior_Template",
                        AISimplePlayAnimBehavior_Template =
                        {
                            playAnim = 
                            {
                                NAME="AIPlayAnimAction_Template",
                                AIPlayAnimAction_Template=
                                {
                                    action = "IDLE",
                                }
                            },
                        },
                    },
                    deathBehavior =
                    {
                        NAME = "Ray_AIDeathBehavior_Template",
                        Ray_AIDeathBehavior_Template =
                        {
                            pauseActorWhenDone = 0,
                            resetActorWhenDone = 0,
                            destroyActorWhenDone = 1,
                            deactivatePhysics = 0,
                            nullWeight = 1,
                            reward = 
                                {NAME="Ray_EventSpawnRewardLum",
                                Ray_EventSpawnRewardLum=
                                {
									friendly="basiclums2",
                                    numRewards=1,
                                    autoPickup = 0,
                                }},
                            actions =
                            {
                                {NAME="AIPlayAnimAction_Template",
                                AIPlayAnimAction_Template =
                                {
                                    action = "death"
                                }},
                                {NAME="AIFadeAction_Template",
                                AIFadeAction_Template =
                                {
                                    visible = 0,
                                    fadeDuration = 2,
                                }},
                            },
                        }
                    },
                    receiveHitBehavior =
                        {NAME="Ray_AIReceiveHitBehavior_Template",
                        Ray_AIReceiveHitBehavior_Template =
                        {
                            receiveHits =
                            {
                                -- all types, all levels
                                {ReceiveHitData =
                                {
                                    action = 
                                        {NAME="Ray_AIGround_ReceiveNormalHitAction_Template",
                                        Ray_AIGround_ReceiveNormalHitAction_Template =
                                        {
                                            action="receiveHit",
                                            normalHitEjectForce = 400,
                                            frictionMultiplier = 0.0,
                                        }},
                                }},
                            },
                        }},

                    health = 1,
                    damageLevels =
                    {
                        { VAL = 25 },
                        { VAL = 50 },
                        { VAL = 100 },
                    },
                }
            },
            {
                NAME = "PhantomComponent_Template",
                PhantomComponent_Template =
                {
                    collisionGroup = CollisionGroup.Character,
                    -- drawDebug = 1,
                    shape =
                    {
                        NAME = "PhysShapePolygon",
                        PhysShapePolygon =
                        {
                            Points =
                            {
                                { VAL = vector2dNew(-1.0,-1.0) },
                                { VAL = vector2dNew(-1.0,1.0) },
                                { VAL = vector2dNew(1.0,1.0) },
                                { VAL = vector2dNew(1.0,-1.0) },
                            },
                        }
                    },
                },
            },
		},
	},
}

