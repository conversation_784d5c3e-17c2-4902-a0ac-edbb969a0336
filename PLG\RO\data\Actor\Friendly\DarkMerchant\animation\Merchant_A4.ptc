<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="89.************"/>
			<AngleLocal type="number" value="89.************"/>
			<Lenght type="number" value="0.053428821265697"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Pelvis"/>
			<Pos type="vector" x="0.78684097528458" y="0.36132219433784" z="0"/>
			<PosEnd type="vector" x="0.7871687412262" y="0.30789437890053" z="0"/>
			<PosLocal type="vector" x="0.78684097528458" y="0.36132219433784" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp385"/>
				<Element index="2" type="string" value="MyApp386"/>
				<Element index="3" type="string" value="MyApp387"/>
				<Element index="4" type="string" value="MyApp388"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A4"/>
			<UID type="string" value="MyApp383"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="2">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="88.************"/>
			<AngleLocal type="number" value="-0.7342559040932"/>
			<Lenght type="number" value="0.067550905048847"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Spine"/>
			<ParentUID type="string" value="MyApp383"/>
			<Pos type="vector" x="0.7871687412262" y="0.30789437890053" z="0"/>
			<PosEnd type="vector" x="0.78844875097275" y="0.24035561084747" z="0"/>
			<PosLocal type="vector" x="0" y="0" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp389"/>
				<Element index="2" type="string" value="MyApp390"/>
				<Element index="3" type="string" value="MyApp391"/>
				<Element index="4" type="string" value="MyApp392"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A4"/>
			<UID type="string" value="MyApp384"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A4.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Body01"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp386"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp383"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-0.3495899438858" y="0.052913505584002" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.99998116493225" y="-0.0061346278525889" z="0"/>
			<PosUV type="vector" x="0.73381388187408" y="0.3796754181385" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A4"/>
			<UID type="string" value="MyApp385"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp385"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp383"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="-0.34502908587456" y="-0.047393098473549" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99998116493225" y="0.0061346278525889" z="0"/>
			<PosUV type="vector" x="0.83412009477615" y="0.************" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A4"/>
			<UID type="string" value="MyApp386"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp388"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp383"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="0.028085956349969" y="0.053060483187437" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.99998116493225" y="-0.0061346278525889" z="0"/>
			<PosUV type="vector" x="0.73379069566727" y="0.35949611663818" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A4"/>
			<UID type="string" value="MyApp387"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp387"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp383"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="0.037637893110514" y="-0.047443509101868" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99998116493225" y="0.0061346278525889" z="0"/>
			<PosUV type="vector" x="0.83429592847824" y="0.35960233211517" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A4"/>
			<UID type="string" value="MyApp388"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="5">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp390"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp384"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="0.92685425281525" y="0.024639571085572" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.99982047080994" y="-0.018948819488287" z="0"/>
			<PosUV type="vector" x="0.76371997594833" y="0.24482889473438" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A4"/>
			<UID type="string" value="MyApp389"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="6">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp389"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp384"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="0.92276501655579" y="-0.02619013376534" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99982047080994" y="0.018948819488287" z="0"/>
			<PosUV type="vector" x="0.81453531980515" y="0.24606823921204" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A4"/>
			<UID type="string" value="MyApp390"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="7">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp392"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp384"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="1.0631800889969" y="0.023518241941929" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.99982047080994" y="-0.018948819488287" z="0"/>
			<PosUV type="vector" x="0.76501560211182" y="0.23564286530018" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A4"/>
			<UID type="string" value="MyApp391"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="8">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp391"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp384"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="1.0777624845505" y="-0.024664720520377" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99982047080994" y="0.018948819488287" z="0"/>
			<PosUV type="vector" x="0.81320858001709" y="0.23557099699974" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A4"/>
			<UID type="string" value="MyApp392"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A4"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="-962.74560546875" y="-27.082702636719" z="0"/>
	<zoomSize type="vector" x="2360.6860351563" y="2360.6862792969" z="2.2720749378204"/>
</root>
