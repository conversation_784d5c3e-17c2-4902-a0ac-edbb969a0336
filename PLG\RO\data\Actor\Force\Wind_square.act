params =
{
    
NAME = "Actor_Template",
Actor_Template =
    {
        COMPONENTS =
        {
            {
                NAME="WindComponent_Template",
                WindComponent_Template =
                {
                    windAreas =
                    {
                        {
                            PhysForceModifier =
                            {
                                force=vector2dNew(-30.0,0.0),
                                Type = 1,
                                point=0,
                                inverted=0,
                                gradientPercentage=0.5,
                                --speedMultiplierX = 0.99,
                                Box =
                                {
                                    BoxData =
                                    {
                                        width=12.0,
                                        height=8.0,
                                    }
                                }
                            },
                        }
                    }
                }
            }
        }
    }
}
