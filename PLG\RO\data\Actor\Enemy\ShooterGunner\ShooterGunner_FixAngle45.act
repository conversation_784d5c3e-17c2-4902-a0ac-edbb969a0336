includeReference("Actor/Includes/helpers.ilu")
includeReference("Actor/Includes/gameplay_types.ilu")

params =
{
    
NAME = "Actor_Template",
Actor_Template =
    {
        SCALE = vector2dNew(2.5, 2.5),
        RANK = 0,
        COMPONENTS =
        {
        }
    }
}

_ForceFixAngle = 1
_ForceFixAngleVal = 45.0
_ForceFixAngleRight = 0

includeReference("Actor/Enemy/ShooterGunner/Components/ShooterGunner_AI.ilu")
includeReference("Actor/Enemy/ShooterGunner/Components/ShooterGunner_Anim.ilu")

--includeReference("Actor/Enemy/Ant_BasicSoldier/Components/Ant_BasicSoldier_sound.ilu")
includeReference("Actor/Enemy/Ant_BasicSoldier/Components/Ant_BasicSoldier_fxController.ilu")
includeReference("Actor/Enemy/Common/Components/FxBankComponent.ilu")
