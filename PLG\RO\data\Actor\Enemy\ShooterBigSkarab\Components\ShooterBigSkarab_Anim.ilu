includeReference("Actor/Includes/gameplay_types.ilu")

component = {
NAME="AnimatedComponent_Template",
AnimatedComponent_Template = 
{
    posOffset = vector2dNew(0, 0),
    
    animationPath = "Actor/Enemy/ShooterBigSkarab/Animation/",
    defaultAnimation = "IDLE",
    -- flip = 1,
    
    animSet = {SubAnimSet_Template =
    {
        animations=
        {
            {SubAnim_Template={ friendlyName="Idle", name="Stand.anm", loop=1, }},
            {SubAnim_Template={ friendlyName="Walk", name="Walk.anm", loop=1, }},
        },
    }},
    
    inputs =
    {
    },
    
    tree = 
    {
        AnimTree_Template =
        {
            nodes =
            {
                {
                    NAME="AnimTreeNodePlayAnim_Template",
                    AnimTreeNodePlayAnim_Template =
                    {
                        nodeName = "IDLE",
                        animationName = "Idle"
                    }
                },
                {
                    NAME="AnimTreeNodePlayAnim_Template",
                    AnimTreeNodePlayAnim_Template =
                    {
                        nodeName = "WALK",
                        animationName = "Walk"
                    }
                },
            },
        }
    },
    
}}

appendTable(params.Actor_Template.COMPONENTS,{component})
component = {}

