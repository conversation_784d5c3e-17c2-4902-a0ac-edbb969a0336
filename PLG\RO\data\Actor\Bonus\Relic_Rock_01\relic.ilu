includeReference("Actor/Includes/helpers.ilu")
includeReference("Actor/Includes/gameplay_types.ilu")

params =
{
    
NAME = "Actor_Template",
Actor_Template =
    {
        SCALE = vector2dNew(1,1),
        RANK = 0,
		useZForced = 1, zForced = 0.1,
		STARTPAUSED=0,
        COMPONENTS =
        {
            {
                NAME="AnimLightComponent_Template",
                AnimLightComponent_Template=
                {
					animationPath = "Actor/Bonus/Relic_Rock_01/animation/",
                    defaultAnimation = "Stand",
					
                    animSet=
                    {
						SubAnimSet_Template =
                        {
							--
		                    animations=
		                    {
		                        {
		                            SubAnim_Template=
		                            {
		                                friendlyName="Appears",
		                                name="Stand.anm",
										markerStart="MRK_Out_Start",
										markerStop="MRK_Out_Stop",
		                                loop=0
		                            },
								},
								{
		                            SubAnim_Template=
		                            {
		                                friendlyName="Stand",
		                                name="Stand.anm",
										markerStart="MRK_Stand_Start",
										markerStop="MRK_Stand_Stop",
		                                loop=1
		                            },
								},

		                    },
							
							--
							banks =
							{
								--State 0
								{
									BankChange_Template =
									{
										bankName = "Graphic_BasicAnimation",
										bankPath = "actor/bonus/relic_rock_01/animation/Relic_Rock_01_A.tga",
									},
								},
								--State 1
								{
									BankChange_Template =
									{
										bankName = "Graphic_BasicAnimation",
										bankPath = "actor/bonus/relic_rock_01/animation/Relic_Rock_02_A.tga",
										state=1,
									},
								},
								--State 2
								{
									BankChange_Template =
									{
										bankName = "Graphic_BasicAnimation",
										bankPath = "actor/bonus/relic_rock_01/animation/Relic_Rock_03_A.tga",
										state=2,
									},
								},
								
							},			
							
						}
					}

                }
            },
			
            {
                NAME = "PlayerDetectorComponent_Template",
                PlayerDetectorComponent_Template =
                {
                    Shape =
                    {
                        NAME = "PhysShapeCircle",
                        PhysShapeCircle = 
                        { 
                            radius = 0.5,
                        }
                    },
                },
            },
            {
                NAME="TriggerComponent_Template",
                TriggerComponent_Template=
                {
                    TriggerOnce = 1,
                    TriggerSelf = 1,
                    TriggerChildren = 0,
                    TriggerActivator = 0,
                    ResetOnCheckpoint = 0,
                    onEnterEvent = 
                    {
                        NAME="Ray_EventRewardPickedUp",
                        Ray_EventRewardPickedUp = 
                        {
                        }
                    },
                }
            },

			
			
            {
                NAME="Ray_FixedAIComponent_Template",
                Ray_FixedAIComponent_Template=
                {
					genericBehavior =
						{NAME="Ray_AIRelicBehavior_Template",
						Ray_AIRelicBehavior_Template =
						{
							relicIndex = RELIC_INDEX,
							medalPath = "Actor/Bonus/Relics/RelicMedallion3.act"						
						}}
                }
            },
			
		
			
        }
    }
}

includeReference("FX/Test_Rought/star.ilu")
