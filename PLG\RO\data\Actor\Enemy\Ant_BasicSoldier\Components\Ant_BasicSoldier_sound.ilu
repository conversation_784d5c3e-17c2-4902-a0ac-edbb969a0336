includeReference("Actor/Includes/Sound/sound_base.ilu")
component =
{
    NAME="SoundComponent_Template",
    SoundComponent_Template =
    {
        soundList =
        {
            {
                SoundDescriptor_Template=
                {
                    name="MRK_AntFoot",
                    volume=-24,
			        category="Enemy",
					limitCategory="foot_ant",
                    params = MONO_3D_RANDOM_NOVOL_SMALLPITCH_NOFADE,
                    files=
                    {
				        { VAL = "Sound/900_Temp/Ant_BasicSoldier/Fol_Ant_Foot_01.wav" },
				        { VAL = "Sound/900_Temp/Ant_BasicSoldier/Fol_Ant_Foot_02.wav" },
				        { VAL = "Sound/900_Temp/Ant_BasicSoldier/Fol_Ant_Foot_03.wav" },
				        { VAL = "Sound/900_Temp/Ant_BasicSoldier/Fol_Ant_Foot_04.wav" },
				        { VAL = "Sound/900_Temp/Ant_BasicSoldier/Fol_Ant_Foot_05.wav" },
				        { VAL = "Sound/900_Temp/Ant_BasicSoldier/Fol_Ant_Foot_06.wav" },
				        { VAL = "Sound/900_Temp/Ant_BasicSoldier/Fol_Ant_Foot_07.wav" },
                    }
                }
            },
            {
                SoundDescriptor_Template=
                {
                    name="MRK_AntPaf",
                    volume=-24,
			        category="Enemy",
			        limitCategory="voice_ant",
                    params = MONO_3D_RANDOM_NOVOL_SMALLPITCH_NOFADE,
                    files=
                    {
				        { VAL = "Sound/900_Temp/Ant_BasicSoldier/Vox_Ant_Hit_01.wav" },
				        { VAL = "Sound/900_Temp/Ant_BasicSoldier/Vox_Ant_Hit_02.wav" },
				        { VAL = "Sound/900_Temp/Ant_BasicSoldier/Vox_Ant_Hit_03.wav" },
                    }
                }
            },
            {
                SoundDescriptor_Template=
                {
                    name="MRK_Eject",
                    volume=-10,
			        category="Enemy",
			        limitCategory="voice_eject_ant",
                    params = MONO_3D_EJECT,
                    files=
                    {
						 {VAL="Sound/900_Temp/Ant_BasicSoldier/Vox_Ant_Eject.wav"},
                    }
                }
            },
        }
    }
}

includeReference("Actor/Includes/Sound/hit_rayman.ilu")
includeReference("Actor/Includes/Sound/hit_globox.ilu")
includeReference("Actor/Includes/Sound/hit_livingstone.ilu")

appendTable(component.SoundComponent_Template.soundList,HIT_RAYMAN)
appendTable(component.SoundComponent_Template.soundList,HIT_GLOBOX)
appendTable(component.SoundComponent_Template.soundList,HIT_LIVINGSTONE)

appendTable(params.Actor_Template.COMPONENTS,{component})

component = {}
