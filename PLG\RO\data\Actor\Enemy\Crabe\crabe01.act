params =
{
    
NAME = "Actor_Template",
Actor_Template =
    {
        SCALE = vector2dNew(2.0,2.0),
        COMPONENTS =
        {
            {
                NAME="AnimLightComponent_Template",
                AnimLightComponent_Template =
                {
                    animSet=
                    {
						SubAnimSet_Template =
                        {
							animations=
							{
								{
									SubAnim_Template=
									{
										friendlyName="Idle",
										name="Actor/Enemy/Crabe/Animation/Friz_A.anm",
										loop=1
									}
								}
							},
						},
					},
					defaultAnimation = "Idle",		
				},
			},
            {
                NAME="RenderSimpleAnimComponent_Template",
                RenderSimpleAnimComponent_Template={}
            },
        },
    }
}
