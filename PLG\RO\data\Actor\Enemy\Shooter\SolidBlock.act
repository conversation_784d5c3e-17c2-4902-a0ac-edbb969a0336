params =
{
    
NAME = "Actor_Template",
Actor_Template =
    {
        SCALE = vector2dNew(3.0,3.0),
        COMPONENTS =
        {
            {NAME="AnimLightComponent_Template",
            AnimLightComponent_Template=
            {
                animationPath = "Personal/Colas/TestAnim/bloc/Animation/",
                defaultAnimation = "Idle",
                animSet = {SubAnimSet_Template =
                {
                    animations =
                    {
                        {SubAnim_Template={ friendlyName="Idle", name="blocAnim1.anm", loop=1, }},
                    }
                }},
            }},
            {NAME="SolidPolylineComponent_Template",
            SolidPolylineComponent_Template=
            {
                polylines =
                {
                    {PolyData =
                    {
                        friction = 1.0,
                        speedLoss = 1.0,
                        --gameMaterial = "GameMaterial/Stone.gmt",
                        bones =
                        {
                            {PolylineBoneData ={Name = "col1", }},
                            {PolylineBoneData ={Name = "col2", }},
                            {PolylineBoneData ={Name = "col3", }},
                            {PolylineBoneData ={Name = "col4", }},
                            {PolylineBoneData ={Name = "col4*", }},
                        },
                    }},
                },
                solidEdges =
                {
                    -- bottom (normal is down)
                    {SolidEdgeData =
                    {
                        bone = "col4",
                        minDelta = 0.7,
                    }},
                },
            }},
            {NAME="RenderSimpleAnimComponent_Template",
            RenderSimpleAnimComponent_Template=
            {
            }},
        },
    }
}
