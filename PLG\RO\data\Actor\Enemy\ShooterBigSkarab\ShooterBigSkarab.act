includeReference("Actor/Includes/helpers.ilu")
includeReference("Actor/Includes/gameplay_types.ilu")

params =
{
    
NAME = "Actor_Template",
Actor_Template =
    {
        SCALE = vector2dNew(8.0, 8.0),
        RANK = 10,
        COMPONENTS =
        {
            {NAME = "ActorSpawnComponent_Template",
            ActorSpawnComponent_Template =
            {
                spawnActors = 
                {
                    {SpawnData =
                    {
                        actorLua = "Actor/Enemy/ShooterGunner/ShooterGunner_rank1.act",
                        spawnActorBoneName = "gunner_a",
						useParentScale = 0,
                    }},
                    {SpawnData =
                    {
                        actorLua = "Actor/Enemy/ShooterGunner/ShooterGunner_rank2.act",
                        spawnActorBoneName = "gunner_b",
						useParentScale = 0,
                    }},
                    {SpawnData =
                    {
                        actorLua = "Actor/Enemy/ShooterGunner/ShooterGunner_rank3.act",
                        spawnActorBoneName = "gunner_c",
						useParentScale = 0,
                    }},
                },
            }},

        }
    }
}

includeReference("Actor/Enemy/ShooterBigSkarab/Components/ShooterBigSkarab_Stick.ilu")
includeReference("Actor/Enemy/ShooterBigSkarab/Components/ShooterBigSkarab_AI.ilu")
includeReference("Actor/Enemy/ShooterBigSkarab/Components/ShooterBigSkarab_Anim.ilu")
