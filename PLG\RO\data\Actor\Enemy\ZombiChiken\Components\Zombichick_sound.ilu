includeReference("Actor/Includes/Sound/sound_base.ilu")

component = 
{
    NAME="SoundComponent_Template",
    SoundComponent_Template = 
	{
        soundList = 
		{
            {SoundDescriptor_Template=
				{   
					name="MRK_Zombichicken",
					volume=-21,
					category="Bonus",
					params = MONO_3D_RANDOM_NOVOL_NOPITCH_BIGFADE,
					files=
					{
						"Sound/Enemy/Zombichicken/Zombichicken01.wav" ,
						"Sound/Enemy/Zombichicken/Zombichicken02.wav" ,
						"Sound/Enemy/Zombichicken/Zombichicken03.wav" ,
					}
				}
			}, 
        }
    }
}




