
        fx = 
        { 
            {
                FxDescriptor_Template=
                {
                    name = "FX_explosion_1",
                    texture	= "FX/Test_Rought/Hit_02.tga",
                    
                    gen =
                    {
                        ITF_ParticleGenerator_Template =
                        {
                            useanim = 1,
                            animstart = 6,
                            animend =  6 ,
                            animuvfreq = 0,
							computeAABB = 1,
                            params =
                            {
                                ParticleGeneratorParameters =
                                {
                                    maxParticles         = 1,
                                    emitParticlesCount   = 1,
                                    velNorm              = 0.0 ,
                                    grav                 = vectorNew(0.0,0.0,0.0),
                                    acc                  = vectorNew(0.0,0.0,0.0),
                                    velocityVar          = 0.0,
                                    friction             = 1.0,
                                    freq                 = 0.001,
                                    emitInterval         = 0.5,
                                    initAngle            = 360.0,
                                    angleDelta           = 360,
                                    angularSpeed         = 0,
                                    angularSpeedDelta    = 00,
                                    timeTarget           = 0.0,
                                    startTime            = 1.0,
                                    stopTime             = 2.0,
									uniformscale = 1,
                                    genBox               =
                                    {
                                        AABB =
                                        {
                                            MIN = vector2dNew(-0.1,-0.1),
                                            MAX = vector2dNew(0.1,0.1),
                                        }
                                    },
                                    boundingBox          =
                                    {
                                        AABB =
                                        {
                                            MIN = vector2dNew(-0.3,-0.3),
                                            MAX = vector2dNew(0.3,0.3),
                                        }
                                    },
                                    nbPhase              = 3,
                                    phases =
                                    {
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.05,
												
                                                colorMin    = "0xffffa229",
                                                colorMax    = "0xffffa229",
                                                sizeMin     = vector2dNew(3,3),
                                                sizeMax     = vector2dNew(3,3),
                                            }
                                        },
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.05,
												
                                                colorMin    = "0xffffa229",
                                                colorMax    = "0xffffa229",
                                                sizeMin     = vector2dNew(4,4),
                                                sizeMax     = vector2dNew(4,4),
                                            }
                                        },
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.0,
												
                                                colorMin    = "0x00ffffff",
                                                colorMax    = "0x00ffffff",
                                                sizeMin     = vector2dNew(5,5),
                                                sizeMax     = vector2dNew(5,5),
                                            }
                                        },
                                    },

                                    renderPrio           = 0,

                                    --GFX_BLEND_UNKNOWN = 0,
                                    --GFX_BLEND_COPY = 1
                                    --GFX_BLEND_ALPHA = 2
                                    --GFX_BLEND_ALPHAPREMULT = 3
                                    --GFX_BLEND_ALPHADEST = 4
                                    --GFX_BLEND_ALPHADESTPREMULT = 5
                                    --GFX_BLEND_ADD = 6
                                    --GFX_BLEND_ADDALPHA = 7
                                    --GFX_BLEND_SUBALPHA = 8
                                    --GFX_BLEND_SUB = 9
                                    --GFX_BLEND_MUL = 10 
                                    --GFX_BLEND_ALPHAMUL = 11
                                    --GFX_BLEND_IALPHAMUL = 12 
                                    --GFX_BLEND_IALPHA = 13
                                    --GFX_BLEND_IALPHAPREMULT = 14
                                    --GFX_BLEND_IALPHADEST = 15
                                    --GFX_BLEND_IALPHADESTPREMULT = 16
                                    --GFX_BLEND_MUL2X = 17
                                    blendMode          = 2,
                                    --PARGEN_GEN_POINTS = 0
                                    --PARGEN_GEN_RECTANGLE = 1
                                    --PARGEN_GEN_CIRCLE = 2
                                    genGenType         = 0,
									-- circleradius         = 1.00,
									-- innercircleradius         =  0.9,
									-- genangmin = -90,
									-- genangmax = 90,

                                    --PARGEN_MODE_FOLLOW  = 0
                                    --PARGEN_MODE_COMPLEX = 1
                                    genMode            = 1,
						}
					},
				}
			},
		}
	},
	{
                FxDescriptor_Template=
                {
                    name = "FX_explosion_2",
                    texture	= "FX/Common/FX_smoke_fire/smoke_anim_01.tga",
          			-- angleOffset = -1.7,

					gen=
                    {
                        ITF_ParticleGenerator_Template =
                        {
                            useAnim = 0,
                            useuvrandom = 0,
							animstart = 0,
							animend =  8,
                            AnimUVfreq = 15,
                            params =
                            {
                                ParticleGeneratorParameters =
                                {
                                    maxParticles         = 25,
                                    emitParticlesCount   = 25,  
                                    velNorm              = 5,
                                    grav                 = vectorNew(0.0,0.0,0.0),
                                    acc                  = vectorNew(0.0,0.0,0.0), 
                                    velocityVar          = 5.0,
                                    friction             = 0.90,
                                    freq                 = 0.0001,
                                    emitInterval         = 0.001,
                                    initAngle            = 180.0,
                                    angleDelta           = 180,
                                    angularSpeed         = 0, 
                                    angularSpeedDelta    = 90,
                                    timeTarget           = 0.0,
                                    startTime            = 1.0,
                                    stopTime             = 1.00,
									uniformscale = 1,
                                    genBox               =
                                    {
                                        AABB =
                                        {
                                            MIN = vector2dNew(-0.0,-0.0),
                                            MAX = vector2dNew(0.5,0.5),
                                        }
                                    },
                                    boundingBox          =
                                    {
                                        AABB =
                                        {
                                            MIN = vector2dNew(0.0,0.0),
                                            MAX = vector2dNew(0.5,0.5),
                                        }
                                    },
                                    nbPhase              = 3,
                                    phases =
                                    {
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.1,
												deltaphasetime = 0.1,
												animstart = 0,
												animend =  0,
                                                colorMin    = "0xffffe286",
                                                colorMax    = "0xffffe286",
                                                sizeMin     = vector2dNew(0.0,0.0),
                                                sizeMax     = vector2dNew(0.0,0.0 ),
                                            }
                                        },
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.5,
												deltaphasetime = 0.3,
												animstart = 0,
												animend =  8,
												animstretchtime = 1,
                                                colorMin    = "0x50777788",
                                                colorMax    = "0x80777788",
                                                sizeMin     = vector2dNew(0.5,0.5),
                                                sizeMax     = vector2dNew(1.0,1.0 ),
                                            }
                                        },
										{
                                            ParPhase =
                                            {
                                                phaseTime   = 0.0,
												animstart = 8,
												animend =  8,
                                                colorMin    = "0x00777788",
                                                colorMax    = "0x00777788",
                                                sizeMin     = vector2dNew(1.0,1.0),
                                                sizeMax     = vector2dNew(2.0,2.0 ),
                                            }
                                        },

									},
									
                                    renderPrio           = 1,
                                    --GFX_BLEND_UNKNOWN = 0,,
                                    --GFX_BLEND_COPY = 1,
                                    --GFX_BLEND_ALPHA = 2,
                                    --GFX_BLEND_ALPHAPREMULT = 3,
                                    --GFX_BLEND_ALPHADEST = 4,
                                    --GFX_BLEND_ALPHADESTPREMULT = 5,
                                    --GFX_BLEND_ADD = 6,
                                    --GFX_BLEND_ADDALPHA = 7,
                                    --GFX_BLEND_SUBALPHA = 8,
                                    --GFX_BLEND_SUB = 9,
                                    --GFX_BLEND_MUL = 10 ,
                                    --GFX_BLEND_ALPHAMUL = 11,
                                    --GFX_BLEND_IALPHAMUL = 12 ,
                                    --GFX_BLEND_IALPHA = 13,
                                    --GFX_BLEND_IALPHAPREMULT = 14,
                                    --GFX_BLEND_IALPHADEST = 15,
                                    --GFX_BLEND_IALPHADESTPREMULT = 16,
                                    --GFX_BLEND_MUL2X = 17,
                                    blendMode          = 2,
                                    --PARGEN_GEN_POINTS = 0
                                    --PARGEN_GEN_RECTANGLE = 1
                                    --PARGEN_GEN_CIRCLE = 2
                                    genGenType         = 2,
									-- circleradius         = 0.02,
									-- innercircleradius         =  0.01,
									-- scaleShape = vectorNew(0.5,1.0,0.0),
									rotateShape = vectorNew(0.0,1,0.0),
                                    --PARGEN_MODE_FOLLOW  = 0
                                    --PARGEN_MODE_COMPLEX = 1
                                    genMode            = 1,
						}
					},
				}
			},
		}
	},
	{
		FxDescriptor_Template=
		{
			name = "FX_explosion_3",
			texture	= "FX/Common/shockwave_atlas.tga", 
			gen=
			{
				ITF_ParticleGenerator_Template =
				{
					useAnim = 0, 
					useuvrandom = 0,
					animstart = 0,
					animend =  8,
					AnimUVfreq = 0,
					params =
					{
						ParticleGeneratorParameters =
						{
							maxParticles         = 1,
							emitParticlesCount   = 1,  
							velNorm              = 0,
							grav                 = vectorNew(0.0,0.0,0.0),
							acc                  = vectorNew(0.0,0.0,0.0),   
							velocityVar          = 0.0,
							friction             = 1.0,
							freq                 = 0.0001,      
							emitInterval         = 1,
							initAngle            = 360,
							angleDelta           = 360,
							angularSpeed         = 0, 
							angularSpeedDelta    = 0,
							timeTarget           = 0.0,
							startTime            = 1.0,
							stopTime             = 1.00,
							uniformscale = 1,
							genBox               =
							{
								AABB =
								{
									MIN = vector2dNew(-0.0,-0.0), 
									MAX = vector2dNew(0.5,0.5),
								}
							},
							boundingBox          =
							{
								AABB =
								{
									MIN = vector2dNew(0.0,-1.0),   
									MAX = vector2dNew(0.5,0.5),  
								}
							}, 
							nbPhase              = 3,
							phases =
							{
								{
									ParPhase =
									{
										phaseTime   = 0.07,     
										animstart = 6,
										animend =  6,

										colorMin    = "0xfffff279",
										colorMax    = "0xfffff279",
										sizeMin     = vector2dNew(1.5,1.5),
										sizeMax     = vector2dNew(1.5,1.5),     
									}
								},
								{
									ParPhase =
									{
										phaseTime   = 0.07,     
										animstart = 6,
										animend =  6,

										colorMin    = "0xaafff279",
										colorMax    = "0xaafff279",
										sizeMin     = vector2dNew(4,4),
										sizeMax     = vector2dNew(4,4),     
									}
								},
								{
									ParPhase =
									{
										phaseTime   = 0.0,   
										animstart = 6,
										animend =  6,

										colorMin    = "0x00fff279",
										colorMax    = "0x00fff279",
										sizeMin     = vector2dNew(8,8),
										sizeMax     = vector2dNew(8,8),      
									}
								},
							},
							renderPrio           = 2,
							--GFX_BLEND_UNKNOWN = 0,,
							--GFX_BLEND_COPY = 1,
							--GFX_BLEND_ALPHA = 2,
							--GFX_BLEND_ALPHAPREMULT = 3,
							--GFX_BLEND_ALPHADEST = 4,
							--GFX_BLEND_ALPHADESTPREMULT = 5,
							--GFX_BLEND_ADD = 6,
							--GFX_BLEND_ADDALPHA = 7,
							--GFX_BLEND_SUBALPHA = 8,
							--GFX_BLEND_SUB = 9,
							--GFX_BLEND_MUL = 10 ,
							--GFX_BLEND_ALPHAMUL = 11,
							--GFX_BLEND_IALPHAMUL = 12 ,
							--GFX_BLEND_IALPHA = 13,
							--GFX_BLEND_IALPHAPREMULT = 14,
							--GFX_BLEND_IALPHADEST = 15,
							--GFX_BLEND_IALPHADESTPREMULT = 16,
							--GFX_BLEND_MUL2X = 17,
							blendMode          = 7,
							--PARGEN_GEN_POINTS = 0
							--PARGEN_GEN_RECTANGLE = 1
							--PARGEN_GEN_CIRCLE = 2
							genGenType         = 0,
							--PARGEN_MODE_FOLLOW  = 0
							--PARGEN_MODE_COMPLEX = 1
							genMode            = 1,
						}
					},
				}
			},
		}
	},
	{
		FxDescriptor_Template=
		{
			name = "FX_explosion_4",
			texture	= "FX/Test_Rought/Hit_03.tga", 
			-- angleOffset = 3.14,
			gen=
			{
				ITF_ParticleGenerator_Template =
				{
					useAnim = 0,
					useuvrandom = 0,
					animstart = 0,
					animend =  0,
					AnimUVfreq = 0,
					
					params =
					{
						ParticleGeneratorParameters =
						{
							maxParticles         = 10,
							emitParticlesCount   = 10,  
							velNorm              = 8.0, 
							grav                 = vectorNew(0.0,0.0,0.0),
							acc                  = vectorNew(0.0,0.0,0.0),   
							velocityVar          = 0.5,
							friction             = 1.0,
							freq                 = 0.001,      
							emitInterval         = 1,
							initAngle            = 180,
							angleDelta           = 0,
							angularSpeed         = 0, 
							angularSpeedDelta    = 0,
							timeTarget           = 0.0,
							startTime            = 1.0,
							stopTime             = 1.00,
							pivot               = vector2dNew(0.0,-0.4), 
							uniformscale = 0,
							orientDir = 2,
							genBox               =
							{
								AABB =
								{
									MIN = vector2dNew(-0.0,-0.0), 
									MAX = vector2dNew(0.5,0.5),
								}
							},
							boundingBox          =
							{
								AABB =
								{
									MIN = vector2dNew(0.0,-1.0),   
									MAX = vector2dNew(0.5,0.5),  
								}
							}, 
							nbPhase              = 3,
							phases =
							{
								{
									ParPhase =
									{
										phaseTime   = 0.10,   
										colorMin    = "0x88ffffff", 
										colorMax    = "0xaaffffff",
										sizeMin     = vector2dNew(0.05,0.05),
										sizeMax     = vector2dNew(0.1,0.05),      
									}
								},
								{
									ParPhase =
									{
										phaseTime   = 0.07,   
										colorMin    = "0x40ffffff", 
										colorMax    = "0x80ffffff",
										sizeMin     = vector2dNew(0.05,1.0), 
										sizeMax     = vector2dNew(0.1,1.0),     
									}
								},
								{
									ParPhase =
									{
										phaseTime   = 0.0, 
--										Blendtonextphase = 0,
										colorMin    = "0x00ffffff", 
										colorMax    = "0x00ffffff",
										sizeMin     = vector2dNew(0.05,1.0),
										sizeMax     = vector2dNew(0.1,1.0),     
									}
								},
							},
							renderPrio           = 1,
							--GFX_BLEND_UNKNOWN = 0,,
							--GFX_BLEND_COPY = 1,
							--GFX_BLEND_ALPHA = 2,
							--GFX_BLEND_ALPHAPREMULT = 3,
							--GFX_BLEND_ALPHADEST = 4,
							--GFX_BLEND_ALPHADESTPREMULT = 5,
							--GFX_BLEND_ADD = 6,
							--GFX_BLEND_ADDALPHA = 7,
							--GFX_BLEND_SUBALPHA = 8,
							--GFX_BLEND_SUB = 9,
							--GFX_BLEND_MUL = 10 ,
							--GFX_BLEND_ALPHAMUL = 11,
							--GFX_BLEND_IALPHAMUL = 12 ,
							--GFX_BLEND_IALPHA = 13,
							--GFX_BLEND_IALPHAPREMULT = 14,
							--GFX_BLEND_IALPHADEST = 15,
							--GFX_BLEND_IALPHADESTPREMULT = 16,
							--GFX_BLEND_MUL2X = 17,
							blendMode          = 7,
							--PARGEN_GEN_POINTS = 0
							--PARGEN_GEN_RECTANGLE = 1
							--PARGEN_GEN_CIRCLE = 2
							genGenType         = 2,
							circleradius         = 0.02,
							innercircleradius         =  0.01,
							-- genangmin = -120,
							-- genangmax = -30,

							--PARGEN_MODE_FOLLOW  = 0
							--PARGEN_MODE_COMPLEX = 1
							genMode            = 1,
						}
					},
				}
			},
		}
	},
	{
		FxDescriptor_Template=
		{
			name = "FX_explosion_5",
			texture	= "FX/Common/shockwave_atlas.tga", 
			gen=
			{
				ITF_ParticleGenerator_Template =
				{
					useAnim = 0, 
					useuvrandom = 0,
					animstart = 0,
					animend =  8,
					AnimUVfreq = 0,
					params =
					{
						ParticleGeneratorParameters =
						{
							maxParticles         = 1,
							emitParticlesCount   = 1,  
							velNorm              = 0,
							grav                 = vectorNew(0.0,0.0,0.0),
							acc                  = vectorNew(0.0,0.0,0.0),   
							velocityVar          = 0.0,
							friction             = 1.0,
							freq                 = 0.01,      
							emitInterval         = 1,
							initAngle            = 360,
							angleDelta           = 360,
							angularSpeed         = 0, 
							angularSpeedDelta    = 0,
							timeTarget           = 0.0,
							startTime            = 1.0,
							stopTime             = 1.00,
							uniformscale = 1,
							genBox               =
							{
								AABB =
								{
									MIN = vector2dNew(-0.0,-0.0), 
									MAX = vector2dNew(0.5,0.5),
								}
							},
							boundingBox          =
							{
								AABB =
								{
									MIN = vector2dNew(0.0,-1.0),   
									MAX = vector2dNew(0.5,0.5),  
								}
							}, 
							nbPhase              = 2,
							phases =
							{
								{
									ParPhase =
									{
										phaseTime   = 0.17,     
										animstart = 3,
										animend =  3,

										colorMin    = "0x40ffffff",
										colorMax    = "0x40ffffff",
										sizeMin     = vector2dNew(0.5,0.5),
										sizeMax     = vector2dNew(0.5,0.5),     
									}
								},
								{
									ParPhase =
									{
										phaseTime   = 0.0,   
										animstart = 3,
										animend =  3,

										colorMin    = "0x00ffffff",
										colorMax    = "0x00ffffff",
										sizeMin     = vector2dNew(6,6),
										sizeMax     = vector2dNew(6,6),      
									}
								},
							},
							renderPrio           = 1,
							--GFX_BLEND_UNKNOWN = 0,,
							--GFX_BLEND_COPY = 1,
							--GFX_BLEND_ALPHA = 2,
							--GFX_BLEND_ALPHAPREMULT = 3,
							--GFX_BLEND_ALPHADEST = 4,
							--GFX_BLEND_ALPHADESTPREMULT = 5,
							--GFX_BLEND_ADD = 6,
							--GFX_BLEND_ADDALPHA = 7,
							--GFX_BLEND_SUBALPHA = 8,
							--GFX_BLEND_SUB = 9,
							--GFX_BLEND_MUL = 10 ,
							--GFX_BLEND_ALPHAMUL = 11,
							--GFX_BLEND_IALPHAMUL = 12 ,
							--GFX_BLEND_IALPHA = 13,
							--GFX_BLEND_IALPHAPREMULT = 14,
							--GFX_BLEND_IALPHADEST = 15,
							--GFX_BLEND_IALPHADESTPREMULT = 16,
							--GFX_BLEND_MUL2X = 17,
							blendMode          = 7,
							--PARGEN_GEN_POINTS = 0
							--PARGEN_GEN_RECTANGLE = 1
							--PARGEN_GEN_CIRCLE = 2
							genGenType         = 0,
							--PARGEN_MODE_FOLLOW  = 0
							--PARGEN_MODE_COMPLEX = 1
							genMode            = 1,
						}
					},
				}
			},
		}
	},

}

appendTable(component.FxBankComponent_Template.Fx,fx)
