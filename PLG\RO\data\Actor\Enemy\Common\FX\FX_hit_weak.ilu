fx =
{ 
	{
		FxDescriptor_Template=
		{
			name = "FX_hit_weak_01",
			texture	= "FX/Test_Rought/Hit_01.tga",  
			gen=
			{
				ITF_ParticleGenerator_Template =
				{
					useAnim = 0,
					useuvrandom = 0,
					animstart = 0,
					animend =  0,
					AnimUVfreq = 0, 
					params =
					{
						ParticleGeneratorParameters =
						{
							maxParticles         = 1, 
							emitParticlesCount   = 1,  
							velNorm              = 0,
							grav                 = vectorNew(0.0,0.0,0.0),
							acc                  = vectorNew(0.0,0.0,0.0), 
							velocityVar          = 0.0,
							friction             = 1.0,
							freq                 = 0.01,  
							emitInterval         = 1, 
							initAngle            = 90,
							angleDelta           = 360,
							angularSpeed         = 0, 
							angularSpeedDelta    = 0,
							timeTarget           = 0.0,
							startTime            = 1.0,
							stopTime             = 1.00,
							pivot               = vector2dNew(0.0,-0.35), 
							uniformscale = 1,
							genBox               =
							{
								AABB =
								{
									MIN = vector2dNew(-0.0,-0.0), 
									MAX = vector2dNew(0.5,0.5),
								}
							},
							boundingBox          =
							{
								AABB =
								{
									MIN = vector2dNew(0.0,-1.0),
									MAX = vector2dNew(0.5,0.5),
								}
							},
							nbPhase              = 3,
							phases =
							{
								{
									ParPhase =
									{
										phaseTime   = 0.05,
										colorMin    = "0x00ffffff",
										colorMax    = "0x00ffffff",
										Blendtonextphase = 0,
										sizeMin     = vector2dNew(1.0,1.0), 
										sizeMax     = vector2dNew(1.0,1.0), 
									}
								},
								{
									ParPhase =
									{
										phaseTime   = 0.1,
										colorMin    = "0xffffffff",
										colorMax    = "0xffffffff",
										sizeMin     = vector2dNew(1.0,1.0), 
										sizeMax     = vector2dNew(1.0,1.0), 
									}
								},
								{
									ParPhase =
									{
										phaseTime   = 0.0,
										colorMin    = "0x00ffffff",
										colorMax    = "0x00ffffff",
										sizeMin     = vector2dNew(2,2),
										sizeMax     = vector2dNew(2,2),  
									}
								},

							},
							renderPrio           = -1,
							--GFX_BLEND_UNKNOWN = 0,,
							--GFX_BLEND_COPY = 1,
							--GFX_BLEND_ALPHA = 2,
							--GFX_BLEND_ALPHAPREMULT = 3,
							--GFX_BLEND_ALPHADEST = 4,
							--GFX_BLEND_ALPHADESTPREMULT = 5,
							--GFX_BLEND_ADD = 6,
							--GFX_BLEND_ADDALPHA = 7,
							--GFX_BLEND_SUBALPHA = 8,
							--GFX_BLEND_SUB = 9,
							--GFX_BLEND_MUL = 10 ,
							--GFX_BLEND_ALPHAMUL = 11,
							--GFX_BLEND_IALPHAMUL = 12 ,
							--GFX_BLEND_IALPHA = 13,
							--GFX_BLEND_IALPHAPREMULT = 14,
							--GFX_BLEND_IALPHADEST = 15,
							--GFX_BLEND_IALPHADESTPREMULT = 16,
							--GFX_BLEND_MUL2X = 17,
							blendMode          = 7,
							--PARGEN_GEN_POINTS = 0
							--PARGEN_GEN_RECTANGLE = 1
							--PARGEN_GEN_CIRCLE = 2
							genGenType         = 0,
							--PARGEN_MODE_FOLLOW  = 0
							--PARGEN_MODE_COMPLEX = 1
							genMode            = 1,
						}
					},
				}
			},
		}
	},
	{
		FxDescriptor_Template=
		{
			name = "FX_hit_weak_02",
			texture	= "FX/Test_Rought/Hit_02.tga",  
			gen=
			{
				ITF_ParticleGenerator_Template =
				{
					useAnim = 0,
					useuvrandom = 0,
					animstart = 0,
					animend =  0,
					AnimUVfreq = 0, 
					params =
					{
						ParticleGeneratorParameters =
						{
							maxParticles         = 1, 
							emitParticlesCount   = 1,  
							velNorm              = 0,
							grav                 = vectorNew(0.0,0.0,0.0),
							acc                  = vectorNew(0.0,0.0,0.0), 
							velocityVar          = 0.0,
							friction             = 1.0,
							freq                 = 0.01,  
							emitInterval         = 1, 
							initAngle            = 360,
							angleDelta           = 360,
							angularSpeed         = 0, 
							angularSpeedDelta    = 0,
							timeTarget           = 0.0,
							startTime            = 1.0,
							stopTime             = 1.00,
							pivot               = vector2dNew(0.0,0.0), 
							uniformscale = 1,
							genBox               =
							{
								AABB =
								{
									MIN = vector2dNew(-0.0,-0.0), 
									MAX = vector2dNew(0.5,0.5),
								}
							},
							boundingBox          =
							{
								AABB =
								{
									MIN = vector2dNew(0.0,-1.0),
									MAX = vector2dNew(0.5,0.5),
								}
							},
							nbPhase              = 3,
							phases =
							{
								{
									ParPhase =
									{
										phaseTime   = 0.05,
										colorMin    = "0xffffffff",
										colorMax    = "0xffffffff",
										sizeMin     = vector2dNew(0.5,0.5), 
										sizeMax     = vector2dNew(0.5,0.5), 
									}
								},
								{
									ParPhase =
									{
										phaseTime   = 0.05,
										colorMin    = "0xffffffff",
										colorMax    = "0xffffffff",
										sizeMin     = vector2dNew(1.5,1.5), 
										sizeMax     = vector2dNew(1.5,1.5), 
									}
								},
								{
									ParPhase =
									{
										phaseTime   = 0.0,
										colorMin    = "0x00ffffff",
										colorMax    = "0x00ffffff",
										sizeMin     = vector2dNew(2,2),
										sizeMax     = vector2dNew(2,2),  
									}
								},

							},
							renderPrio           = 1,
							--GFX_BLEND_UNKNOWN = 0,,
							--GFX_BLEND_COPY = 1,
							--GFX_BLEND_ALPHA = 2,
							--GFX_BLEND_ALPHAPREMULT = 3,
							--GFX_BLEND_ALPHADEST = 4,
							--GFX_BLEND_ALPHADESTPREMULT = 5,
							--GFX_BLEND_ADD = 6,
							--GFX_BLEND_ADDALPHA = 7,
							--GFX_BLEND_SUBALPHA = 8,
							--GFX_BLEND_SUB = 9,
							--GFX_BLEND_MUL = 10 ,
							--GFX_BLEND_ALPHAMUL = 11,
							--GFX_BLEND_IALPHAMUL = 12 ,
							--GFX_BLEND_IALPHA = 13,
							--GFX_BLEND_IALPHAPREMULT = 14,
							--GFX_BLEND_IALPHADEST = 15,
							--GFX_BLEND_IALPHADESTPREMULT = 16,
							--GFX_BLEND_MUL2X = 17,
							blendMode          = 7,
							--PARGEN_GEN_POINTS = 0
							--PARGEN_GEN_RECTANGLE = 1
							--PARGEN_GEN_CIRCLE = 2
							genGenType         = 0,
							--PARGEN_MODE_FOLLOW  = 0
							--PARGEN_MODE_COMPLEX = 1
							genMode            = 1,
						}
					},
				}
			},
		}
	},
	{
		FxDescriptor_Template=
		{
			name = "FX_hit_weak_03",
			texture	= "FX/Test_Rought/Hit_03.tga", 
			-- angleOffset = 3.14,
			gen=
			{
				ITF_ParticleGenerator_Template =
				{
					useAnim = 0,
					useuvrandom = 0,
					animstart = 0,
					animend =  0,
					AnimUVfreq = 0,
					
					params =
					{
						ParticleGeneratorParameters =
						{
							maxParticles         = 5,
							emitParticlesCount   = 5,  
							velNorm              = 8.0, 
							grav                 = vectorNew(0.0,0.0,0.0),
							acc                  = vectorNew(0.0,0.0,0.0),   
							velocityVar          = 0.2,
							friction             = 1.0,
							freq                 = 0.001,      
							emitInterval         = 1,
							initAngle            = 180,
							angleDelta           = 0,
							angularSpeed         = 0, 
							angularSpeedDelta    = 0,
							timeTarget           = 0.0,
							startTime            = 1.0,
							stopTime             = 1.00,
							pivot               = vector2dNew(0.0,-0.4), 
							uniformscale = 0,
							orientDir = 2,
							genBox               =
							{
								AABB =
								{
									MIN = vector2dNew(-0.0,-0.0), 
									MAX = vector2dNew(0.5,0.5),
								}
							},
							boundingBox          =
							{
								AABB =
								{
									MIN = vector2dNew(0.0,-1.0),   
									MAX = vector2dNew(0.5,0.5),  
								}
							}, 
							nbPhase              = 3,
							phases =
							{
								{
									ParPhase =
									{
										phaseTime   = 0.025,   
										colorMin    = "0x88ffffff", 
										colorMax    = "0xaaffffff",
										sizeMin     = vector2dNew(0.05,0.4),
										sizeMax     = vector2dNew(0.1,0.8),      
									}
								},
								{
									ParPhase =
									{
										phaseTime   = 0.1,   
										colorMin    = "0x88ffffff", 
										colorMax    = "0xaaffffff",
										sizeMin     = vector2dNew(0.05,0.4),
										sizeMax     = vector2dNew(0.1,0.8),     
									}
								},
								{
									ParPhase =
									{
										phaseTime   = 0.0, 
--										Blendtonextphase = 0,
										colorMin    = "0x00ffffff", 
										colorMax    = "0x00ffffff",
										sizeMin     = vector2dNew(0.05,0.05),
										sizeMax     = vector2dNew(0.1,0.1),     
									}
								},
							},
							renderPrio           = -1,
							--GFX_BLEND_UNKNOWN = 0,,
							--GFX_BLEND_COPY = 1,
							--GFX_BLEND_ALPHA = 2,
							--GFX_BLEND_ALPHAPREMULT = 3,
							--GFX_BLEND_ALPHADEST = 4,
							--GFX_BLEND_ALPHADESTPREMULT = 5,
							--GFX_BLEND_ADD = 6,
							--GFX_BLEND_ADDALPHA = 7,
							--GFX_BLEND_SUBALPHA = 8,
							--GFX_BLEND_SUB = 9,
							--GFX_BLEND_MUL = 10 ,
							--GFX_BLEND_ALPHAMUL = 11,
							--GFX_BLEND_IALPHAMUL = 12 ,
							--GFX_BLEND_IALPHA = 13,
							--GFX_BLEND_IALPHAPREMULT = 14,
							--GFX_BLEND_IALPHADEST = 15,
							--GFX_BLEND_IALPHADESTPREMULT = 16,
							--GFX_BLEND_MUL2X = 17,
							blendMode          = 7,
							--PARGEN_GEN_POINTS = 0
							--PARGEN_GEN_RECTANGLE = 1
							--PARGEN_GEN_CIRCLE = 2
							genGenType         = 2,
							circleradius         = 0.02,
							innercircleradius         =  0.01,
							-- genangmin = -120,
							-- genangmax = -30,

							--PARGEN_MODE_FOLLOW  = 0
							--PARGEN_MODE_COMPLEX = 1
							genMode            = 1,
						}
					},
				}
			},
		}
	},
	{
		FxDescriptor_Template=
		{
			name = "FX_hit_weak_04",
			texture	= "FX/Common/shockwave_atlas.tga", 
			gen=
			{
				ITF_ParticleGenerator_Template =
				{
					useAnim = 0, 
					useuvrandom = 0,
					animstart = 0,
					animend =  8,
					AnimUVfreq = 0,
					params =
					{
						ParticleGeneratorParameters =
						{
							maxParticles         = 1,
							emitParticlesCount   = 1,  
							velNorm              = 0,
							grav                 = vectorNew(0.0,0.0,0.0),
							acc                  = vectorNew(0.0,0.0,0.0),   
							velocityVar          = 0.0,
							friction             = 1.0,
							freq                 = 0.01,      
							emitInterval         = 1,
							initAngle            = 360,
							angleDelta           = 360,
							angularSpeed         = 0, 
							angularSpeedDelta    = 0,
							timeTarget           = 0.0,
							startTime            = 1.0,
							stopTime             = 1.00,
							uniformscale = 1,
							genBox               =
							{
								AABB =
								{
									MIN = vector2dNew(-0.0,-0.0), 
									MAX = vector2dNew(0.5,0.5),
								}
							},
							boundingBox          =
							{
								AABB =
								{
									MIN = vector2dNew(0.0,-1.0),   
									MAX = vector2dNew(0.5,0.5),  
								}
							}, 
							nbPhase              = 2,
							phases =
							{
								{
									ParPhase =
									{
										phaseTime   = 0.20,     
										animstart = 3,
										animend =  3,

										colorMin    = "0x20ffffff",
										colorMax    = "0x20ffffff",
										sizeMin     = vector2dNew(0.5,0.5),
										sizeMax     = vector2dNew(0.5,0.5),     
									}
								},
								{
									ParPhase =
									{
										phaseTime   = 0.0,   
										animstart = 3,
										animend =  3,

										colorMin    = "0x00ffffff",
										colorMax    = "0x00ffffff",
										sizeMin     = vector2dNew(3,3),
										sizeMax     = vector2dNew(3,3),      
									}
								},
							},
							renderPrio           = -1,
							--GFX_BLEND_UNKNOWN = 0,,
							--GFX_BLEND_COPY = 1,
							--GFX_BLEND_ALPHA = 2,
							--GFX_BLEND_ALPHAPREMULT = 3,
							--GFX_BLEND_ALPHADEST = 4,
							--GFX_BLEND_ALPHADESTPREMULT = 5,
							--GFX_BLEND_ADD = 6,
							--GFX_BLEND_ADDALPHA = 7,
							--GFX_BLEND_SUBALPHA = 8,
							--GFX_BLEND_SUB = 9,
							--GFX_BLEND_MUL = 10 ,
							--GFX_BLEND_ALPHAMUL = 11,
							--GFX_BLEND_IALPHAMUL = 12 ,
							--GFX_BLEND_IALPHA = 13,
							--GFX_BLEND_IALPHAPREMULT = 14,
							--GFX_BLEND_IALPHADEST = 15,
							--GFX_BLEND_IALPHADESTPREMULT = 16,
							--GFX_BLEND_MUL2X = 17,
							blendMode          = 7,
							--PARGEN_GEN_POINTS = 0
							--PARGEN_GEN_RECTANGLE = 1
							--PARGEN_GEN_CIRCLE = 2
							genGenType         = 0,
							--PARGEN_MODE_FOLLOW  = 0
							--PARGEN_MODE_COMPLEX = 1
							genMode            = 1,
						}
					},
				}
			},
		}
	},
	{
		FxDescriptor_Template=
		{
			name = "FX_hit_weak_05",  
			texture	= "FX/Common/prtcl_Dust.tga", 
			angleOffset = 1,
			gen=
			{
				ITF_ParticleGenerator_Template =
				{
					useAnim = 1,
					useuvrandom = 1,
					animstart = 0,
					animend =  3,
					AnimUVfreq = 0, 
					params =
					{
						ParticleGeneratorParameters =
						{
							maxParticles         = 3, 
							emitParticlesCount   = 3,  
							velNorm              = 1, 
							grav                 = vectorNew(0.0,0.0,0.0),  
							acc                  = vectorNew(0.0,0.0,0.0), 
							velocityVar          = 1.0,
							friction             = 1.0,
							freq                 = 0.001,  
							emitInterval         = 1, 
							initAngle            = 360,
							angleDelta           = 360,
							angularSpeed         = 30, 
							angularSpeedDelta    = 45,
							timeTarget           = 0.0,
							startTime            = 1.0,
							stopTime             = 1.00,
							uniformscale = 1,
							genBox               =
							{
								AABB =
								{
									MIN = vector2dNew(-0.0,-0.0), 
									MAX = vector2dNew(0.5,0.5),
								}
							},
							boundingBox          =
							{
								AABB =
								{
									MIN = vector2dNew(0.0,-1.0),
									MAX = vector2dNew(0.5,0.5),
								}
							},
							nbPhase              = 3,
							phases =
							{
								{
									ParPhase =
									{
										phaseTime   = 0.1,
										colorMin    = "0x88ffaa44",
										colorMax    = "0x88ff9000",
										sizeMin     = vector2dNew(0.3,0.3), 
										sizeMax     = vector2dNew(0.6,0.6), 
									}
								}, 
								{
									ParPhase =
									{
										phaseTime   = 0.2,
										colorMin    = "0x88ffaa44",
										colorMax    = "0x88ff9000",
										sizeMin     = vector2dNew(0.6,0.6), 
										sizeMax     = vector2dNew(1.2,1.2), 
									}
								}, 
								{
									ParPhase =
									{
										phaseTime   = 0.0,
										colorMin    = "0x00ffaa44",
										colorMax    = "0x00ff9000",
										sizeMin     = vector2dNew(0.7,0.7), 
										sizeMax     = vector2dNew(1.4,1.4), 
									}
								},
							},
							renderPrio           = -1,
							--GFX_BLEND_UNKNOWN = 0,,
							--GFX_BLEND_COPY = 1,
							--GFX_BLEND_ALPHA = 2,
							--GFX_BLEND_ALPHAPREMULT = 3,
							--GFX_BLEND_ALPHADEST = 4,
							--GFX_BLEND_ALPHADESTPREMULT = 5,
							--GFX_BLEND_ADD = 6,
							--GFX_BLEND_ADDALPHA = 7,
							--GFX_BLEND_SUBALPHA = 8,
							--GFX_BLEND_SUB = 9,
							--GFX_BLEND_MUL = 10 ,
							--GFX_BLEND_ALPHAMUL = 11,
							--GFX_BLEND_IALPHAMUL = 12 ,
							--GFX_BLEND_IALPHA = 13,
							--GFX_BLEND_IALPHAPREMULT = 14,
							--GFX_BLEND_IALPHADEST = 15,
							--GFX_BLEND_IALPHADESTPREMULT = 16,
							--GFX_BLEND_MUL2X = 17,
							blendMode          = 2,
							--PARGEN_GEN_POINTS = 0
							--PARGEN_GEN_RECTANGLE = 1
							--PARGEN_GEN_CIRCLE = 2
							genGenType         = 2,
							circleradius         = 0.02,
							innercircleradius         =  0.01,
							genangmin = -80,
							genangmax = 80,

							--PARGEN_MODE_FOLLOW  = 0
							--PARGEN_MODE_COMPLEX = 1
							genMode            = 1, 
						}
					},
				}
			},
		}
	}

}
appendTable(component.FxBankComponent_Template.Fx,fx)
