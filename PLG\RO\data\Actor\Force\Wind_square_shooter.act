params =
{
    
NAME = "Actor_Template",
Actor_Template =
    {
        COMPONENTS =
        {
            {NAME="WindComponent_Template",
            WindComponent_Template =
            {
                windAreas =
                {
                    {PhysForceModifier =
                    {
                        force = vector2dNew(80, 0),
                        type = 1,
                        point = 0,
                        inverted = 0,
                        gradientPercentage = 0.7,
                        
                        offset = vector2dNew(3, 0),
                        box = {BoxData =
                            {
                                width = 6.0,
                                height = 4.0,
                            }},
                    }},
                }
            }},
            
            {NAME="FXControllerComponent_Template",
            FXControllerComponent_Template =
            {
                defaultFx = "Fan",
                fxControlList =
                {
                    {FXControl={name="Fan",particle="FX_Fan",fxUseActorSpeed=0,fxEmitFromBase=0,}},
                }
            }},
        }
    }
}

includeReference("Actor/Includes/helpers.ilu")

