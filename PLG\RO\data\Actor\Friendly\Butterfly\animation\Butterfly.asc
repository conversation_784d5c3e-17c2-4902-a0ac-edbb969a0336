<?xml version="1.0" ?>
<root>
	<AnimsList>
		<Element index="1" type="string" value="Actor/Friendly/Butterfly/animation/landing.anm"/>
		<Element index="2" type="string" value="Actor/Friendly/Butterfly/animation/offstand.anm"/>
		<Element index="3" type="string" value="Actor/Friendly/Butterfly/animation/stand.anm"/>
		<Element index="4" type="string" value="Actor/Friendly/Butterfly/animation/takeoff.anm"/>
	</AnimsList>
	<PatchBankList>
		<orange type="string" value="Actor/Friendly/Butterfly/animation/orange.tga"/>
	</PatchBankList>
	<Scale type="number" value="1"/>
	<SceneVersion type="number" value="17"/>
	<Squeleton type="string" value="Actor/Friendly/Butterfly/animation/Butterfly_Template1.skl"/>
	<UseDataFolder type="boolean" value="true"/>
	<UseRelative type="boolean" value="true"/>
</root>
