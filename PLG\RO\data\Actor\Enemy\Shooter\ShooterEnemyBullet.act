includeReference("Actor/Includes/helpers.ilu")
includeReference("Actor/Includes/gameplay_types.ilu")

params =
{
    
NAME = "Actor_Template",
Actor_Template =
    {
        SCALE = vector2dNew(0.7, 0.7),
        COMPONENTS =
        {
            {
                NAME = "TextureGraphicComponent_Template",
                TextureGraphicComponent_Template =
                {
                    visualAABB =
                    {
                        AABB =
                        {
                        },
                    },
                    angleOffset = 360.000000,
                    texture = "FX/Textures/Shape/canonball.png",
                    speedRotZ = 20.000000,
                },
            },
			
			{
                NAME="FXControllerComponent_Template",
                FXControllerComponent_Template = 
                {
                    defaultfx="fireball_shoot",
                    fxControlList = 
                    {
                        {
                            FXControl=
                            {
                                name="fireball_shoot",
                                particles={{VAL="fireball_shoot1"},{VAL="fireball_shoot2"},{VAL="fireball_shoot3"},{VAL="fireball_shoot4"},{VAL="fireball_shoot5"}},
                            }
                        },
                    }
                }				
            },            
            
            {NAME="Ray_ShooterStimComponent_Template",
            Ray_ShooterStimComponent_Template =
            {
				basicBullet = 
				{
					NAME = "Ray_BasicBullet_Template", 
					Ray_BasicBullet_Template =
					{
						defaultSpeed = vector2dNew(5, 0),
						lifetime = 10.0,
						faction = Faction.Enemy,
						destroyOnEnvironment = 0,
						stimShape = 
						{NAME = "PhysShapeCircle",
						PhysShapeCircle =
						{
							radius = 0.3,
						}},
					},
				},                
            }},
            
        }
    }
}

includeReference("FX/Actors/Fireball/fireball_shoot.ilu")
