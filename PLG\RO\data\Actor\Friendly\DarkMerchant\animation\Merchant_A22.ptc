<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="1.0112622157376"/>
			<AngleLocal type="number" value="1.0112622157376"/>
			<Lenght type="number" value="0.011476694606245"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Bonus_04"/>
			<Pos type="vector" x="0.96801507472992" y="0.13374082744122" z="0"/>
			<PosEnd type="vector" x="0.97948998212814" y="0.13353827595711" z="0"/>
			<PosLocal type="vector" x="0.96801507472992" y="0.13374082744122" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="D1714D1712D1710MyApp1699"/>
				<Element index="2" type="string" value="D1714D1712D1710MyApp1700"/>
				<Element index="3" type="string" value="D1714D1712D1710MyApp1701"/>
				<Element index="4" type="string" value="D1714D1712D1710MyApp1702"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A22"/>
			<UID type="string" value="D1714D1712D1710MyApp1698"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A22.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Bonus_04"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D1714D1712D1710MyApp1700"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D1714D1712D1710MyApp1698"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-1.6224477291107" y="0.028980961069465" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.017648939043283" y="-0.9998442530632" z="0"/>
			<PosUV type="vector" x="0.94888615608215" y="0.10509300976992" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A22"/>
			<UID type="string" value="D1714D1712D1710MyApp1699"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D1714D1712D1710MyApp1699"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D1714D1712D1710MyApp1698"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="-1.7570793628693" y="-0.040623631328344" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.017648939043283" y="0.9998442530632" z="0"/>
			<PosUV type="vector" x="0.94856971502304" y="0.1747140288353" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A22"/>
			<UID type="string" value="D1714D1712D1710MyApp1700"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D1714D1712D1710MyApp1702"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D1714D1712D1710MyApp1698"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="1.************" y="0.028650255873799" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.017648939043283" y="-0.9998442530632" z="0"/>
			<PosUV type="vector" x="0.98972678184509" y="0.10470286011696" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A22"/>
			<UID type="string" value="D1714D1712D1710MyApp1701"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D1714D1712D1710MyApp1701"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D1714D1712D1710MyApp1698"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="1.8581019639969" y="-0.038891535252333" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.017648939043283" y="0.9998442530632" z="0"/>
			<PosUV type="vector" x="0.99002301692963" y="0.17224994301796" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A22"/>
			<UID type="string" value="D1714D1712D1710MyApp1702"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A22"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="-10216.026367188" y="-986.15515136719" z="0"/>
	<zoomSize type="vector" x="11241.889648438" y="11241.889648438" z="10.58558177948"/>
</root>
