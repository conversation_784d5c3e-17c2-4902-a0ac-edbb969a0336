includeReference("Actor/Includes/gameplay_types.ilu")

component = {
NAME="AnimatedComponent_Template",
AnimatedComponent_Template = 
{
    posOffset = vector2dNew(0, -1),
    
    animationPath = "Actor/Enemy/ShooterGunner/Animation/",
    defaultAnimation = "IDLE",
    
    animSet = {SubAnimSet_Template =
    {
        animations=
        {
            {SubAnim_Template={ friendlyName="Idle", name="Stand.anm", loop=1, }},
            {SubAnim_Template={ friendlyName="IdleToShoot", name="Shoot.anm", markerStart="MRK_StandToShoot_Start", markerStop="MRK_StandToShoot_Stop", }},
            {SubAnim_Template={ friendlyName="Shoot", name="Shoot.anm", markerStart="MRK_Shoot_Start", markerStop="MRK_Shoot_Stop", }},
            -- {SubAnim_Template={ friendlyName="Shoot", name="Shoot.anm", }},
            {SubAnim_Template={ friendlyName="ShootToIdle", name="Shoot.anm", markerStart="MRK_ShootToStand_Start", markerStop="MRK_ShootToStand_Stop", }},
            {SubAnim_Template={ friendlyName="ReceiveHit", name="Receive_Hit.anm", markerStart="MRK_ReceiveHit_Start", markerStop="MRK_ReceiveHit_Stop", }},
            {SubAnim_Template={ friendlyName="ReceiveHitToIdle", name="Receive_Hit.anm", markerStart="MRK_ReceiveHit_To_Stand_Start", markerStop="MRK_ReceiveHit_To_Stand_Stop", }},
            -- {SubAnim_Template={ friendlyName="Stunned", name="Fmi_Stand_Etourdi.anm", loop=1, }},
            -- {SubAnim_Template={ friendlyName="StunnedToStand", name="Etourdi_tr_Stand.anm", }},
            {SubAnim_Template={ friendlyName="Death", name="Death.anm", }},
        },
    }},
    
    inputs =
    {
        {InputDesc={name="RandomN", varType=AnimInputTypes.float}},              -- Used to play random animations
        {InputDesc={name="ReceivedHitLevel", varType=AnimInputTypes.uint}},      -- Level of the last received hit
        {InputDesc={name="ReceivedHitType", varType=AnimInputTypes.uint}},       -- Type of the hit being received
        {InputDesc={name="Stunned", varType=AnimInputTypes.uint}},               -- If the character is stunned
    },
    
    tree = 
    {
        AnimTree_Template =
        {
            nodes =
            {
                {
                    NAME="AnimTreeNodePlayAnim_Template",
                    AnimTreeNodePlayAnim_Template =
                    {
                        nodeName = "IDLE",
                        animationName = "Idle"
                    }
                },
                {
                    NAME="AnimTreeNodePlayAnim_Template",
                    AnimTreeNodePlayAnim_Template =
                    {
                        nodeName = "SHOOT",
                        animationName = "Shoot"
                    }
                },
                {
                    NAME="AnimTreeNodePlayAnim_Template",
                    AnimTreeNodePlayAnim_Template =
                    {
                        nodeName = "RECEIVEHIT",
                        animationName = "ReceiveHit"
                    }
                },
                {
                    NAME="AnimTreeNodePlayAnim_Template",
                    AnimTreeNodePlayAnim_Template =
                    {
                        nodeName = "DEATH",
                        animationName = "Death"
                    }
                },
            },
            nodeTransitions =
            {
                {
                    BlendTreeTransition_Template =
                    {
                        from =
                        {
                            { VAL = "IDLE" },
                        },
                        to =
                        {
                            { VAL = "SHOOT" },
                        },
                        node =
                        {
                            NAME="AnimTreeNodePlayAnim_Template",
                            AnimTreeNodePlayAnim_Template =
                            {
                                animationName = "IdleToShoot"
                            }
                        },
                    }
                },
                {
                    BlendTreeTransition_Template =
                    {
                        from =
                        {
                            { VAL = "SHOOT" },
                        },
                        to =
                        {
                            { VAL = "IDLE" },
                        },
                        node =
                        {
                            NAME="AnimTreeNodePlayAnim_Template",
                            AnimTreeNodePlayAnim_Template =
                            {
                                animationName = "ShootToIdle"
                            }
                        },
                    }
                },
                {
                    BlendTreeTransition_Template =
                    {
                        from =
                        {
                            { VAL = "RECEIVEHIT" },
                        },
                        to =
                        {
                            { VAL = "IDLE" },
                        },
                        node =
                        {
                            NAME="AnimTreeNodePlayAnim_Template",
                            AnimTreeNodePlayAnim_Template =
                            {
                                animationName = "ReceiveHitToIdle"
                            }
                        },
                    }
                },
            },
        }
    },
}}

appendTable(params.Actor_Template.COMPONENTS,{component})
component = {}

