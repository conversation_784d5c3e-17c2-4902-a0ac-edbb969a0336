params =
{
    
NAME = "Actor_Template",
Actor_Template =
    {
        PROCEDURAL = 0,
        zForced = 0.000000,
        rankForced = 0.000000,
        scaleForced = vector2dNew(0.000000,0.000000),
        SCALE = vector2dNew(3.000000,3.000000),
        ObjectFamily = 1,
        RANK = -1.000000,
        STARTPAUSED = 0,
        COMPONENTS =
        {
			{
				NAME="Ray_FixedAIComponent_Template",
				Ray_FixedAIComponent_Template=
				{
					canBeVacuumed=1,
                    genericBehavior =
                    {
                        NAME = "AIPlayActionsBehavior_Template",
                        AIPlayActionsBehavior_Template =
                        { 
							name = "BHV_IDLE",
                            actions = 
                            {
                                {
                                    NAME="AIIdleAction_Template",
                                    AIIdleAction_Template =
                                    {
                                        action = "idle",
                                    }
                                },
                            }
                        },
                    },
				}
			},
            {
                NAME = "PhantomComponent_Template",
                PhantomComponent_Template =
                {
                    Shape =
                    {
                        NAME = "PhysShapeCircle",
                        PhysShapeCircle =
                        {
                            Radius = 0.2800000,
                        },
                    },
                    collisionGroup = 8,
                },
            },
            {
                NAME = "AnimLightComponent_Template",
                AnimLightComponent_Template =
                {
                    patchLevel = 0,
                    patchHLevel = 2,
                    patchVLevel = 2,
                    visualAABB =
                    {
                        AABB =
                        {
                            MIN = vector2dNew(0.000000,0.000000),
                            MAX = vector2dNew(0.000000,0.000000),
                        },
                    },
                    renderintarget = 0,
                    posOffset = vector2dNew(0.000000,0.000000),
                    rotOffset = 0.000000,
                    blendmode = 2,
                    materialtype = 0,
                    useBase = 0,
                    scale = vector2dNew(1.000000,1.000000),
                    smoothAnim = 0,
                    canonizeTransitions = 1,
                    defaultBlendFrames = 0,
                    draw2D = 0,
                    visualAABBanimID = "",
                    animSet =
                    {
                        SubAnimSet_Template =
                        {
                            animations =
                            {
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "Idle",
                                        name = "world/1_jungleworld/platform/plum/animation/hang_stand.anm",
                                        playRate = 1.000000,
                                        loop = 1,
                                        useRootRotation = 1,
                                    },
								},
								{
									SubAnim_Template =
                                    {
                                        friendlyName = "Idle_wind",
                                        name = "world/1_jungleworld/platform/plum/animation/hang_stand.anm",
                                        playRate = 4.000000,
                                        loop = 1,
                                        useRootRotation = 1,
                                    },
                                },
                            },
                        },
                    },
                    defaultAnimation = "Idle",
                    defaultColor = "0xffffffff",
                    flip = 0,
                    disableLight = 0,
                    backZOffset = 0.000000,
                    frontZOffset = 0.000000,
					inputs =
					{
						{InputDesc={name="Vacuum", varType=AnimInputTypes.bool}},              -- Used to play random animations
					},
					tree = 
					{
						AnimTree_Template =
						{
							nodes =
							{
								{NAME="BlendTreeNodeChooseBranch_Template",
								BlendTreeNodeChooseBranch_Template =
								{
									nodeName="idle",
									leafs=
									{
										{NAME="AnimTreeNodePlayAnim_Template",
										AnimTreeNodePlayAnim_Template =
										{
											animationName = "idle",
										}},
										{NAME="AnimTreeNodePlayAnim_Template",
										AnimTreeNodePlayAnim_Template =
										{
											animationName = "Idle_wind",
										}},
									},
									leafsCriterias=
									{
										{BlendLeaf =
											{criterias =
												{
													{CriteriaDesc={name="Vacuum",eval="==",value=0}},
												},
											}
										},
										{BlendLeaf =
										{
										}},
									},
								}},
								{NAME="AnimTreeNodePlayAnim_Template",
								AnimTreeNodePlayAnim_Template =
								{
									nodeName="Vaccumed",
									animationName = "Idle_wind",
								}},
							}
						}
					},
                },
            },
        },
        xFLIPPED = 0,
    },
}
includeReference("Actor/Enemy/Shooter/Common/Components/shooter_big_ActorParam.ilu")