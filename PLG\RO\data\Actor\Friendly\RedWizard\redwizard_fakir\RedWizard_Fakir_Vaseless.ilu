includeReference("Actor/Includes/helpers.ilu")
includeReference("Actor/Includes/gameplay_types.ilu")

--act_phantomOffset = getValue(act_phantomOffset, vector2dNew(0.000000,0.000000))

params =
{
    
NAME = "Actor_Template",
Actor_Template =
    {
        SCALE = vector2dNew(1.500000,1.500000),
        RANK = -1.000000,
        COMPONENTS =
        {
            {
                NAME = "AnimatedComponent_Template",
                AnimatedComponent_Template =
                {
                    animationPath = "actor/friendly/redwizard/redwizard_fakir/animation/",
                    animSet =
                    {
                        SubAnimSet_Template =
                        {
                            animations =
                            {
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "Idle",
                                        name = "stand_sitdown.anm",
                                        loop = 1,
                                    },
                                },
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "IdleSquashed",
                                        name = "Boucle_Reaction_Squash.anm",
                                        loop = 1,
                                        markerStart = "MRK_Boucle_Stand_Start",
                                        markerStop = "MRK_Boucle_Stand_Stop",
                                    },
                                },
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "OnSquash",
                                        name = "Boucle_Reaction_Squash.anm",
                                        markerStart = "MRK_Squash_Start",
                                        markerStop = "MRK_Squash_Stop",
                                    },
                                },
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "OnUnsquash",
                                        name = "Boucle_Reaction_Squash.anm",
                                        markerStart = "MRK_Desquash_Start",
                                        markerStop = "MRK_Desquash_Stop",
                                    },
                                },
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "ReceiveHit",
                                        name = "Boucle_Reaction_Squash.anm",
                                        markerStart = "MRK_Squash_Start",
                                        markerStop = "MRK_Desquash_Stop",
                                    },
                                },
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "ReceiveHit_Squashed",
                                        name = "Boucle_Reaction_Squash.anm",
                                        markerStart = "MRK_Boucle_Stand_Start",
                                        markerStop = "MRK_Boucle_Stand_Stop",
                                    },
                                },
                            },
                        },
                    },
                    defaultAnimation = "IDLE",
                    inputs =
                    {
                        {
                           InputDesc=
                            {
                                name = "WeightOnPolyLine",
                                varType = 0,
                            },
                        },
                    },
                    tree =
                    {
                        AnimTree_Template =
                        {
                            nodes =
                            {
                                {
                                    NAME = "BlendTreeNodeChooseBranch_Template",
                                    BlendTreeNodeChooseBranch_Template =
                                    {
                                        nodeName = "IDLE",
                                        leafs =
                                        {
                                            {
                                                NAME = "AnimTreeNodePlayAnim_Template",
                                                AnimTreeNodePlayAnim_Template =
                                                {
                                                    nodeName = "IdleNormal",
                                                    animationName = "Idle",
                                                },
                                            },
                                            {
                                                NAME = "AnimTreeNodePlayAnim_Template",
                                                AnimTreeNodePlayAnim_Template =
                                                {
                                                    nodeName = "IdleSquashed",
                                                    animationName = "IdleSquashed",
                                                },
                                            },
                                        },
                                        leafsCriterias =
                                        {
                                            {
                                                BlendLeaf =
                                                {
                                                    criterias =
                                                    {
                                                        {
                                                            CriteriaDesc =
                                                            {
                                                                name = "WeightOnPolyLine",
                                                                eval = "<",
                                                                value = 0.5,
                                                            },
                                                        },
                                                    },
                                                },
                                            },
                                            {
                                                BlendLeaf =
                                                {
                                                },
                                            },
                                        },
                                    },
                                },
                                {
                                    NAME = "BlendTreeNodeChooseBranch_Template",
                                    BlendTreeNodeChooseBranch_Template =
                                    {
                                        nodeName = "RECEIVEHIT",
                                        leafs =
                                        {
                                            {
                                                NAME = "AnimTreeNodePlayAnim_Template",
                                                AnimTreeNodePlayAnim_Template =
                                                {
                                                    animationName = "ReceiveHit",
                                                },
                                            },
                                            {
                                                NAME = "AnimTreeNodePlayAnim_Template",
                                                AnimTreeNodePlayAnim_Template =
                                                {
                                                    animationName = "ReceiveHit_Squashed",
                                                },
                                            },
                                        },
                                        leafsCriterias =
                                        {
                                            {
                                                BlendLeaf =
                                                {
                                                    criterias =
                                                    {
                                                        {
                                                            CriteriaDesc =
                                                            {
                                                                name = "WeightOnPolyLine",
                                                                eval = "<",
                                                                value = 0.5,
                                                            },
                                                        },
                                                    },
                                                },
                                            },
                                            {
                                                BlendLeaf =
                                                {
                                                },
                                            },
                                        },
                                    },
                                },
                            },
                            nodeTransitions =
                            {
                                {
                                    BlendTreeTransition_Template =
                                    {
                                        from =
                                        {
                                            {
                                                VAL = "IdleNormal",
                                            },
                                        },
                                        to =
                                        {
                                            {
                                                VAL = "IdleSquashed",
                                            },
                                        },
                                        node =
                                        {
                                            NAME = "AnimTreeNodePlayAnim_Template",
                                            AnimTreeNodePlayAnim_Template =
                                            {
                                                nodeName = "OnSquash",
                                                animationName = "OnSquash",
                                            },
                                        },
                                    },
                                },
                                {
                                    BlendTreeTransition_Template =
                                    {
                                        from =
                                        {
                                            {
                                                VAL = "IdleSquashed",
                                            },
                                        },
                                        to =
                                        {
                                            {
                                                VAL = "IdleNormal",
                                            },
                                        },
                                        node =
                                        {
                                            NAME = "AnimTreeNodePlayAnim_Template",
                                            AnimTreeNodePlayAnim_Template =
                                            {
                                                nodeName = "OnUnsquash",
                                                animationName = "OnUnsquash",
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            },
            {
                NAME = "PolylineComponent_Template",
                PolylineComponent_Template =
                {
                    polylineParams =
                    {
                        {
                            PolylineParameters =
                            {
                                gameMaterial = "gamematerial/BasicPlateforme.gmt",
                                polylines =
                                {
                                    {
                                        VAL = "L_Plate",
                                    },
                                },
                            },
                        },
                    },
                },
            },
            {
                NAME = "PlayAnimOnWeightChangeComponent_Template",
                PlayAnimOnWeightChangeComponent_Template =
                {
                    listenToTrigger = 0,
                    listenToWeight = 1,
                },
            },
            {
                NAME = "RenderSimpleAnimComponent_Template",
                RenderSimpleAnimComponent_Template =
                {
                },
            },
            -- {
                -- NAME = "PhantomComponent_Template",
                -- PhantomComponent_Template =
                -- {
                    -- shape =
                    -- {
                        -- NAME = "PhysShapeCircle",
                        -- PhysShapeCircle =
                        -- {
                            -- Radius = -0.500000,
                        -- },
                    -- },
                    -- offset = vector2dNew(0.000000,-0.500000),
                    -- collisionGroup = 4,
                -- },
            -- },
            -- {
                -- NAME = "PlayAnimOnEventReceiveComponent_Template",
                -- PlayAnimOnEventReceiveComponent_Template =
                -- {
                    -- listenEvents =
                    -- {
                        -- {
                            -- NAME = "PunchStim",
                            -- PunchStim =
                            -- {
                            -- },
                        -- },
                    -- },
                    -- idleAnim = "Idle",
                    -- eventAnim = "ReceiveHit",
                -- },
            -- },
        },
    },
    ActorEditorParams =
    {
    },
}


includeReference("Actor/Friendly/RedWizard/Components/RedWizard_fxController.ilu")
includeReference("Actor/Friendly/RedWizard/Components/RedWizard_Sound.ilu")
