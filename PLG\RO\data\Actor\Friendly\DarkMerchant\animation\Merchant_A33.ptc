<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-94.************"/>
			<AngleLocal type="number" value="-94.************"/>
			<Lenght type="number" value="0.097434513270855"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Glasses"/>
			<Pos type="vector" x="0.8022238612175" y="0.40379530191422" z="0"/>
			<PosEnd type="vector" x="0.79429996013641" y="0.50090706348419" z="0"/>
			<PosLocal type="vector" x="0.8022238612175" y="0.40379530191422" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="D2465MyApp2454"/>
				<Element index="2" type="string" value="D2465MyApp2455"/>
				<Element index="3" type="string" value="D2465MyApp2456"/>
				<Element index="4" type="string" value="D2465MyApp2457"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A33"/>
			<UID type="string" value="D2465MyApp2453"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A33.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Fx_HatSpin_02"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2465MyApp2455"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2465MyApp2453"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.50235116481781" y="0.86466372013092" z="0"/>
					<Pos type="vector" x="-0.16229499876499" y="0.091811627149582" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.82094568014145" y="0.57100629806519" z="0"/>
			<PosUV type="vector" x="0.89501738548279" y="0.39550116658211" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A33"/>
			<UID type="string" value="D2465MyApp2454"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2465MyApp2454"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2465MyApp2453"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.37482669949532" y="-0.92709493637085" z="0"/>
					<Pos type="vector" x="-0.065096542239189" y="-0.072741836309433" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.95450699329376" y="0.29818874597549" z="0"/>
			<PosUV type="vector" x="0.73023879528046" y="0.3915579020977" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A33"/>
			<UID type="string" value="D2465MyApp2455"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2465MyApp2457"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2465MyApp2453"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.5068946480751" y="0.86200803518295" z="0"/>
					<Pos type="vector" x="1.0718108415604" y="0.024335741996765" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.81792932748795" y="0.57531875371933" z="0"/>
			<PosUV type="vector" x="0.81798607110977" y="0.50985985994339" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A33"/>
			<UID type="string" value="D2465MyApp2456"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2465MyApp2456"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2465MyApp2453"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.56029087305069" y="-0.82829594612122" z="0"/>
					<Pos type="vector" x="1.0879807472229" y="-0.0109739061445" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.87111818790436" y="0.49107345938683" z="0"/>
			<PosUV type="vector" x="0.78266525268555" y="0.50855857133865" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A33"/>
			<UID type="string" value="D2465MyApp2457"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A33"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="-1715.7834472656" y="-907.65600585938" z="0"/>
	<zoomSize type="vector" x="3412.3232421875" y="3412.3210449219" z="3.0908713340759"/>
</root>
