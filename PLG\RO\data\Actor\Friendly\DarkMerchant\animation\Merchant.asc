<?xml version="1.0" ?>
<root>
	<AnimsList>
		<Element index="1" type="string" value="Actor/Friendly/DarkMerchant/animation/Mrc_Away_Stand.anm"/>
		<Element index="2" type="string" value="Actor/Friendly/DarkMerchant/animation/Mrc_Closed_Stand.anm"/>
		<Element index="3" type="string" value="Actor/Friendly/DarkMerchant/animation/Mrc_Closed_to_Open.anm"/>
		<Element index="4" type="string" value="Actor/Friendly/DarkMerchant/animation/Mrc_Open_Stand.anm"/>
		<Element index="5" type="string" value="Actor/Friendly/DarkMerchant/animation/Mrc_Open_Warp.anm"/>
		<Element index="6" type="string" value="Actor/Friendly/DarkMerchant/animation/Mrc_Open_to_Away.anm"/>
	</AnimsList>
	<PatchBankList>
		<Merchant_A type="string" value="Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	</PatchBankList>
	<Scale type="number" value="1"/>
	<SceneVersion type="number" value="17"/>
	<Squeleton type="string" value="Actor/Friendly/DarkMerchant/animation/Merchant_Template1.skl"/>
	<UseDataFolder type="boolean" value="true"/>
	<UseRelative type="boolean" value="true"/>
</root>
