fx =
{ 
	{
		FxDescriptor_Template=
		{
			name = "FX_break_jade_01",  
			texture	= "FX/Common/prtcl_debris_jade.tga", 
			angleOffset = 1,
			gen=
			{
				ITF_ParticleGenerator_Template =
				{
					useAnim = 1,
					useuvrandom = 1,
					animstart = 0,
					animend =  3,
					AnimUVfreq = 0, 
					params =
					{
						ParticleGeneratorParameters =
						{
							maxParticles         = 5, 
							emitParticlesCount   = 5,  
							velNorm              = 4,
							grav                 = vectorNew(0.0,-15.0,0.0), 
							acc                  = vectorNew(0.0,0.0,0.0), 
							velocityVar          = 1.0,
							friction             = 0.99,  
							freq                 = 0.001,  
							emitInterval         = 1, 
							initAngle            = 360,
							angleDelta           = 360,
							angularSpeed         = 180, 
							angularSpeedDelta    = 360,
							timeTarget           = 0.0,
							startTime            = 1.0,
							stopTime             = 1.00,
							uniformscale = 1,
							genBox               =
							{
								AABB =
								{
									MIN = vector2dNew(-0.5,-0.5), 
									MAX = vector2dNew(0.5,0.5),
								}
							},
							boundingBox          =
							{
								AABB =
								{
									MIN = vector2dNew(0.0,-1.0),
									MAX = vector2dNew(0.5,0.5),
								}
							},
							nbPhase              = 3,
							phases =
							{
								{
									ParPhase =
									{
										phaseTime   = 0.6,
										colorMin    = "0xffffffff",
										colorMax    = "0xffffffff",
										sizeMin     = vector2dNew(0.2,0.2), 
										sizeMax     = vector2dNew(0.4,0.4), 
									}
								}, 
								{
									ParPhase =
									{
										phaseTime   = 0.2,
										colorMin    = "0xffffffff",
										colorMax    = "0xffffffff",
										sizeMin     = vector2dNew(0.2,0.2), 
										sizeMax     = vector2dNew(0.4,0.4), 
									}
								},
								{
									ParPhase =
									{
										phaseTime   = 0.0,
										colorMin    = "0x00ffffff",
										colorMax    = "0x00ffffff",
										sizeMin     = vector2dNew(0.2,0.2), 
										sizeMax     = vector2dNew(0.4,0.4), 
									}
								},
							},
							renderPrio           = 1,
							--GFX_BLEND_UNKNOWN = 0,,
							--GFX_BLEND_COPY = 1,
							--GFX_BLEND_ALPHA = 2,
							--GFX_BLEND_ALPHAPREMULT = 3,
							--GFX_BLEND_ALPHADEST = 4,
							--GFX_BLEND_ALPHADESTPREMULT = 5,
							--GFX_BLEND_ADD = 6,
							--GFX_BLEND_ADDALPHA = 7,
							--GFX_BLEND_SUBALPHA = 8,
							--GFX_BLEND_SUB = 9,
							--GFX_BLEND_MUL = 10 ,
							--GFX_BLEND_ALPHAMUL = 11,
							--GFX_BLEND_IALPHAMUL = 12 ,
							--GFX_BLEND_IALPHA = 13,
							--GFX_BLEND_IALPHAPREMULT = 14,
							--GFX_BLEND_IALPHADEST = 15,
							--GFX_BLEND_IALPHADESTPREMULT = 16,
							--GFX_BLEND_MUL2X = 17,
							blendMode          = 2,
							--PARGEN_GEN_POINTS = 0
							--PARGEN_GEN_RECTANGLE = 1
							--PARGEN_GEN_CIRCLE = 2
							genGenType         = 2,
							circleradius         = 1.0,
							innercircleradius         =  0.5,
							genangmin = -50,
							genangmax = 50,

							--PARGEN_MODE_FOLLOW  = 0
							--PARGEN_MODE_COMPLEX = 1
							genMode            = 1,
						}
					},
				}
			},
		}
	},
	{
		FxDescriptor_Template=
		{
			name = "FX_break_jade_02",  
			texture	= "FX/Common/prtcl_debris_jade.tga", 
			angleOffset = 1,
			gen=
			{
				ITF_ParticleGenerator_Template =
				{
					useAnim = 1,
					useuvrandom = 1,
					animstart = 0,
					animend =  3,
					AnimUVfreq = 0, 
					params =
					{
						ParticleGeneratorParameters =
						{
							maxParticles         = 10, 
							emitParticlesCount   = 10,  
							velNorm              = 3, 
							grav                 = vectorNew(0.0,-10.0,0.0), 
							acc                  = vectorNew(0.0,0.0,0.0), 
							velocityVar          = 1.0,
							friction             = 1.0,
							freq                 = 0.001,  
							emitInterval         = 1, 
							initAngle            = 360,
							angleDelta           = 360,
							angularSpeed         = 90, 
							angularSpeedDelta    = 90,
							timeTarget           = 0.0,
							startTime            = 1.0,
							stopTime             = 1.00,
							uniformscale = 1,
							genBox               =
							{
								AABB =
								{
									MIN = vector2dNew(-0.0,-0.0), 
									MAX = vector2dNew(0.5,0.5),
								}
							},
							boundingBox          =
							{
								AABB =
								{
									MIN = vector2dNew(0.0,-1.0),
									MAX = vector2dNew(0.5,0.5),
								}
							},
							nbPhase              = 3,
							phases =
							{
								{
									ParPhase =
									{
										phaseTime   = 0.5,
										colorMin    = "0xffffffff",
										colorMax    = "0xffffffff",
										sizeMin     = vector2dNew(0.05,0.05), 
										sizeMax     = vector2dNew(0.1,0.1), 
									}
								}, 
								{
									ParPhase =
									{
										phaseTime   = 0.2,
										colorMin    = "0xffffffff",
										colorMax    = "0xffffffff",
										sizeMin     = vector2dNew(0.05,0.05), 
										sizeMax     = vector2dNew(0.1,0.1), 
									}
								},
								{
									ParPhase =
									{
										phaseTime   = 0.0,
										colorMin    = "0x00ffffff",
										colorMax    = "0x00ffffff",
										sizeMin     = vector2dNew(0.05,0.05), 
										sizeMax     = vector2dNew(0.1,0.1), 
									}
								},
							},
							renderPrio           = 1,
							--GFX_BLEND_UNKNOWN = 0,,
							--GFX_BLEND_COPY = 1,
							--GFX_BLEND_ALPHA = 2,
							--GFX_BLEND_ALPHAPREMULT = 3,
							--GFX_BLEND_ALPHADEST = 4,
							--GFX_BLEND_ALPHADESTPREMULT = 5,
							--GFX_BLEND_ADD = 6,
							--GFX_BLEND_ADDALPHA = 7,
							--GFX_BLEND_SUBALPHA = 8,
							--GFX_BLEND_SUB = 9,
							--GFX_BLEND_MUL = 10 ,
							--GFX_BLEND_ALPHAMUL = 11,
							--GFX_BLEND_IALPHAMUL = 12 ,
							--GFX_BLEND_IALPHA = 13,
							--GFX_BLEND_IALPHAPREMULT = 14,
							--GFX_BLEND_IALPHADEST = 15,
							--GFX_BLEND_IALPHADESTPREMULT = 16,
							--GFX_BLEND_MUL2X = 17,
							blendMode          = 2,
							--PARGEN_GEN_POINTS = 0
							--PARGEN_GEN_RECTANGLE = 1
							--PARGEN_GEN_CIRCLE = 2
							genGenType         = 2,
							circleradius         = 1.0,
							innercircleradius         =  0.5,
							genangmin = -60,
							genangmax = 60,

							--PARGEN_MODE_FOLLOW  = 0
							--PARGEN_MODE_COMPLEX = 1
							genMode            = 1,
						}
					},
				}
			},
		}
	},
	{
		FxDescriptor_Template=
		{
			name = "FX_break_jade_03",  
			texture	= "FX/Common/prtcl_Dust.tga", 
			angleOffset = 1,
			gen=
			{
				ITF_ParticleGenerator_Template =
				{
					useAnim = 1,
					useuvrandom = 1,
					animstart = 0,
					animend =  3,
					AnimUVfreq = 0, 
					params =
					{
						ParticleGeneratorParameters =
						{
							maxParticles         = 10, 
							emitParticlesCount   = 10,  
							velNorm              = 1.0, 
							grav                 = vectorNew(0.0,-1.0,0.0),  
							acc                  = vectorNew(0.0,0.0,0.0), 
							velocityVar          = 2.0,
							friction             = 1.0,
							freq                 = 0.001,  
							emitInterval         = 1, 
							initAngle            = 360,
							angleDelta           = 360,
							angularSpeed         = 30, 
							angularSpeedDelta    = 45,
							timeTarget           = 0.0,
							startTime            = 1.0,
							stopTime             = 1.00,
							uniformscale = 1,
							genBox               =
							{
								AABB =
								{
									MIN = vector2dNew(-0.0,-0.0), 
									MAX = vector2dNew(0.5,0.5),
								}
							},
							boundingBox          =
							{
								AABB =
								{
									MIN = vector2dNew(0.0,-1.0),
									MAX = vector2dNew(0.5,0.5),
								}
							},
							nbPhase              = 2,
							phases =
							{
								{
									ParPhase =
									{
										phaseTime   = 2.0,
										colorMin    = "0xff5ceb85",
										colorMax    = "0xff5ceb85",
										sizeMin     = vector2dNew(0.3,0.3), 
										sizeMax     = vector2dNew(0.8,0.8), 
									}
								}, 
								{
									ParPhase =
									{
										phaseTime   = 0.3,
										colorMin    = "0x005ceb85",
										colorMax    = "0x005ceb85",
										sizeMin     = vector2dNew(0.8,0.8), 
										sizeMax     = vector2dNew(1.5,1.5), 
									}
								},
							},
							renderPrio           = 1,
							--GFX_BLEND_UNKNOWN = 0,,
							--GFX_BLEND_COPY = 1,
							--GFX_BLEND_ALPHA = 2,
							--GFX_BLEND_ALPHAPREMULT = 3,
							--GFX_BLEND_ALPHADEST = 4,
							--GFX_BLEND_ALPHADESTPREMULT = 5,
							--GFX_BLEND_ADD = 6,
							--GFX_BLEND_ADDALPHA = 7,
							--GFX_BLEND_SUBALPHA = 8,
							--GFX_BLEND_SUB = 9,
							--GFX_BLEND_MUL = 10 ,
							--GFX_BLEND_ALPHAMUL = 11,
							--GFX_BLEND_IALPHAMUL = 12 ,
							--GFX_BLEND_IALPHA = 13,
							--GFX_BLEND_IALPHAPREMULT = 14,
							--GFX_BLEND_IALPHADEST = 15,
							--GFX_BLEND_IALPHADESTPREMULT = 16,
							--GFX_BLEND_MUL2X = 17,
							blendMode          = 2,
							--PARGEN_GEN_POINTS = 0
							--PARGEN_GEN_RECTANGLE = 1
							--PARGEN_GEN_CIRCLE = 2
							genGenType         = 2,
							circleradius         = 0.5,
							innercircleradius         =  0.01,
							genangmin = -80,
							genangmax = 80,

							--PARGEN_MODE_FOLLOW  = 0
							--PARGEN_MODE_COMPLEX = 1
							genMode            = 1, 
						}
					},
				}
			},
		}
	}
}
appendTable(component.FxBankComponent_Template.Fx,fx)
