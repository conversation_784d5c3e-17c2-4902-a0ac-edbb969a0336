includeReference("Actor/Includes/helpers.ilu")
includeReference("Actor/Includes/Sound/sound_base.ilu")
component = 
{
    NAME="FXControllerComponent_Template",
    FXControllerComponent_Template = 
	{
		fxControlList = 
		{		
			{FXControl={name="MRK_Takeoff",sound="MRK_Takeoff"}},
			-- {FXControl={name="MRK_Takeoff",sounds={{VAL="Takeoff"},{VAL="Takeoff_add"}}}},
			-- {FXControl={name="MRK_Offstand",sound="MRK_Offstand",fxStopOnEndAnim=1,fxPlayOnce=1}},
			-- {FXControl={name="MRK_Land",sound="MRK_Land"}},
		},
	},
}

appendTable(params.Actor_Template.COMPONENTS,{component})

