includeReference("Actor/Includes/Sound/sound_base.ilu")
includeReference("Actor/Includes/helpers.ilu")
component =
{
    NAME="SoundComponent_Template",
    SoundComponent_Template =
    {
        soundList = 
        {
        
            {   
                SoundDescriptor_Template=
                {
					name="MRK_Takeoff",
					volume=-19,
					category="Environment",
					limitCategory="butterfly_takeoff",
					params = MONO_3D_RANDOM_NOVOL_NOPITCH_NOFADE,
					files=
					{
						{VAL="Sound/Friendly/Butterfly/B_takeoff01.wav",} ,
						{VAL="Sound/Friendly/Butterfly/B_takeoff02.wav",} ,
						{VAL="Sound/Friendly/Butterfly/B_takeoff03.wav",} ,
						{VAL="Sound/Friendly/Butterfly/B_takeoff04.wav",} ,
						{VAL="Sound/Friendly/Butterfly/B_takeoff05.wav",} ,
						{VAL="Sound/Friendly/Butterfly/B_takeoff06.wav",} ,
						
						-- {VAL="Sound/Friendly/Butterfly/ButterflyTakeOff01.wav",} ,
					}				

				} 
			},
			
			{				
				SoundDescriptor_Template=
				{

					name="Takeoff_add",
					volume=-40,
					category="Environment",
					limitCategory="butterfly_offstand",
					params=
					{
						SoundParams=
						{
							numChannels=1,
							loop=0, --if loop = 1 special behaviour for random and sequence
							playMode=PlayMode.Random, --0 for first,1 for random, 2 for random remember last, 3 for random sequence, 4 for sequence
							randomVolMin=0.0,
							randomVolMax=0.0,
							randomPitchMin=0.9,
							randomPitchMax=1.1,
							fadeInTime=0.5,
							fadeOutTime=0.1,
							modifiers=
							{
								{
									NAME="SpatializedPanning",
									SpatializedPanning=
									{
										widthMin = 0.5,
										widthMax = 2.0,
									}
								},
								{
									NAME="ScreenRollOff",
									ScreenRollOff=
									{
										distanceMin = 0.2,
										distanceMax = 2.0,
									}
								},
								
							}
						}	
					},
					files=
					{
						{VAL="Sound/Friendly/Butterfly/B_offstand01.wav",} ,
						{VAL="Sound/Friendly/Butterfly/B_offstand02.wav",} ,
						{VAL="Sound/Friendly/Butterfly/B_offstand03.wav",} ,
						{VAL="Sound/Friendly/Butterfly/B_offstand04.wav",} ,
					}

				}		
			},
				
			{				
				SoundDescriptor_Template=
				{

					name="MRK_Offstand",
					volume=-45,
					category="Environment",
					limitCategory="butterfly_offstand",
					params = MONO_3D_RANDOM_NOVOL_SMALLPITCH_BIGFADE_LOOP,
					files=
					{
						{VAL="Sound/Friendly/Butterfly/B_offstand01.wav",} ,
						{VAL="Sound/Friendly/Butterfly/B_offstand02.wav",} ,
						{VAL="Sound/Friendly/Butterfly/B_offstand03.wav",} ,
						{VAL="Sound/Friendly/Butterfly/B_offstand04.wav",} ,
					}

				}		
			},
			
			{
				SoundDescriptor_Template=
				{									   
					name="MRK_Land",
					volume=-45,
					category="Environment",
					limitCategory="butterfly_land",
					params= MONO_3D_RANDOM_NOVOL_SMALLPITCH_BIGFADE,
					files=
					{
						{VAL="Sound/Friendly/Butterfly/B_land.wav",} ,
						
					}			
				}
			},
		}
	}
 }
 appendTable(params.Actor_Template.COMPONENTS,{component})
