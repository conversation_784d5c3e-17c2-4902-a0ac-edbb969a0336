

DESCRIPTOR = 
{
	{
                SoundDescriptor_Template=
                {
			        name="MRK_Squashed_Voice",
			        WwiseEventGuid="0E7AC2FD-349F-483B-99C4-77806D971165",
                    volume=-20,
			        category="NPC_CAT_01",
					--limitCategory="Fakir_Squash_Voice",
					limitMode=LimiterMode.RejectNew,
					maxInstances=3,
                    params =
					{
						SoundParams =
						{
							numChannels = 1,
							loop = 0,
							playMode = 1,
							randomVolMin = 0.000000,
							randomVolMax = 0.000000,
							randomPitchMin = 1.0,
							randomPitchMax = 1.0,
							fadeInTime = 0.00000,
							fadeOutTime = 0.000000,
							modifiers=
							{
						
								{
									NAME="SpatializedPanning",
									SpatializedPanning=
									{
										widthMin = 0.5,
										widthMax = 2.0,
									}
								},
								{
									NAME="ScreenRollOff",
									ScreenRollOff=
									{
										distanceMin = 0.5,
										distanceMax = 2.0,
									}
								},
								
							}
						},
						
					},
                    files=
                    {
                        { VAL = "Sound/200_Characters/210_Common/Fakir/Vox_Fakir_PlateSquash.wav", },
						
				        
                    }
                }
	},			
}

appendTable(component.SoundComponent_Template.soundList,DESCRIPTOR)
