includeReference("Actor/Includes/gameplay_types.ilu")

PhantomComponent_Template =
{NAME = "PhantomComponent_Template",
PhantomComponent_Template =
{
    collisionGroup = CollisionGroup.Character,
    -- drawDebug = 1,
    shape = 
        {
            NAME = "PhysShapePolygon",
            PhysShapePolygon =
            {
                Points =
                {
                    { VAL = vector2dNew(-1.0,-1.0) },
                    { VAL = vector2dNew(-1.0,1.0) },
                    { VAL = vector2dNew(1.0,1.0) },
                    { VAL = vector2dNew(1.0,-1.0) },
                },
            }
        },
}}
appendTable(params.Actor_Template.COMPONENTS,{PhantomComponent_Template})
PhantomComponent_Template = {}

component =
{
    NAME="SimpleAIComponent_Template",
    SimpleAIComponent_Template =
    {
        genericBehavior =
        {
            NAME = "AIRoamingBehavior_Template",
            AIRoamingBehavior_Template =
            {
                idle = 
                    {
                        NAME="AIIdleAction_Template",
                        AIIdleAction_Template=
                        {
                            minTime = 0.3,
                            maxTime = 1.0,
                        }
                    },
                move = 
                    {
                        NAME="AIWalkInDirAction_Template",
                        AIWalkInDirAction_Template=
                        {
                            minTime = 2.0,
                            maxTime = 3.0,
                            walkForce = 4.0,
                            walkAnimRate = 3.0,
                        }
                    },
            },
        },
        receiveHitBehavior =
            {NAME="Ray_AIReceiveHitBehavior_Template",
            Ray_AIReceiveHitBehavior_Template =
            {
                receiveHits =
                {
                    -- all types, all levels
                    {ReceiveHitData =
                    {
                        action = 
                            {NAME="Ray_AIFlyReceiveHitAction_Template",
                            Ray_AIFlyReceiveHitAction_Template=
                            {
                                stunTime = 3.0,
                                ejectForce = 480.0,
                                stabilizeForce = 10.0,
                                rotateRateSpeed = 20.0,
                            }},
                    }},
                },
            }},
        deathBehavior =
        {
            NAME = "Ray_AIDeathBehavior_Template",
            Ray_AIDeathBehavior_Template =
            {
                actions =
                {
                    {NAME="AIPlayAnimAction_Template",
                    AIPlayAnimAction_Template =
                    {
                        action = "DEATH"
                    }},
                    {NAME="AIFadeAction_Template",
                    AIFadeAction_Template =
                    {
                        visible = 0,
                        fadeDuration = 2,
                    }},
                },
            }
        },
        
        faction = Faction.Enemy,
        damageLevels =
        {
            { VAL = 25 },
            { VAL = 50 },
            { VAL = 100 },
        },
    }
}

appendTable(params.Actor_Template.COMPONENTS,{component})
component = {}
