<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-90"/>
			<AngleLocal type="number" value="-90"/>
			<Lenght type="number" value="0.033445600420237"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Hair_01_L"/>
			<Pos type="vector" x="0.043809432536364" y="0.044342692941427" z="0"/>
			<PosEnd type="vector" x="0.043809432536364" y="0.077788293361664" z="0"/>
			<PosLocal type="vector" x="0.043809432536364" y="0.044342692941427" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp2505"/>
				<Element index="2" type="string" value="MyApp2506"/>
				<Element index="3" type="string" value="MyApp2507"/>
				<Element index="4" type="string" value="MyApp2508"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A36"/>
			<UID type="string" value="MyApp2504"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A36.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Fx_Flash_01"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2506"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2504"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-1.4021738767624" y="0.044910456985235" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="1" y="0" z="0"/>
			<PosUV type="vector" x="0.088719889521599" y="-0.0025538532063365" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A36"/>
			<UID type="string" value="MyApp2505"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2505"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2504"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="-1.3478260040283" y="-0.04890938103199" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-1" y="0" z="0"/>
			<PosUV type="vector" x="-0.0050999484956264" y="-0.00073615834116936" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A36"/>
			<UID type="string" value="MyApp2506"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2508"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2504"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="1.************" y="0.043819840997458" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="1" y="0" z="0"/>
			<PosUV type="vector" x="0.087629273533821" y="0.088330924510956" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A36"/>
			<UID type="string" value="MyApp2507"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2507"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2504"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="1.2934783697128" y="-0.047818761318922" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-1" y="0" z="0"/>
			<PosUV type="vector" x="-0.0040093287825584" y="0.087603852152824" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A36"/>
			<UID type="string" value="MyApp2508"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A36"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="604.49182128906" y="275.02496337891" z="0"/>
	<zoomSize type="vector" x="2750.7358398438" y="2750.7377929688" z="2.6500356197357"/>
</root>
