component = 
{
    NAME="FxBankComponent_Template",
    FxBankComponent_Template= 
    {
        matTableList =
        {
            -- {
                -- MatTable=
                -- {
                    -- name="FX_break", 
                    -- matFxList = 
                    -- {
                        -- {
                            -- MatFx=
                            -- {
                                -- material="__default__",
                                -- fx="FX_break_jade_01"
                            -- }
                        -- },
                        -- {
                            -- MatFx=
                            -- {
                                -- material="GameMaterial/Stackable_Stone.gmt",
                                -- fx="FX_break_jade_01"
                            -- }
                        -- }, 
                        -- {
                            -- MatFx=
                            -- {
                                -- material="GameMaterial/Wall_Rock.gmt",
                                -- fx="FX_ground_rock"
                            -- }
                        -- },
                    -- },
                -- },
            -- },
        },
        Fx = 
        { 
		}
    }
}
includeReference("Actor/Breakable/Common/FX/FX_break_jade.ilu")
includeReference("Actor/Enemy/Common/FX/FX_hit_weak.ilu")
appendTable(params.Actor_Template.COMPONENTS,{component})
