includeReference("Actor/Includes/gameplay_types.ilu")

component = 
{
    NAME="AnimatedComponent_Template",
    AnimatedComponent_Template = 
    {
        animationPath = "Actor/Enemy/Ant_BasicSoldier/Animation/",
        defaultAnimation = "IDLE",
		animSet=
		{
			SubAnimSet_Template =
			{
		        animations=
		        {
		            {
		                NAME="SubAnim_Template",
		                SubAnim_Template=
		                {
		                    friendlyName="Idle",
		                    name="Fmi_Stand.anm",
		                    loop=1,
		                }
		            },
		            {
		                NAME="SubAnim_Template",
		                SubAnim_Template=
		                {
		                    friendlyName="Walk",
		                    name="Fmi_Marche.anm",
		                    loop=1,
		                    markerStart="MRK_Marche_Start",
		                    markerStop="MRK_Marche_Stop",
		                }
		            },
		            {
		                NAME="SubAnim_Template",
		                SubAnim_Template=
		                {
		                    friendlyName="IdleToWalk",
		                    name="Fmi_Marche.anm",
		                    markerStart="MRK_StandtrMarche_Start",
		                    markerStop="MRK_StandtrMarche_Stop",
		                }
		            },
		            {
		                NAME="SubAnim_Template",
		                SubAnim_Template=
		                {
		                    friendlyName="WalkToIdle",
		                    name="Fmi_Marche.anm",
		                    markerStart="MRK_MarchetrStand_Start",
		                    markerStop="MRK_MarchetrStand_Stop",
		                }
		            },
		            {
		                NAME="SubAnim_Template",
		                SubAnim_Template=
		                {
		                    friendlyName="ReceiveHit",
		                    name="Fmi_Paf.anm",
		                    markerStart="MRK_Paf_Start",
		                    markerStop="MRK_Paf_Stop",
		                }
		            },
		            {
		                NAME="SubAnim_Template",
		                SubAnim_Template=
		                {
		                    friendlyName="ScreenEjectStart",
		                    name="Paf_Ejection_Screen.anm",
		                    markerStart="MRK_Paf_Start",
		                    markerStop="MRK_Paf_Stop",
		                }
		            },
		            {
		                NAME="SubAnim_Template",
		                SubAnim_Template=
		                {
		                    friendlyName="ScreenEject",
		                    name="Paf_Ejection_Screen.anm",
		                    markerStart="MRK_Ejection_Start",
		                    markerStop="MRK_Ejection_Stop",
		                }
		            },
		            {
		                NAME="SubAnim_Template",
		                SubAnim_Template=
		                {
		                    friendlyName="Stunned",
		                    name="Fmi_Stand_Etourdi.anm",
		                    loop=1,
		                }
		            },
		            {
		                NAME="SubAnim_Template",
		                SubAnim_Template=
		                {
		                    friendlyName="StunnedToStand",
		                    name="Etourdi_tr_Stand.anm",
		                }
		            },
		        },
			},
		},
        inputs =
        {
            {InputDesc={name="RandomN", varType=AnimInputTypes.float}},              -- Used to play random animations
            {InputDesc={name="ReceivedHitLevel", varType=AnimInputTypes.uint}},      -- Level of the last received hit
            {InputDesc={name="ReceivedHitType", varType=AnimInputTypes.uint}},       -- Type of the hit being received
            {InputDesc={name="Stunned", varType=AnimInputTypes.uint}},               -- If the character is stunned
        },
        tree = 
        {
            AnimTree_Template =
            {
                nodes =
                {
                    {
                        NAME="AnimTreeNodePlayAnim_Template",
                        AnimTreeNodePlayAnim_Template =
                        {
                            nodeName = "IDLE",
                            animationName = "Idle"
                        }
                    },
                    {
                        NAME="AnimTreeNodePlayAnim_Template",
                        AnimTreeNodePlayAnim_Template =
                        {
                            nodeName = "WALK",
                            animationName = "Walk"
                        }
                    },
                    {
                        NAME="BlendTreeNodeChooseBranch_Template",
                        BlendTreeNodeChooseBranch_Template =
                        {
                            nodeName = "RECEIVEHIT",
                            leafs =
                            {
                                {
                                    NAME="AnimTreeNodePlayAnim_Template",
                                    AnimTreeNodePlayAnim_Template =
                                    {
                                        nodeName = "Camera_Eject",
                                        animationName = "ScreenEject",
                                    }
                                },
                                {
                                    NAME="AnimTreeNodePlayAnim_Template",
                                    AnimTreeNodePlayAnim_Template =
                                    {
                                        nodeName = "Stunned",
                                        animationName = "Stunned",
                                    }
                                },
                                {
                                    NAME="AnimTreeNodePlayAnim_Template",
                                    AnimTreeNodePlayAnim_Template =
                                    {
                                        animationName="ReceiveHit",
                                    }
                                },
                            },
                            leafsCriterias =
                            {
                                {
                                    BlendLeaf =
                                    {
                                        -- Camera eject
                                        criterias =
                                        {
                                            {CriteriaDesc={name="ReceivedHitType",eval="==",value=ReceivedHitType.EjectXY}},
                                        },
                                    }
                                },
                                {
                                    BlendLeaf =
                                    {
                                        -- Stunned
                                        criterias =
                                        {
                                            {CriteriaDesc={name="Stunned",eval="==",value=1}},
                                        },
                                    }
                                },
                                {
                                    BlendLeaf =
                                    {
                                        -- Normal
                                    }
                                },
                            },
                        }
                    },
                    {
                        NAME="AnimTreeNodePlayAnim_Template",
                        AnimTreeNodePlayAnim_Template =
                        {
                            nodeName = "DEATH",
                            animationName = "ReceiveHit"
                        }
                    },
                },
                nodeTransitions =
                {
                    {
                        BlendTreeTransition_Template =
                        {
                            from =
                            {
                                { VAL = "IDLE" },
                            },
                            to =
                            {
                                { VAL = "WALK" },
                            },
                            node =
                            {
                                NAME="AnimTreeNodePlayAnim_Template",
                                AnimTreeNodePlayAnim_Template =
                                {
                                    animationName = "IdleToWalk"
                                }
                            },
                        }
                    },
                    {
                        BlendTreeTransition_Template =
                        {
                            from =
                            {
                                { VAL = "WALK" },
                            },
                            to =
                            {
                                { VAL = "IDLE" },
                            },
                            node =
                            {
                                NAME="AnimTreeNodePlayAnim_Template",
                                AnimTreeNodePlayAnim_Template =
                                {
                                    animationName = "WalkToIdle"
                                }
                            },
                        }
                    },
                    {
                        BlendTreeTransition_Template =
                        {
                            to =
                            {
                                { VAL = "Camera_Eject" },
                            },
                            node =
                            {
                                NAME="AnimTreeNodePlayAnim_Template",
                                AnimTreeNodePlayAnim_Template =
                                {
                                    animationName = "ScreenEjectStart"
                                }
                            },
                        }
                    },
                    {
                        BlendTreeTransition_Template =
                        {
                            from =
                            {
                                { VAL = "Stunned" },
                            },
                            to =
                            {
                                { VAL = "IDLE" },
                                { VAL = "WALK" },
                            },
                            node =
                            {
                                NAME="AnimTreeNodePlayAnim_Template",
                                AnimTreeNodePlayAnim_Template =
                                {
                                    animationName = "StunnedToStand"
                                }
                            },
                        }
                    },
                },
            }
        },
    }
}

appendTable(params.Actor_Template.COMPONENTS,{component})
component = {}

