component =
{
    NAME="ParticlePhysComponent_Template",
    ParticlePhysComponent_Template =
    {
        physRadius = 0.6,                               -- The radius of the collision
        physFriction = 1.5,                             -- The character friction
        physUnstickMinAngle = 10,                       -- Minimum angle between speed and edge to unstick from edge
        physUnstickMaxAngle = 40,                       -- Maximum angle between speed and edge to unstick from edge
        physUnstickMinAngleSpeed = 10,                  -- Speed to unstick from edge at minimum angle between edge and speed
        physUnstickMaxAngleSpeed = 0,                   -- Speed to unstick from edge at maximum angle between edge and speed
        physAngularSpeedMinLinear = 5,                  -- Minimum linear speed to translate to angular speed
        physAngularSpeedMaxLinear = 35,                 -- Maximum linear speed to translate to angular speed
        physAngularSpeedMinAngular = 100.0,             -- Angular speed at minimum linear speed
        physAngularSpeedMaxAngular = 500.0,             -- Angular speed at maximum linear speed
        physLandSpeedLossMinAngle=0,				    -- : Angle difference for minimum speed loss coefficient when landing on a surface
        physLandSpeedLossMaxAngle=90,			        -- : Angle difference for maximum speed loss coefficient when landing on a surface
        physLandSpeedLossMinLoss=0,			            -- : Loss at minimum angle difference when landing on a surface
        physLandSpeedLossMaxLoss=0.1,			        -- : Loss at maximum angle difference when landing on a surface
        physWeight = 1.0,                               -- Weight of rayman
        physEjectMultiplier = 50.0,                     -- Multiplier to the force applied when we are on a moving platform that changed direction
        physEjectThreshold = 5.0,                       -- The eject force is not applied if the speed of the platform is not bigger than this
        physDefaultStickMin =183, 			            -- min angle for default sticky area (0-360)
        physDefaultStickMax =355,			            -- mAX angle for default sticky area (0-360)
    }
}

appendTable(params.Actor_Template.COMPONENTS,{component})
component = {}
