<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="90.************"/>
			<AngleLocal type="number" value="90.************"/>
			<Lenght type="number" value="0.11415082961321"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Foot_L"/>
			<Pos type="vector" x="0.064176231622696" y="0.74422520399094" z="0"/>
			<PosEnd type="vector" x="0.063569039106369" y="0.63007599115372" z="0"/>
			<PosLocal type="vector" x="0.064176231622696" y="0.74422520399094" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="D2450MyApp2439"/>
				<Element index="2" type="string" value="D2450MyApp2440"/>
				<Element index="3" type="string" value="D2450MyApp2441"/>
				<Element index="4" type="string" value="D2450MyApp2442"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A31"/>
			<UID type="string" value="D2450MyApp2438"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A31.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Fx_Wrap_02"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2450MyApp2440"/>
			<Color>
				<A type="number" value="200"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2450MyApp2438"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.10506888478994" y="0.99446499347687" z="0"/>
					<Pos type="vector" x="-0.17666679620743" y="0.060211516916752" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.99500977993011" y="-0.09977762401104" z="0"/>
			<PosUV type="vector" x="0.0040728375315666" y="0.76471185684204" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A31"/>
			<UID type="string" value="D2450MyApp2439"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2450MyApp2439"/>
			<Color>
				<A type="number" value="200"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2450MyApp2438"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.089354388415813" y="-0.99599987268448" z="0"/>
					<Pos type="vector" x="0.031233182176948" y="-0.050897181034088" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99551057815552" y="-0.094651065766811" z="0"/>
			<PosUV type="vector" x="0.11505372822285" y="0.74038922786713" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A31"/>
			<UID type="string" value="D2450MyApp2440"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2450MyApp2442"/>
			<Color>
				<A type="number" value="200"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2450MyApp2438"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.20897537469864" y="0.97792088985443" z="0"/>
					<Pos type="vector" x="1.0398658514023" y="0.052792377769947" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.97901862859726" y="-0.20377065241337" z="0"/>
			<PosUV type="vector" x="0.010753203183413" y="0.62580615282059" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A31"/>
			<UID type="string" value="D2450MyApp2441"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2450MyApp2441"/>
			<Color>
				<A type="number" value="200"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2450MyApp2438"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.08411106467247" y="-0.99645638465881" z="0"/>
					<Pos type="vector" x="1.0957989692688" y="-0.042577683925629" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99599498510361" y="-0.089410245418549" z="0"/>
			<PosUV type="vector" x="0.10608795285225" y="0.61891412734985" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A31"/>
			<UID type="string" value="D2450MyApp2442"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A31"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="430.44332885742" y="-1703.9814453125" z="0"/>
	<zoomSize type="vector" x="3464.8679199219" y="3464.8671875" z="3.0908718109131"/>
</root>
