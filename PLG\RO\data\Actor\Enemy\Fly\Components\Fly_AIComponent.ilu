includeReference("Actor/Includes/helpers.ilu")
includeReference("Actor/Includes/gameplay_types.ilu")

PhantomComponent_Template =
{NAME = "PhantomComponent_Template",
PhantomComponent_Template =
{
    collisionGroup = CollisionGroup.Character,
    -- drawDebug = 1,
    shape = 
        {
            NAME = "PhysShapePolygon",
            PhysShapePolygon =
            {
                Points =
                {
                    { VAL = vector2dNew(-1.0,-1.0) },
                    { VAL = vector2dNew(-1.0,1.0) },
                    { VAL = vector2dNew(1.0,1.0) },
                    { VAL = vector2dNew(1.0,-1.0) },
                },
            }
        },
}}
appendTable(params.Actor_Template.COMPONENTS,{PhantomComponent_Template})
PhantomComponent_Template = {}

component =
{
    NAME="Ray_FlyAIComponent_Template",
    Ray_FlyAIComponent_Template =
    {
       -- aiBonusLua = "Actor/Weapon/FirePunch/FirePunch_Spawn.act",
       -- aiBonusBone = "B_Mch_Bras_D",
        
        followBehavior =
        {
            Ray_AIFlyFollowPlayerBehavior_Template =
            {
                aiReturnWaitTime = 3.0,
                aiMoveFromGroundWaitTime = 1.0,
                aiDetectRadius = 10.0,
                flyIdle = 
                {
                    NAME="Ray_AIFlyIdleAction_Template",
                    Ray_AIFlyIdleAction_Template=
                    {
                    }
                },
                flyMove = 
                {
                    NAME="Ray_AIFlyMoveAction_Template",
                    Ray_AIFlyMoveAction_Template=
                    {
                        aiFlyMaxSpeed = 5.0,
                    }
                },
                flyMoveInDir=
                {
                    NAME="Ray_AIFlyMoveInDirAction_Template",
                    Ray_AIFlyMoveInDirAction_Template=
                    {
                        aiFlyMaxSpeed = 5.0,
                    }
                },
            }
        },
        
        receiveHitBehavior =
            {NAME="Ray_AIReceiveHitBehavior_Template",
            Ray_AIReceiveHitBehavior_Template =
            {
                receiveHits =
                {
                    -- all types, all levels
                    {ReceiveHitData =
                    {
                        action = 
                            {NAME="Ray_AIFlyReceiveHitAction_Template",
                            Ray_AIFlyReceiveHitAction_Template=
                            {
                                stunTime = 3.0,
                                ejectForce = 480.0,
                                stabilizeForce = 10.0,
                                rotateRateSpeed = 20.0,
                            }},
                    }},
                },
            }},
        
        deathBehavior =
        {
            Ray_AIDeathBehavior_Template =
            {
                actions =
                {
                    {
                        NAME="AIPlayFXAction_Template",
                        AIPlayFXAction_Template=
                        {
                            action = "DEAD",
                            fxName = "MRK_Death",
                        }
                    },
                },
            }
        },

        faction = Faction.Enemy,
        damageLevels =
        {
            { VAL = 25 },
            { VAL = 50 },
            { VAL = 100 },
        },
    }
}

appendTable(params.Actor_Template.COMPONENTS,{component})
component = {}
