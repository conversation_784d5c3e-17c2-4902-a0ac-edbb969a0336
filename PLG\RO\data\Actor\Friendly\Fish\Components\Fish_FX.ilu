includeReference("Actor/Includes/Sound/sound_base.ilu")
includeReference("Actor/Includes/helpers.ilu")

component = 
{
    NAME = "FXControllerComponent_Template",
    FXControllerComponent_Template=
    {
        fxControlList =
        {
            --{name="MRK_example",sound="example",playContact=1,fxStopOnEndAnim=1,fxPlayOnce=1},
		    --COMMON
            {
                FXControl =
                {
                    name="MRK_Fish_Material",
                    sound="MRK_Fish_Material",
                }
            },
			{
                FXControl =
                {
                    name="MRK_Fish_Lum_Material",
                    sound="MRK_Fish_Lum_Material",
                }
            },
        },
		fxHit =
        {
            {
                FXHit =
                {
                    instigator="rayman",
                    level=0,
                    name="Punch_Weak",
                }
            },
            {
                FXHit =
                {
                    instigator="rayman",
                    level=1,
                    name="Punch_Strong",
                }
            },
            {
                FXHit =
                {
                    instigator="rayman",
                    level=2,
                    name="Punch_Mega",
                }
            },
            {
                FXHit =
                {
                    instigator="rayman2",
                    level=0,
                    name="Punch_Weak",
                }
            },
            {
                FXHit =
                {
                    instigator="rayman2",
                    level=1,
                    name="Punch_Strong",
                }
            },
            {
                FXHit =
                {
                    instigator="rayman2",
                    level=2,
                    name="Punch_Mega",
                }
            },
            {
                FXHit =
                {
                    instigator="globox",
                    level=0,
                    name="Slap_Weak",
                }
            },
            {
                FXHit =
                {
                    instigator="globox",
                    level=1,
                    name="Slap_Strong",
                }
            },
			{
                FXHit =
                {
                    instigator="globox",
                    level=2,
                    name="Slap_Mega",
                }
            },
            {
                FXHit =
                {
                    instigator="globox2",
                    level=0,
                    name="Slap_Weak",
                }
            },
            {
                FXHit =
                {
                    instigator="globox2",
                    level=1,
                    name="Slap_Strong",
                }
            },
            {
                FXHit =
                {
                    instigator="globox2",
                    level=2,
                    name="Slap_Mega",
                }
            },
        },
    },
}
appendTable(params.Actor_Template.COMPONENTS,{component})
