<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-1.7152496365758"/>
			<AngleLocal type="number" value="-1.7152496365758"/>
			<Lenght type="number" value="0.01271131914109"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Bonus_01"/>
			<Pos type="vector" x="0.97530776262283" y="0.044478621333838" z="0"/>
			<PosEnd type="vector" x="0.98801338672638" y="0.044859100133181" z="0"/>
			<PosLocal type="vector" x="0.97530776262283" y="0.044478621333838" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp1699"/>
				<Element index="2" type="string" value="MyApp1700"/>
				<Element index="3" type="string" value="MyApp1701"/>
				<Element index="4" type="string" value="MyApp1702"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A19"/>
			<UID type="string" value="MyApp1698"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A19.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Bonus_01"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp1700"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp1698"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-0.32495114207268" y="0.030312227085233" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.029932284727693" y="-0.99955201148987" z="0"/>
			<PosUV type="vector" x="0.9720863699913" y="0.014056341722608" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A19"/>
			<UID type="string" value="MyApp1699"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp1699"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp1698"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="-2.4288609027863" y="-0.038898274302483" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.029932284727693" y="0.99955201148987" z="0"/>
			<PosUV type="vector" x="0.94328325986862" y="0.082435332238674" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A19"/>
			<UID type="string" value="MyApp1700"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp1702"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp1698"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="1.7319542169571" y="0.031215950846672" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.029932284727693" y="-0.99955201148987" z="0"/>
			<PosUV type="vector" x="0.99824768304825" y="0.013935631141067" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A19"/>
			<UID type="string" value="MyApp1701"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp1701"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp1698"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="0.14512769877911" y="-0.039766870439053" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.029932284727693" y="0.99955201148987" z="0"/>
			<PosUV type="vector" x="0.97596138715744" y="0.084282889962196" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A19"/>
			<UID type="string" value="MyApp1702"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A19"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="-8274.7861328125" y="145.24685668945" z="0"/>
	<zoomSize type="vector" x="9156.5908203125" y="9156.5849609375" z="8.6220235824585"/>
</root>
