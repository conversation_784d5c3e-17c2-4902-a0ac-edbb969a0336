params =
{
    
NAME = "Actor_Template",
Actor_Template =
    {
        COMPONENTS =
        {
            {
                NAME="GravityComponent_Template",
                GravityComponent_Template =
                {
                    gravityAreas =
                    {
                        {
                            PhysForceModifier =
                            {
                                force=vector2dNew(40.0,0.0),
                                Type = 0,
                                point=1,
                                inverted=0,
                                gradientPercentage=0,
                                Circle =
                                {
                                    CircleData =
                                    {
                                        radius=5.0,
                                        angleStart=180.0,
                                        angleEnd=270.0,
                                    }
                                }
                            },
                        }
                    }
                }
            }
        }
    }
}
