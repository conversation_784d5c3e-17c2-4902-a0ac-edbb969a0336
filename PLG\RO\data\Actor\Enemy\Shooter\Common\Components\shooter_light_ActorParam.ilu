includeReference("Actor/Includes/helpers.ilu")
includeReference("Actor/Includes/gameplay_types.ilu")

Shooter_components =
{
	NAME = "Ray_ShooterActorParameterComponent_Template",
	Ray_ShooterActorParameterComponent_Template =
	{
		vacuumData =
		{
			NAME = "Ray_VacuumData_Template",
			Ray_VacuumData_Template =
			{
				vacuumMinDuration = 0.1,
				vacuumMaxDuration = 0.2,
			},
		},
		
		 --vaccumObjTypeName = "LIGHT_ENEMY",
	},
}

appendTable(params.Actor_Template.COMPONENTS,{Shooter_components})
Shooter_components = {}
