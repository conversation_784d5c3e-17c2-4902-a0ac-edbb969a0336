<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-82.127976191819"/>
			<AngleLocal type="number" value="-82.127976191819"/>
			<Lenght type="number" value="0.071864999830723"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Hair_01_R"/>
			<Pos type="vector" x="0.83710777759552" y="0.24128423631191" z="0"/>
			<PosEnd type="vector" x="0.84695047140121" y="0.31247201561928" z="0"/>
			<PosLocal type="vector" x="0.83710777759552" y="0.24128423631191" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="D473MyApp426"/>
				<Element index="2" type="string" value="D473MyApp427"/>
				<Element index="3" type="string" value="D473MyApp428"/>
				<Element index="4" type="string" value="D473MyApp429"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A7"/>
			<UID type="string" value="D473MyApp422"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="2">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-78.************"/>
			<AngleLocal type="number" value="-78.************"/>
			<Lenght type="number" value="0.065406404435635"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Hair_02_R"/>
			<Pos type="vector" x="0.87693631649017" y="0.24174204468727" z="0"/>
			<PosEnd type="vector" x="0.88998359441757" y="0.30583390593529" z="0"/>
			<PosLocal type="vector" x="0.87693631649017" y="0.24174204468727" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="D473MyApp430"/>
				<Element index="2" type="string" value="D473MyApp431"/>
				<Element index="3" type="string" value="D473MyApp432"/>
				<Element index="4" type="string" value="D473MyApp433"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A7"/>
			<UID type="string" value="D473MyApp423"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="3">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-76.************"/>
			<AngleLocal type="number" value="-76.************"/>
			<Lenght type="number" value="0.060514811426401"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Hair_03_R"/>
			<Pos type="vector" x="0.92157173156738" y="0.24082644283772" z="0"/>
			<PosEnd type="vector" x="0.93576347827911" y="0.29965361952782" z="0"/>
			<PosLocal type="vector" x="0.92157173156738" y="0.24082644283772" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="D473MyApp434"/>
				<Element index="2" type="string" value="D473MyApp435"/>
				<Element index="3" type="string" value="D473MyApp436"/>
				<Element index="4" type="string" value="D473MyApp437"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A7"/>
			<UID type="string" value="D473MyApp424"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="4">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-75.************"/>
			<AngleLocal type="number" value="-75.************"/>
			<Lenght type="number" value="0.054295055568218"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Hair_04_R"/>
			<Pos type="vector" x="0.96002686023712" y="0.24174204468727" z="0"/>
			<PosEnd type="vector" x="0.97330302000046" y="0.29438894987106" z="0"/>
			<PosLocal type="vector" x="0.96002686023712" y="0.24174204468727" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="D473MyApp438"/>
				<Element index="2" type="string" value="D473MyApp439"/>
				<Element index="3" type="string" value="D473MyApp440"/>
				<Element index="4" type="string" value="D473MyApp441"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A7"/>
			<UID type="string" value="D473MyApp425"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A7.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Hair01_R"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D473MyApp427"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D473MyApp422"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-0.088652700185776" y="0.012695470824838" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99057650566101" y="-0.1369608938694" z="0"/>
			<PosUV type="vector" x="0.84881103038788" y="0.23323446512222" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A7"/>
			<UID type="string" value="D473MyApp426"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D473MyApp426"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D473MyApp422"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="-0.070899032056332" y="-0.011225641705096" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.99057650566101" y="0.1369608938694" z="0"/>
			<PosUV type="vector" x="0.82529008388519" y="0.23777456581593" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A7"/>
			<UID type="string" value="D473MyApp427"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D473MyApp429"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D473MyApp422"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="1.0824472904205" y="0.013883966952562" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99057650566101" y="-0.1369608938694" z="0"/>
			<PosUV type="vector" x="0.86151510477066" y="0.31643968820572" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A7"/>
			<UID type="string" value="D473MyApp428"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D473MyApp428"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D473MyApp422"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="1.1329298019409" y="-0.014752957969904" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.99057650566101" y="0.1369608938694" z="0"/>
			<PosUV type="vector" x="0.************" y="0.32395556569099" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A7"/>
			<UID type="string" value="D473MyApp429"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="5">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D473MyApp431"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D473MyApp423"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-0.087580017745495" y="0.012681428343058" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.97990190982819" y="-0.1994801312685" z="0"/>
			<PosUV type="vector" x="0.88822019100189" y="0.2335991859436" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A7"/>
			<UID type="string" value="D473MyApp430"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="6">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D473MyApp430"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D473MyApp423"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="-0.04162635654211" y="-0.0094426330178976" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.97990190982819" y="0.1994801312685" z="0"/>
			<PosUV type="vector" x="0.86714035272598" y="0.24095775187016" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A7"/>
			<UID type="string" value="D473MyApp431"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="7">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D473MyApp433"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D473MyApp423"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="1.0823895931244" y="0.014506436884403" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.97990190982819" y="-0.1994801312685" z="0"/>
			<PosUV type="vector" x="0.9052734375" y="0.30822065472603" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A7"/>
			<UID type="string" value="D473MyApp432"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="8">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D473MyApp432"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D473MyApp423"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="1.1149545907974" y="-0.014213860034943" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.97990190982819" y="0.1994801312685" z="0"/>
			<PosUV type="vector" x="0.87755525112152" y="0.31603693962097" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A7"/>
			<UID type="string" value="D473MyApp433"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="9">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D473MyApp435"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D473MyApp424"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-0.10912049561739" y="0.012623223476112" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.97211199998856" y="-0.23451690375805" z="0"/>
			<PosUV type="vector" x="0.93229430913925" y="0.23144683241844" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A7"/>
			<UID type="string" value="D473MyApp434"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="10">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D473MyApp434"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D473MyApp424"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="-0.086200155317783" y="-0.013303848914802" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.97211199998856" y="0.23451690375805" z="0"/>
			<PosUV type="vector" x="0.90741556882858" y="0.23887550830841" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A7"/>
			<UID type="string" value="D473MyApp435"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="11">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D473MyApp437"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D473MyApp424"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="1.0647648572922" y="0.015735933557153" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.97211199998856" y="-0.23451690375805" z="0"/>
			<PosUV type="vector" x="0.95197969675064" y="0.29977321624756" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A7"/>
			<UID type="string" value="D473MyApp436"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="12">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D473MyApp436"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D473MyApp424"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="1.1087464094162" y="-0.016620745882392" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.97211199998856" y="0.23451690375805" z="0"/>
			<PosUV type="vector" x="0.92114955186844" y="0.30994871258736" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A7"/>
			<UID type="string" value="D473MyApp437"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="13">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D473MyApp439"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D473MyApp425"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.099833488464355" y="0.99500423669815" z="0"/>
					<Pos type="vector" x="-0.1164872944355" y="0.011345693841577" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.94038927555084" y="-0.34010019898415" z="0"/>
			<PosUV type="vector" x="0.96948164701462" y="0.23283511400223" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A7"/>
			<UID type="string" value="D473MyApp438"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="14">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D473MyApp438"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D473MyApp425"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="-0.054244697093964" y="-0.01066064555198" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.96964454650879" y="0.24451875686646" z="0"/>
			<PosUV type="vector" x="0.94896966218948" y="0.24149295687675" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A7"/>
			<UID type="string" value="D473MyApp439"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="15">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D473MyApp441"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D473MyApp425"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="1.0607484579086" y="0.020486254245043" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.96964454650879" y="-0.24451875686646" z="0"/>
			<PosUV type="vector" x="0.99397391080856" y="0.29257789254189" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A7"/>
			<UID type="string" value="D473MyApp440"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="16">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D473MyApp440"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D473MyApp425"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.10506887733936" y="-0.99446493387222" z="0"/>
					<Pos type="vector" x="1.1530251502991" y="-0.018455835059285" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.98996883630753" y="0.14128586649895" z="0"/>
			<PosUV type="vector" x="0.95743900537491" y="0.30695804953575" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A7"/>
			<UID type="string" value="D473MyApp441"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A7"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="-3298.5732421875" y="-944.56298828125" z="0"/>
	<zoomSize type="vector" x="4840.6962890625" y="4840.69921875" z="4.6589956283569"/>
</root>
