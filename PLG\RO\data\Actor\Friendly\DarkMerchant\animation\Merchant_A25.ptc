<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="89.************"/>
			<AngleLocal type="number" value="89.************"/>
			<Lenght type="number" value="0.053428821265697"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Pelvis"/>
			<Pos type="vector" x="0.16321223974228" y="0.87452685832977" z="0"/>
			<PosEnd type="vector" x="0.1635400056839" y="0.82109904289246" z="0"/>
			<PosLocal type="vector" x="0.16321223974228" y="0.87452685832977" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="D2218D2007MyApp385"/>
				<Element index="2" type="string" value="D2218D2007MyApp386"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A25"/>
			<UID type="string" value="D2218D2007MyApp383"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="2">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="88.************"/>
			<AngleLocal type="number" value="-0.7342559040932"/>
			<Lenght type="number" value="0.067550905048847"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Spine"/>
			<ParentUID type="string" value="D2218D2007MyApp383"/>
			<Pos type="vector" x="0.1635400056839" y="0.82109904289246" z="0"/>
			<PosEnd type="vector" x="0.16482001543045" y="0.75356024503708" z="0"/>
			<PosLocal type="vector" x="0" y="0" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="D2218D2007MyApp389"/>
				<Element index="2" type="string" value="D2218D2007MyApp390"/>
				<Element index="3" type="string" value="D2218MyApp2079"/>
				<Element index="4" type="string" value="D2218MyApp2078"/>
				<Element index="5" type="string" value="MyApp4816"/>
				<Element index="6" type="string" value="MyApp4815"/>
				<Element index="7" type="string" value="D2218MyApp2073"/>
				<Element index="8" type="string" value="D2218MyApp2072"/>
				<Element index="9" type="string" value="D2218D2007MyApp391"/>
				<Element index="10" type="string" value="D2218D2007MyApp392"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A25"/>
			<UID type="string" value="D2218D2007MyApp384"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A25.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Body06"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2218D2007MyApp386"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2218D2007MyApp383"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-0.19811506569386" y="0.082668595016003" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.99998116493225" y="-0.0061346278525889" z="0"/>
			<PosUV type="vector" x="0.080480262637138" y="0.88460457324982" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A25"/>
			<UID type="string" value="D2218D2007MyApp385"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2218D2007MyApp385"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2218D2007MyApp383"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="-0.14398600161076" y="-0.097134433686733" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99998116493225" y="0.0061346278525889" z="0"/>
			<PosUV type="vector" x="0.26029765605927" y="0.88281559944153" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A25"/>
			<UID type="string" value="D2218D2007MyApp386"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2218D2007MyApp390"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2218D2007MyApp384"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-0.5023330450058" y="0.081121206283569" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.99982047080994" y="-0.018948812037706" z="0"/>
			<PosUV type="vector" x="0.08179035037756" y="0.8534888625145" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A25"/>
			<UID type="string" value="D2218D2007MyApp389"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2218D2007MyApp389"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2218D2007MyApp384"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="-0.34751018881798" y="-0.093028962612152" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99982047080994" y="0.018948812037706" z="0"/>
			<PosUV type="vector" x="0.25610747933388" y="0.8463322520256" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A25"/>
			<UID type="string" value="D2218D2007MyApp390"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="5">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2218D2007MyApp392"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2218D2007MyApp384"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="2.************" y="0.06226933375001" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.99982047080994" y="-0.018948812037706" z="0"/>
			<PosUV type="vector" x="0.10505848377943" y="0.62064641714096" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A25"/>
			<UID type="string" value="D2218D2007MyApp391"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="6">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2218D2007MyApp391"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2218D2007MyApp384"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="3.0719356536865" y="-0.0014347585383803" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99982047080994" y="0.018948812037706" z="0"/>
			<PosUV type="vector" x="0.16890661418438" y="0.61365139484406" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A25"/>
			<UID type="string" value="D2218D2007MyApp392"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="7">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2218MyApp2073"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2218D2007MyApp384"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.10695411264896" y="-0.99426400661469" z="0"/>
					<Pos type="vector" x="1.8491007089615" y="-0.018260080367327" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99611216783524" y="-0.088094793260098" z="0"/>
			<PosUV type="vector" x="0.18416368961334" y="0.69655901193619" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A25"/>
			<UID type="string" value="D2218MyApp2072"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="8">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2218MyApp2072"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2218D2007MyApp384"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.37181776762009" y="0.92830580472946" z="0"/>
					<Pos type="vector" x="1.************" y="0.055095750838518" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.92109364271164" y="-0.38934129476547" z="0"/>
			<PosUV type="vector" x="0.11040338873863" y="0.71720308065414" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A25"/>
			<UID type="string" value="D2218MyApp2073"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="9">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2218MyApp2079"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2218D2007MyApp384"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.79779052734375" y="-0.60293471813202" z="0"/>
					<Pos type="vector" x="1.1310960054398" y="-0.1483305990696" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.61794364452362" y="-0.7862223982811" z="0"/>
			<PosUV type="vector" x="0.31329184770584" y="0.74751687049866" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A25"/>
			<UID type="string" value="D2218MyApp2078"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="10">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2218MyApp2078"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2218D2007MyApp384"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.053921192884445" y="0.99854516983032" z="0"/>
					<Pos type="vector" x="0.45167076587677" y="0.15243062376976" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.9973441362381" y="-0.072832755744457" z="0"/>
			<PosUV type="vector" x="0.011714845895767" y="0.78770536184311" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A25"/>
			<UID type="string" value="D2218MyApp2079"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="11">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp4816"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2218D2007MyApp384"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.97986942529678" y="-0.19963958859444" z="0"/>
					<Pos type="vector" x="1.2024904489517" y="-0.042425904422998" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.21817111968994" y="-0.97591060400009" z="0"/>
			<PosUV type="vector" x="0.20749750733376" y="0.74068820476532" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A25"/>
			<UID type="string" value="MyApp4815"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="12">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp4815"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2218D2007MyApp384"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.89715385437012" y="0.44171810150146" z="0"/>
					<Pos type="vector" x="0.98782533407211" y="0.094609886407852" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.42463880777359" y="-0.90536284446716" z="0"/>
			<PosUV type="vector" x="0.070211499929428" y="0.75258976221085" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A25"/>
			<UID type="string" value="MyApp4816"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A25"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="-0.77978515625" y="-2124.4206542969" z="0"/>
	<zoomSize type="vector" x="3633.8872070313" y="3633.884765625" z="3.6050431728363"/>
</root>
