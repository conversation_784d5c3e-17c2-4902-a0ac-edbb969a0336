# Batch Git Commit Script

A robust Python script designed for large game development projects that need to initialize Git repositories and commit files in manageable batches to avoid memory and performance issues.

## Features

- **Automatic Git Repository Initialization**: Creates a Git repo if one doesn't exist
- **Batch Processing**: Commits files in configurable size batches (default 1GB)
- **Smart File Handling**: 
  - Ski<PERSON> already tracked files
  - Handles large files gracefully
  - Respects .gitignore patterns
- **Game Development Optimized**: 
  - Pre-configured .gitignore patterns for common game dev artifacts
  - Large file detection with Git LFS recommendations
  - Binary file awareness
- **Robust Error Handling**: Continues processing even if individual files fail
- **Progress Tracking**: Detailed logging and progress feedback
- **Dry Run Mode**: Preview changes before execution

## Requirements

- Python 3.6+
- Git installed and accessible from command line
- Write permissions in the target directory

## Usage

### Basic Usage

```bash
# Initialize Git and commit all files in 1GB batches
python batch_git_commit.py

# Use smaller 500MB batches
python batch_git_commit.py --batch-size 0.5

# Preview what would be done without making changes
python batch_git_commit.py --dry-run

# Flag files larger than 50MB for Git LFS consideration
python batch_git_commit.py --max-file-size 50
```

### Command Line Options

- `--batch-size FLOAT`: Batch size in GB (default: 1.0)
- `--max-file-size INT`: Max file size in MB before flagging for LFS (default: 100)
- `--dry-run`: Show what would be done without making changes
- `--verbose`: Enable verbose logging

### Example Workflows

#### Initial Repository Setup
```bash
# First run - initialize and commit everything
python batch_git_commit.py --dry-run  # Preview first
python batch_git_commit.py            # Actually commit
```

#### Large Project with LFS
```bash
# For projects with many large assets
python batch_git_commit.py --max-file-size 50 --batch-size 0.5
# Review the large files report
# Set up Git LFS for recommended file types
git lfs install
git lfs track "*.fbx"
git lfs track "*.psd"
# etc.
```

## What the Script Does

1. **Repository Initialization**
   - Checks if Git repository exists
   - Initializes Git repo if needed
   - Sets up comprehensive .gitignore for game development

2. **File Discovery**
   - Scans for untracked files (respects .gitignore)
   - Calculates file sizes
   - Identifies files already tracked by Git

3. **Batch Creation**
   - Groups files into batches based on size limit
   - Uses intelligent packing algorithm
   - Handles oversized files in separate batches

4. **Commit Process**
   - Adds files to Git staging area
   - Creates descriptive commit messages
   - Handles errors gracefully
   - Continues with remaining batches if one fails

5. **Reporting**
   - Provides detailed progress feedback
   - Generates summary statistics
   - Reports large files that may need Git LFS

## Game Development .gitignore Patterns

The script automatically adds these patterns to .gitignore:

### Build Outputs
- `*.obj`, `*.o`, `*.a`, `*.lib`, `*.dll`, `*.exe`, `*.pdb`
- `bin/`, `obj/`, `build/`, `Build/`, `Binaries/`, `Intermediate/`

### IDE Files
- `.vs/`, `.vscode/`, `*.vcxproj.user`, `*.vcxproj.filters`

### Game Engine Specific
- **Unreal Engine**: `*.uasset`, `*.umap`
- **Unity**: `Library/`, `Temp/`, `Logs/`

### Large Asset Files (Consider for LFS)
- **3D Models**: `*.fbx`, `*.max`, `*.mb`, `*.ma`
- **Textures**: `*.psd`, `*.tga`, `*.exr`, `*.hdr`
- **Audio**: `*.wav`, `*.mp3`, `*.ogg`, `*.wem`
- **Video**: `*.mp4`, `*.avi`, `*.mov`

## Git LFS Integration

When large files are detected, the script provides recommendations:

```bash
# Example output for large files
git lfs install
git lfs track '*.fbx'
git lfs track '*.psd'
git lfs track '*.wav'
```

## Error Handling

The script handles common issues:

- **Permission Errors**: Logs and continues with other files
- **Large Files**: Places in separate batches or recommends LFS
- **Git Errors**: Continues with remaining batches
- **Missing Files**: Skips files that disappear during processing

## Logging

The script creates detailed logs:

- **Console Output**: Progress and summary information
- **Log File**: `batch_git_commit.log` with detailed information
- **Batch Reports**: Size and file count for each batch
- **Large File Reports**: Recommendations for Git LFS setup

## Troubleshooting

### Common Issues

1. **"Git command failed"**
   - Ensure Git is installed and in PATH
   - Check repository permissions

2. **"No untracked files found"**
   - Files may already be tracked
   - Check .gitignore patterns
   - Use `--verbose` for more details

3. **Large memory usage**
   - Reduce `--batch-size`
   - Consider Git LFS for large assets

4. **Slow performance**
   - Large repositories take time to scan
   - Consider excluding large directories in .gitignore

### Performance Tips

- Use `--dry-run` first to estimate time
- Exclude unnecessary large directories in .gitignore
- Consider Git LFS for assets > 100MB
- Run during off-peak hours for large repositories

## Integration with Perforce

This script is designed to work alongside Perforce:

- Respects existing .p4ignore patterns
- Doesn't interfere with Perforce workspace
- Adds Git as a local version control layer
- Maintains separate ignore files for each system

## Safety Features

- **Dry Run Mode**: Preview all changes before execution
- **Incremental Processing**: Only processes untracked files
- **Error Recovery**: Continues processing after individual failures
- **Detailed Logging**: Full audit trail of all operations
- **Backup Friendly**: Doesn't modify existing tracked files
