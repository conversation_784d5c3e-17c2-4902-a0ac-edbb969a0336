<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-90"/>
			<AngleLocal type="number" value="-90"/>
			<Lenght type="number" value="0.033445600420237"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Hair_01_L"/>
			<Pos type="vector" x="0.32647496461868" y="0.16678339242935" z="0"/>
			<PosEnd type="vector" x="0.32647496461868" y="0.2002289891243" z="0"/>
			<PosLocal type="vector" x="0.32647496461868" y="0.16678339242935" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="D2518D2516MyApp2505"/>
				<Element index="2" type="string" value="D2518D2516MyApp2506"/>
				<Element index="3" type="string" value="D2518D2516MyApp2507"/>
				<Element index="4" type="string" value="D2518D2516MyApp2508"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A38"/>
			<UID type="string" value="D2518D2516MyApp2504"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A38.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Fx_Flash_03"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2518D2516MyApp2506"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2518D2516MyApp2504"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.09983341395855" y="0.99500417709351" z="0"/>
					<Pos type="vector" x="-1.9677904844284" y="0.050831824541092" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99500417709351" y="-0.09983341395855" z="0"/>
			<PosUV type="vector" x="0.37730678915977" y="0.10096946358681" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A38"/>
			<UID type="string" value="D2518D2516MyApp2505"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2518D2516MyApp2505"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2518D2516MyApp2504"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.09983341395855" y="-0.99500417709351" z="0"/>
					<Pos type="vector" x="-1.6363116502762" y="-0.057910948991776" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.99500417709351" y="0.09983341395855" z="0"/>
			<PosUV type="vector" x="0.26856401562691" y="0.11205597221851" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A38"/>
			<UID type="string" value="D2518D2516MyApp2506"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2518D2516MyApp2508"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2518D2516MyApp2504"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="1.5889843702316" y="0.060557126998901" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="1" y="0" z="0"/>
			<PosUV type="vector" x="0.38703209161758" y="0.21992792189121" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A38"/>
			<UID type="string" value="D2518D2516MyApp2507"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2518D2516MyApp2507"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2518D2516MyApp2504"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.09983341395855" y="-0.99500417709351" z="0"/>
					<Pos type="vector" x="1.7447097301483" y="-0.044344693422318" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.99500417709351" y="0.09983341395855" z="0"/>
			<PosUV type="vector" x="0.28213027119637" y="0.22513625025749" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A38"/>
			<UID type="string" value="D2518D2516MyApp2508"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A38"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="294.5" y="0" z="0"/>
	<zoomSize type="vector" x="1121" y="1121" z="1"/>
</root>
