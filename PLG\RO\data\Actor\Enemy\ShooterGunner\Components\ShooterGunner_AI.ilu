includeReference("Actor/Includes/helpers.ilu")
includeReference("Actor/Includes/gameplay_types.ilu")

_ForceFixAngle = getValue( _ForceFixAngle, 0 )
_ForceFixAngleVal = getValue( _ForceFixAngleVal, 0.0 )
_ForceFixAngleRight = getValue( _ForceFixAngleRight, 0 )

PhantomComponent_Template =
{NAME = "PhantomComponent_Template",
PhantomComponent_Template =
{
    collisionGroup = CollisionGroup.Character,
    -- drawDebug = 1,
    shape = 
        {NAME = "PhysShapePolygon",
        PhysShapePolygon =
        {
            Points =
            {
                { VAL = vector2dNew(-1.0,-1.0) },
                { VAL = vector2dNew(-1.0,1.0) },
                { VAL = vector2dNew(1.0,1.0) },
                { VAL = vector2dNew(1.0,-1.0) },
            },
        }},
}}
appendTable(params.Actor_Template.COMPONENTS,{PhantomComponent_Template})
PhantomComponent_Template = {}

component =
{NAME="Ray_ShooterTurretAIComponent_Template",
Ray_ShooterTurretAIComponent_Template =
{
    findEnemyRadius = 15,

    idleBehavior =
        {NAME="AISimplePlayAnimBehavior_Template",
        AISimplePlayAnimBehavior_Template =
        {
            playAnim = 
            {
                NAME="AIPlayAnimAction_Template",
                AIPlayAnimAction_Template=
                {
                    action = "IDLE",
                }
            },
        }},
    
    attackBehavior =
        {NAME="Ray_AIShooterAttackBehavior_Template",
        Ray_AIShooterAttackBehavior_Template =
        {
            -- DEBUG_disableAttack = 1,
            
            minAngle = 315,
            maxAngle = 97,
            rotateUsingAnimation = 1,
			keepDirForBurst = 1,
			forceAngle = _ForceFixAngle,
			forcedAngle = _ForceFixAngleVal,
			lookRight = _ForceFixAngleRight, 
            
                idle = {NAME="AIIdleAction_Template",AIIdleAction_Template =
                {
                    action = "IDLE", 
                    minTime = 1.0,
                    maxTime = 2.0,
                }},
                hit = {NAME="Ray_AIShooterLaunchBulletAction_Template",Ray_AIShooterLaunchBulletAction_Template =
                {
                    action = "SHOOT",
                    marker = "MRK_Perform_Hit",
                    bulletExitBone = "perform_hit",
                    minBurstCount = 3,
                    maxBurstCount = 3,
                    bullet = "Actor/Enemy/Shooter/ShooterEnemyBullet.act",
                }},
        }},
    
    receiveHitBehavior =
        {NAME="Ray_AIReceiveHitBehavior_Template",
        Ray_AIReceiveHitBehavior_Template =
        {
            receiveHits =
            {
                -- all types, all levels
                {ReceiveHitData =
                {
                    action = 
                        {NAME="Ray_AIShooterReceiveHitAction_Template",
                        Ray_AIShooterReceiveHitAction_Template =
                        {
                            action = "RECEIVEHIT",
                            minStunTime = 0.3,
                            maxStunTime = 0.7,
                        }},
                }},
            },
        }},
    
    deathBehavior =
        {NAME="Ray_AIDeathBehavior_Template",
        Ray_AIDeathBehavior_Template =
        {
            pauseActorWhenDone = 0,
            resetActorWhenDone = 0,
            
            actions =
            {
                {NAME="AIPlayAnimAction_Template",
                AIPlayAnimAction_Template =
                {
                    action = "DEATH", 
                }},
                {NAME="AIFadeAction_Template",
                AIFadeAction_Template =
                {
                    visible = 1,
                    fadeDuration = 0,
                }},
            },
        }},
    
    faction = Faction.Enemy,
    damageLevels =
    {
        { VAL = 25 },
        { VAL = 50 },
        { VAL = 75 },
		{ VAL = 100 },
    },
}}

appendTable(params.Actor_Template.COMPONENTS,{component})
component = {}
