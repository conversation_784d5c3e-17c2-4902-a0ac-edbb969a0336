<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="4.4955735774375"/>
			<AngleLocal type="number" value="4.4955735774375"/>
			<Lenght type="number" value="0.13227537274361"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_01_L"/>
			<Pos type="vector" x="0.59497666358948" y="0.14653600752354" z="0"/>
			<PosEnd type="vector" x="0.72684508562088" y="0.13616798818111" z="0"/>
			<PosLocal type="vector" x="0.59497666358948" y="0.14653600752354" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="D776D736D647MyApp578"/>
				<Element index="2" type="string" value="D776D736D647MyApp579"/>
				<Element index="3" type="string" value="D776D736D647MyApp580"/>
				<Element index="4" type="string" value="D776D736D647MyApp581"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A14"/>
			<UID type="string" value="D776D736D647MyApp576"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="2">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-8.6866913764787"/>
			<AngleLocal type="number" value="-13.182264953916"/>
			<Lenght type="number" value="0.072600126266479"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_02_L"/>
			<ParentCut type="boolean" value="false"/>
			<ParentUID type="string" value="D776D736D647MyApp576"/>
			<Pos type="vector" x="0.72691136598587" y="0.13622875511646" z="0"/>
			<PosEnd type="vector" x="0.79867869615555" y="0.1471936404705" z="0"/>
			<PosLocal type="vector" x="6.1318278312683e-005" y="6.5774736867752e-005" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="D776D736D647MyApp606"/>
				<Element index="2" type="string" value="D776D736D647MyApp607"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A14"/>
			<UID type="string" value="D776D736D647MyApp602"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A14.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Cloak03_L"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D776D736D647MyApp579"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D776D736D647MyApp576"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-0.070308037102222" y="0.0084694335237145" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.078382082283497" y="-0.99692344665527" z="0"/>
			<PosUV type="vector" x="0.58504140377045" y="0.13882158696651" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A14"/>
			<UID type="string" value="D776D736D647MyApp578"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D776D736D647MyApp578"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D776D736D647MyApp576"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="-0.1141075566411" y="-0.09601191431284" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.078382082283497" y="0.99692344665527" z="0"/>
			<PosUV type="vector" x="0.58745509386063" y="0.24343559145927" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A14"/>
			<UID type="string" value="D776D736D647MyApp579"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D776D736D647MyApp581"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D776D736D647MyApp576"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.0052631115540862" y="0.99998617172241" z="0"/>
					<Pos type="vector" x="1.0292924642563" y="0.010201320983469" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.073134079575539" y="-0.99732220172882" z="0"/>
			<PosUV type="vector" x="0.72990822792053" y="0.12569434940815" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A14"/>
			<UID type="string" value="D776D736D647MyApp580"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D776D736D647MyApp580"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D776D736D647MyApp576"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="0.52455246448517" y="-0.097136810421944" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.078382082283497" y="0.99692344665527" z="0"/>
			<PosUV type="vector" x="0.67176234722137" y="0.23793539404869" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A14"/>
			<UID type="string" value="D776D736D647MyApp581"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="5">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D776D736D647MyApp607"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D776D736D647MyApp602"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.46550980210304" y="0.88504284620285" z="0"/>
					<Pos type="vector" x="1.0694173574448" y="0.0063840998336673" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.32650080323219" y="-0.94519692659378" z="0"/>
			<PosUV type="vector" x="0.8046247959137" y="0.14164392650127" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A14"/>
			<UID type="string" value="D776D736D647MyApp606"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="6">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D776D736D647MyApp606"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D776D736D647MyApp602"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.77006715536118" y="-0.63796299695969" z="0"/>
					<Pos type="vector" x="0.91352218389511" y="-0.037541545927525" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.66488128900528" y="0.74694901704788" z="0"/>
			<PosUV type="vector" x="0.78680247068405" y="0.1833563297987" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A14"/>
			<UID type="string" value="D776D736D647MyApp607"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A14"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="-2849.37109375" y="-326.87908935547" z="0"/>
	<zoomSize type="vector" x="5394.626953125" y="5394.6259765625" z="4.9042057991028"/>
</root>
