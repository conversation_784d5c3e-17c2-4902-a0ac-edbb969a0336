<?xml version="1.0" ?>
<root>
	<AnimsList>
		<Element index="1" type="string" value="Actor/Enemy/SpikyFruit/animation/LongStem.anm"/>
		<Element index="2" type="string" value="Actor/Enemy/SpikyFruit/animation/ShortStem.anm"/>
	</AnimsList>
	<PatchBankList>
		<fruit type="string" value="Actor/Enemy/SpikyFruit/animation/fruit.tga"/>
	</PatchBankList>
	<Scale type="number" value="1"/>
	<SceneVersion type="number" value="17"/>
	<Squeleton type="string" value="Actor/Enemy/SpikyFruit/animation/spikyfruit_Template1.skl"/>
	<UseDataFolder type="boolean" value="true"/>
	<UseRelative type="boolean" value="true"/>
</root>
