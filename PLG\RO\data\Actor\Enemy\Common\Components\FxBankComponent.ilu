component = 
{
    NAME="FxBankComponent_Template",
    FxBankComponent_Template=
    {
        matTableList =
        {
            {
                MatTable=
                {
                    name="Slide",
                    matFxList = 
                    {
                        {
                            MatFx=
                            {
                                material="__default__",
                                fx="test2"
                            }
                        },
                    },
                },
            },
        },
        Fx = 
        { 
            {
                FxDescriptor_Template=
                {
                    name = "test5",
                    texture	= "FX/Common/prtcl_test_1.tga",
                    gen=
                    {
                        ITF_ParticleGenerator_Template =
                        {
                            useAnim = 1,
                            animstart = 3,
                            animend =  3,
                            AnimUVfreq = 0,
                            params =
                            {
                                ParticleGeneratorParameters =
                                {
                                    maxParticles         = 20,
                                    emitParticlesCount   = -1,  
                                    velNorm              = 1,
                                    grav                 = vectorNew(0.0,0.0,0.0),
                                    acc                  = vectorNew(0.0,0.0,0.0), 
                                    velocityVar          = 0.3,
                                    friction             = 0.99,
                                    freq                 = 0.1,
                                    emitInterval         = 0.075,
                                    initAngle            = 0.0,
                                    angleDelta           = 360,
                                    angularSpeed         = 1000,
                                    angularSpeedDelta    = 100,
                                    timeTarget           = 0.0,
                                    startTime            = 1.0,
                                    stopTime             = 1.0,
                                    genBox               =
                                    {
                                        AABB =
                                        {
                                            MIN = vector2dNew(-0.0,-0.0),
                                            MAX = vector2dNew(0.5,0.5),
                                        }
                                    },
                                    boundingBox          =
                                    {
                                        AABB =
                                        {
                                            MIN = vector2dNew(0.0,0.0),
                                            MAX = vector2dNew(0.5,0.5),
                                        }
                                    },
                                    nbPhase              = 3,
                                    phases =
                                    {
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.5,
                                                colorMin    = "0xffffffff",
                                                colorMax    = "0xffffff00",
                                                sizeMin     = vector2dNew(0.0,0.0),
                                                sizeMax     = vector2dNew(0.0,0.0),
                                            }
                                        },
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.1,
                                                colorMin    = "0xffffffff",
                                                colorMax    = "0xffffff88",
                                                sizeMin     = vector2dNew(0.75,0.75),
                                                sizeMax     = vector2dNew(0.75,0.75),
                                                velocityVar  = 0.5,
                                            }
                                        },
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 1.0,
                                                colorMin    = "0xffffffff",
                                                colorMax    = "0xffffffff",
                                                sizeMin     = vector2dNew(0.0,0.0),
                                                sizeMax     = vector2dNew(0.0,0.0),
                                            }
                                        },
                                    },
                                    renderPrio           = 0,
                                    --GFX_BLEND_UNKNOWN = 0,,
                                    --GFX_BLEND_COPY = 1,
                                    --GFX_BLEND_ALPHA = 2,
                                    --GFX_BLEND_ALPHAPREMULT = 3,
                                    --GFX_BLEND_ALPHADEST = 4,
                                    --GFX_BLEND_ALPHADESTPREMULT = 5,
                                    --GFX_BLEND_ADD = 6,
                                    --GFX_BLEND_ADDALPHA = 7,
                                    --GFX_BLEND_SUBALPHA = 8,
                                    --GFX_BLEND_SUB = 9,
                                    --GFX_BLEND_MUL = 10 ,
                                    --GFX_BLEND_ALPHAMUL = 11,
                                    --GFX_BLEND_IALPHAMUL = 12 ,
                                    --GFX_BLEND_IALPHA = 13,
                                    --GFX_BLEND_IALPHAPREMULT = 14,
                                    --GFX_BLEND_IALPHADEST = 15,
                                    --GFX_BLEND_IALPHADESTPREMULT = 16,
                                    --GFX_BLEND_MUL2X = 17,
                                    blendMode          = 2,
                                    --PARGEN_GEN_POINTS = 0
                                    --PARGEN_GEN_RECTANGLE = 1
                                    --PARGEN_GEN_CIRCLE = 2
                                    genGenType         = 1,
                                    --PARGEN_MODE_FOLLOW  = 0
                                    --PARGEN_MODE_COMPLEX = 1
                                    genMode            = 0,
                                }
                            },
                        }
                    },
                }
            },
            {
                FxDescriptor_Template=
                {
                    name = "test4",
                    texture	= "FX/Common/prtcl_test_1.tga",
                    gen=
                    {
                        ITF_ParticleGenerator_Template =
                        {
                            useAnim = 1,
                            animstart = 2,
                            animend = 2,
                            AnimUVfreq = 0,
                            params =
                            {
                                ParticleGeneratorParameters =
                                {
                                    maxParticles         = 200,
                                    emitParticlesCount   = -1,
                                    velNorm              = 0.5,
                                    grav                 = vectorNew(0.0,0.2,0.0),
                                    acc                  = vectorNew(0.0,0.0,0.0),
                                    velocityVar          = 0.2,
                                    friction             = 0.95,
                                    freq                 = 0.1,
                                    emitInterval         = 0.075,
                                    initAngle            = 0.0,
                                    angleDelta           = 20,
                                    angularSpeed         = 0,
                                    angularSpeedDelta    = 20,
                                    timeTarget           = 0.0,
                                    startTime            = 1.0,
                                    stopTime             = 1.0,
                                    genBox               =
                                    {
                                        AABB =
                                        {
                                            MIN = vector2dNew(-0.0,-0.0),
                                            MAX = vector2dNew(0.1,0.1),
                                        }
                                    },
                                    boundingBox          =
                                    {
                                        AABB =
                                        {
                                            MIN = vector2dNew(0.0,0.0),
                                            MAX = vector2dNew(0.1,0.1),
                                        }
                                    },
                                    nbPhase              = 3,
                                    phases =
                                    {
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.5,
                                                colorMin    = "0xffffffff",
                                                colorMax    = "0xffffffff",
                                                sizeMin     = vector2dNew(0.0,0.0),
                                                sizeMax     = vector2dNew(0.0,0.0),
                                            }
                                        },
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.1,
                                                colorMin    = "0xffffffff",
                                                colorMax    = "0xffffffff",
                                                sizeMin     = vector2dNew(0.75,0.75),
                                                sizeMax     = vector2dNew(1.0,1.0),
                                            }
                                        },
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 1.0,
                                                colorMin    = "0xffffffff",
                                                colorMax    = "0xffffffff",
                                                sizeMin     = vector2dNew(0.0,0.0),
                                                sizeMax     = vector2dNew(0.0,0.0),
                                            }
                                        },
                                    },
                                    renderPrio           = 0,
                                    --GFX_BLEND_UNKNOWN = 0,
                                    --GFX_BLEND_COPY = 1
                                    --GFX_BLEND_ALPHA = 2
                                    --GFX_BLEND_ALPHAPREMULT = 3
                                    --GFX_BLEND_ALPHADEST = 4
                                    --GFX_BLEND_ALPHADESTPREMULT = 5
                                    --GFX_BLEND_ADD = 6
                                    --GFX_BLEND_ADDALPHA = 7
                                    --GFX_BLEND_SUBALPHA = 8
                                    --GFX_BLEND_SUB = 9
                                    --GFX_BLEND_MUL = 10 
                                    --GFX_BLEND_ALPHAMUL = 11
                                    --GFX_BLEND_IALPHAMUL = 12 
                                    --GFX_BLEND_IALPHA = 13
                                    --GFX_BLEND_IALPHAPREMULT = 14
                                    --GFX_BLEND_IALPHADEST = 15
                                    --GFX_BLEND_IALPHADESTPREMULT = 16
                                    --GFX_BLEND_MUL2X = 17
                                    blendMode          = 2,
                                    --PARGEN_GEN_POINTS = 0
                                    --PARGEN_GEN_RECTANGLE = 1
                                    --PARGEN_GEN_CIRCLE = 2
                                    genGenType         = 0,
                                    --PARGEN_MODE_FOLLOW  = 0
                                    --PARGEN_MODE_COMPLEX = 1
                                    genMode            = 1,
                                }
                            },
                        }
                    },
                }
            },
            {
                FxDescriptor_Template=
                {
                    name = "test3",
                    texture	= "FX/Common/prtcl_test_1.tga",
                    gen=
                    {
                        ITF_ParticleGenerator_Template =
                        {
                            useAnim = 1,
                            animstart = 1,
                            animend = 1,
	                        AnimUVfreq = 0,
                            params =
                            {
                                ParticleGeneratorParameters =
                                {
                                    maxParticles         = 50,
                                    emitParticlesCount   = -1,
                                    velNorm              = 10 ,
                                    grav                 = vectorNew(0.0,0.2,0.0),
                                    acc                  = vectorNew(0.0,0.0,0.0),
                                    velocityVar          = 0.2,
                                    friction             = 0.98,
                                    freq                 = 0.05,
                                    emitInterval         = 0.002,
                                    initAngle            = 0.0,
                                    angleDelta           = 360,
                                    angularSpeed         = 00,
                                    angularSpeedDelta    = 100,
                                    timeTarget           = 0.0,
                                    startTime             = 1.0,
                                    stopTime              = 1.0,
                                    genBox               =
                                    {
                                        AABB =
                                        {
                                            MIN = vector2dNew(-0.0,-0.0),
                                            MAX = vector2dNew(0.1,0.1),
                                        }
                                    },
                                    boundingBox          =
                                    {
                                        AABB =
                                        {
                                            MIN = vector2dNew(0.0,0.0),
                                            MAX = vector2dNew(0.1,0.1),
                                        }
                                    },
                                    nbPhase              = 3,
                                    phases =
                                    {
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.5,
                                                colorMin    = "0xffffffff",
                                                colorMax    = "0xffffffff",
                                                sizeMin     = vector2dNew(0.0,0.0),
                                                sizeMax     = vector2dNew(0.0,0.0),
                                            }
                                        },
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.5,
                                                colorMin    = "0xffffffff",
                                                colorMax    = "0xffffffff",
                                                sizeMin     = vector2dNew(0.5,0.5),
                                                sizeMax     = vector2dNew(0.75,0.75),
                                            }
                                        },
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 1,
                                                colorMin    = "0xffffffff",
                                                colorMax    = "0xffffffff",
                                                sizeMin     = vector2dNew(0.0,0.0),
                                                sizeMax     = vector2dNew(0.0,0.0),
                                            }
                                        },
                                    },
                                    renderPrio           = 0,
                                    --GFX_BLEND_UNKNOWN = 0,
                                    --GFX_BLEND_COPY = 1
                                    --GFX_BLEND_ALPHA = 2
                                    --GFX_BLEND_ALPHAPREMULT = 3
                                    --GFX_BLEND_ALPHADEST = 4
                                    --GFX_BLEND_ALPHADESTPREMULT = 5
                                    --GFX_BLEND_ADD = 6
                                    --GFX_BLEND_ADDALPHA = 7
                                    --GFX_BLEND_SUBALPHA = 8
                                    --GFX_BLEND_SUB = 9
                                    --GFX_BLEND_MUL = 10 
                                    --GFX_BLEND_ALPHAMUL = 11
                                    --GFX_BLEND_IALPHAMUL = 12 
                                    --GFX_BLEND_IALPHA = 13
                                    --GFX_BLEND_IALPHAPREMULT = 14
                                    --GFX_BLEND_IALPHADEST = 15
                                    --GFX_BLEND_IALPHADESTPREMULT = 16
                                    --GFX_BLEND_MUL2X = 17
                                    blendMode          = 2,
                                    --PARGEN_GEN_POINTS = 0
                                    --PARGEN_GEN_RECTANGLE = 1
                                    --PARGEN_GEN_CIRCLE = 2
                                    genGenType         = 0,
                                    --PARGEN_MODE_FOLLOW  = 0
                                    --PARGEN_MODE_COMPLEX = 1
                                    genMode            = 1,
                                }
                            },
                        }
                    },
                }
            },
			{
				FxDescriptor_Template =
				{
					name = "FX_LumPicked_Broken",
					texture = "FX/Common/RayPunch_Particles.tga",
					gen = 
					{
						ITF_ParticleGenerator_Template =
						{	
							useanim = 1,
                            animstart = 6,
                            animend =  6,
                            animuvfreq = 0,
							params = 
							{
								ParticleGeneratorParameters =
								{
									maxParticles         = 100,
                                    emitParticlesCount   = -1,
									velNorm             = 0.2,
									grav                = vectorNew(0.0,0.0,0.0),
									acc                 = vectorNew(0.0,0.0,0.5),
									velocityVar         = 1,
									friction            = 0.97,
									freq                = 1 / 80,
									emitInterval        = 1 / 120,
									initAngle           = 20,
									angleDelta          = 360,
									angularSpeed        = 100,
									angularSpeedDelta   = 500,
									timeTarget          = 0.0,
									startTime           = 1.0,
									stopTime            = 2.0,
									randomizeDirection  = 0,
									computeAABB		    = 1,
									genBox              = {AABB =
									{
										MIN = vector2dNew(-0.1,-0.1),
										MAX = vector2dNew(0.1,0.1),
									}},

									phases = 
									{
										{ParPhase = 
										{
											phaseTime   = 1.0,
											colorMin    = "0x00ffffff",
											colorMax    = "0x0000ccff",
											sizeMin     = vector2dNew(10,10),
											sizeMax     = vector2dNew(10,10),
										}},
										{ParPhase = 
										{
											phaseTime   = 0.01,
											colorMin    = "0xffffffff",
											colorMax    = "0xffbbeeff",
											sizeMin     = vector2dNew(1,1),
											sizeMax     = vector2dNew(1,1),
										}},
										{ParPhase = 
										{
											phaseTime   = 0.1,
											colorMin    = "0x5500aaff",
											colorMax    = "0x5500aaff",
											sizeMin     = vector2dNew(0.6,0.6),
											sizeMax     = vector2dNew(0.6,0.6),
										}},
									},

									renderPrio = 0,
									blendMode = 2,
									genGenType = 0,
									genMode = 1,
								}
							},
						}
					},
				}
			},
            {
                FxDescriptor_Template=
                {
                    name = "test2",
                    texture	= "FX/Common/prtcl_test_1.tga",
                    angleOffset = 90,
                    gen=
                    {
                        ITF_ParticleGenerator_Template =
                        {
                            useAnim = 1,
                            animstart = 0,
                            animend = 0,
                            AnimUVfreq = 0,
                            params =
                            {
                                ParticleGeneratorParameters =
                                {
                                    maxParticles         = 50,
                                    emitParticlesCount   = -1,
                                    velNorm              = 0.5 ,
                                    grav                 = vectorNew(0.0,-5.0,0.0),
                                    acc                  = vectorNew(0.0,0.0,0.0),
                                    velocityVar          = 1.0,
                                    friction             = 0.96,
                                    freq                 = 0.03,
                                    emitInterval         = 0.1,
                                    initAngle            = 0.0,
                                    angleDelta           = 360,
                                    angularSpeed         = 10,
                                    angularSpeedDelta    = 200,
                                    timeTarget           = 0.0,
                                    startTime             = 1.0,
                                    stopTime              = 1.0,
                                    genBox               =
                                    {
                                        AABB =
                                        {
                                            MIN = vector2dNew(-0.0,-0.0),
                                            MAX = vector2dNew(0.1,0.1),
                                        }
                                    },
                                    boundingBox          =
                                    {
                                        AABB =
                                        {
                                            MIN = vector2dNew(0.0,0.0),
                                            MAX = vector2dNew(0.1,0.1),
                                        }
                                    },
                                    nbPhase              = 3,
                                    phases =
                                    {
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.1,
                                                colorMin    = "0x00ffffff",
                                                colorMax    = "0x00ffffff",
                                                sizeMin     = vector2dNew(0.1,0.1),
                                                sizeMax     = vector2dNew(0.5,0.5),
                                            }
                                        },
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.3,
                                                colorMin    = "0xffffffff",
                                                colorMax    = "0xffffffff",
                                                sizeMin     = vector2dNew(0.2,0.2),
                                                sizeMax     = vector2dNew(1.0,1.0),
                                            }
                                        },
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.3,
                                                colorMin    = "0x00ffffff",
                                                colorMax    = "0x00ffffff",
                                                sizeMin     = vector2dNew(0.2,0.2),
                                                sizeMax     = vector2dNew(1.0,1.0),
                                            }
                                        },
                                    },
                                    renderPrio           = 0,
                                    --GFX_BLEND_UNKNOWN = 0,
                                    --GFX_BLEND_COPY = 1
                                    --GFX_BLEND_ALPHA = 2
                                    --GFX_BLEND_ALPHAPREMULT = 3
                                    --GFX_BLEND_ALPHADEST = 4
                                    --GFX_BLEND_ALPHADESTPREMULT = 5
                                    --GFX_BLEND_ADD = 6
                                    --GFX_BLEND_ADDALPHA = 7
                                    --GFX_BLEND_SUBALPHA = 8
                                    --GFX_BLEND_SUB = 9
                                    --GFX_BLEND_MUL = 10 
                                    --GFX_BLEND_ALPHAMUL = 11
                                    --GFX_BLEND_IALPHAMUL = 12 
                                    --GFX_BLEND_IALPHA = 13
                                    --GFX_BLEND_IALPHAPREMULT = 14
                                    --GFX_BLEND_IALPHADEST = 15
                                    --GFX_BLEND_IALPHADESTPREMULT = 16
                                    --GFX_BLEND_MUL2X = 17
                                    blendMode          = 2,
                                    --PARGEN_GEN_POINTS = 0
                                    --PARGEN_GEN_RECTANGLE = 1
                                    --PARGEN_GEN_CIRCLE = 2
                                    genGenType         = 0,
                                    --PARGEN_MODE_FOLLOW  = 0
                                    --PARGEN_MODE_COMPLEX = 1
                                    genMode            = 1,
                                }
                            },
                        }
                    },
                }
            },
			{
                FxDescriptor_Template=
                {
                    name = "FX_LumPicked",
                    texture	= "FX/Common/RayPunch_Particles.tga",
                    angleOffset = 90,
                    gen=
                    {
                        ITF_ParticleGenerator_Template =
                        {
                            useAnim = 1,
                            animstart = 6,
                            animend = 6,
                            AnimUVfreq = 0,
                            params =
                            {
                                ParticleGeneratorParameters =
                                {
                                    maxParticles         = 50,
                                    emitParticlesCount   = -1,
                                    velNorm              = 0.5 ,
                                    grav                 = vectorNew(0.0,-5.0,-0.1),
                                    acc                  = vectorNew(0.0,0.0,0.0),
                                    velocityVar          = 1.0,
                                    friction             = 0.96,
                                    freq                 = 0.03,
                                    emitInterval         = 0.1,
                                    initAngle            = 0.0,
                                    angleDelta           = 360,
                                    angularSpeed         = 10,
                                    angularSpeedDelta    = 200,
                                    timeTarget           = 0.0,
                                    startTime             = 1.0,
                                    stopTime              = 1.0,
                                    genBox               =
                                    {
                                        AABB =
                                        {
                                            MIN = vector2dNew(-0.0,-0.0),
                                            MAX = vector2dNew(0.1,0.1),
                                        }
                                    },
                                    boundingBox          =
                                    {
                                        AABB =
                                        {
                                            MIN = vector2dNew(0.0,0.0),
                                            MAX = vector2dNew(0.1,0.1),
                                        }
                                    },
                                    nbPhase              = 3,
                                    phases =
                                    {
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.1,
                                                colorMin    = "0x00ffffff",
                                                colorMax    = "0x00ffffff",
                                                sizeMin     = vector2dNew(0.1,0.1),
                                                sizeMax     = vector2dNew(0.5,0.5),
                                            }
                                        },
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.01,
                                                colorMin    = "0xffffffff",
                                                colorMax    = "0xffffffff",
                                                sizeMin     = vector2dNew(0.1,0.1),
                                                sizeMax     = vector2dNew(0.5,0.5),
                                            }
                                        },
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 1.0,
                                                colorMin    = "0x00ffffff",
                                                colorMax    = "0x00ffffff",
                                                sizeMin     = vector2dNew(0.05,0.05),
                                                sizeMax     = vector2dNew(0.2,0.2),
                                            }
                                        },
                                    },
                                    renderPrio           = 0,
                                    --GFX_BLEND_UNKNOWN = 0,
                                    --GFX_BLEND_COPY = 1
                                    --GFX_BLEND_ALPHA = 2
                                    --GFX_BLEND_ALPHAPREMULT = 3
                                    --GFX_BLEND_ALPHADEST = 4
                                    --GFX_BLEND_ALPHADESTPREMULT = 5
                                    --GFX_BLEND_ADD = 6
                                    --GFX_BLEND_ADDALPHA = 7
                                    --GFX_BLEND_SUBALPHA = 8
                                    --GFX_BLEND_SUB = 9
                                    --GFX_BLEND_MUL = 10 
                                    --GFX_BLEND_ALPHAMUL = 11
                                    --GFX_BLEND_IALPHAMUL = 12 
                                    --GFX_BLEND_IALPHA = 13
                                    --GFX_BLEND_IALPHAPREMULT = 14
                                    --GFX_BLEND_IALPHADEST = 15
                                    --GFX_BLEND_IALPHADESTPREMULT = 16
                                    --GFX_BLEND_MUL2X = 17
                                    blendMode          = 2,
                                    --PARGEN_GEN_POINTS = 0
                                    --PARGEN_GEN_RECTANGLE = 1
                                    --PARGEN_GEN_CIRCLE = 2
                                    genGenType         = 0,
                                    --PARGEN_MODE_FOLLOW  = 0
                                    --PARGEN_MODE_COMPLEX = 1
                                    genMode            = 1,
                                }
                            },
                        }
                    },
                }
            },
			{
                FxDescriptor_Template=
                {
                    name = "FX_LumPicked_debug",
                    texture	= "FX/Common/RayPunch_Particles.tga",
                    angleOffset = 90,
                    gen=
                    {
                        ITF_ParticleGenerator_Template =
                        {
                            useAnim = 1,
                            animstart = 6,
                            animend = 6,
                            AnimUVfreq = 0,
                            params =
                            {
                                ParticleGeneratorParameters =
                                {
                                    maxParticles         = 50,
                                    emitParticlesCount   = -1,
                                    velNorm              = 0.5 ,
                                    grav                 = vectorNew(0.0,-5.0,-0.1),
                                    acc                  = vectorNew(0.0,0.0,0.0),
                                    velocityVar          = 1.0,
                                    friction             = 0.96,
                                    freq                 = 0.1,
                                    emitInterval         = 0.1,
                                    initAngle            = 0.0,
                                    angleDelta           = 360,
                                    angularSpeed         = 10,
                                    angularSpeedDelta    = 200,
                                    timeTarget           = 0.0,
                                    startTime             = 1.0,
                                    stopTime              = 1.0,
                                    genBox               =
                                    {
                                        AABB =
                                        {
                                            MIN = vector2dNew(-0.0,-0.0),
                                            MAX = vector2dNew(0.1,0.1),
                                        }
                                    },
                                    boundingBox          =
                                    {
                                        AABB =
                                        {
                                            MIN = vector2dNew(0.0,0.0),
                                            MAX = vector2dNew(0.1,0.1),
                                        }
                                    },
                                    nbPhase              = 3,
                                    phases =
                                    {
                                        {
                                            ParPhase =
                                            {
                                                --phaseTime   = 0.1,
												phaseTime   = 0.4,
                                                colorMin    = "0x00ffffff",
                                                colorMax    = "0x00ffffff",
                                                sizeMin     = vector2dNew(0.1,0.1),
                                                sizeMax     = vector2dNew(0.5,0.5),
                                            }
                                        },
                                        {
                                            ParPhase =
                                            {
                                                --phaseTime   = 0.01,
												phaseTime   = 4,
                                                colorMin    = "0xffffffff",
                                                colorMax    = "0xffffffff",
                                                sizeMin     = vector2dNew(4,4),
                                                sizeMax     = vector2dNew(4,4),
                                            }
                                        },
                                        {
                                            ParPhase =
                                            {
                                                --phaseTime   = 1.0,
												phaseTime   = 0.5,
                                                colorMin    = "0x00ffffff",
                                                colorMax    = "0x00ffffff",
                                                sizeMin     = vector2dNew(0.05,0.05),
                                                sizeMax     = vector2dNew(0.2,0.2),
                                            }
                                        },
                                    },
                                    renderPrio           = 0,
                                    --GFX_BLEND_UNKNOWN = 0,
                                    --GFX_BLEND_COPY = 1
                                    --GFX_BLEND_ALPHA = 2
                                    --GFX_BLEND_ALPHAPREMULT = 3
                                    --GFX_BLEND_ALPHADEST = 4
                                    --GFX_BLEND_ALPHADESTPREMULT = 5
                                    --GFX_BLEND_ADD = 6
                                    --GFX_BLEND_ADDALPHA = 7
                                    --GFX_BLEND_SUBALPHA = 8
                                    --GFX_BLEND_SUB = 9
                                    --GFX_BLEND_MUL = 10 
                                    --GFX_BLEND_ALPHAMUL = 11
                                    --GFX_BLEND_IALPHAMUL = 12 
                                    --GFX_BLEND_IALPHA = 13
                                    --GFX_BLEND_IALPHAPREMULT = 14
                                    --GFX_BLEND_IALPHADEST = 15
                                    --GFX_BLEND_IALPHADESTPREMULT = 16
                                    --GFX_BLEND_MUL2X = 17
                                    blendMode          = 2,
                                    --PARGEN_GEN_POINTS = 0
                                    --PARGEN_GEN_RECTANGLE = 1
                                    --PARGEN_GEN_CIRCLE = 2
                                    genGenType         = 0,
                                    --PARGEN_MODE_FOLLOW  = 0
                                    --PARGEN_MODE_COMPLEX = 1
                                    genMode            = 1,
                                }
                            },
                        }
                    },
                }
            },
            {
                FxDescriptor_Template=
                {
                    name = "test1",
                    texture	= "FX/Common/prtcl_test_1.tga",
                    angleOffset = 140,
                    gen=
                    {
                        ITF_ParticleGenerator_Template =
                        {
                            useAnim = 1,
                            animstart = 0,
                            animend = 0,
                            AnimUVfreq = 0,
                            params =
                            {
                                ParticleGeneratorParameters =
                                {
                                    maxParticles         = 50,
                                    emitParticlesCount   = -1,
                                    velNorm              = 6 ,
                                    grav                 = vectorNew(0.0,-6.0,0.0),
                                    acc                  = vectorNew(0.0,0.0,0.0),
                                    velocityVar          = 1.0,
                                    friction             = 0.98,
                                    freq                 = 0.05,
                                    emitInterval         = 0.1,
                                    initAngle            = 0.0,
                                    angleDelta           = 360,
                                    angularSpeed         = 10,
                                    angularSpeedDelta    = 200,
                                    timeTarget           = 0.0,
                                    startTime             = 1.0,
                                    stopTime              = 1.0,
                                    genBox               =
                                    {
                                        AABB =
                                        {
                                            MIN = vector2dNew(-0.0,-0.0),
                                            MAX = vector2dNew(0.1,0.1),
                                        }
                                    },
                                    boundingBox          =
                                    {
                                        AABB =
                                        {
                                            MIN = vector2dNew(0.0,0.0),
                                            MAX = vector2dNew(0.1,0.1),
                                        }
                                    },
                                    nbPhase              = 3,
                                    phases =
                                    {
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.1,
                                                colorMin    = "0xffffffff",
                                                colorMax    = "0xffffffff",
                                                sizeMin     = vector2dNew(0.1,0.1),
                                                sizeMax     = vector2dNew(0.5,0.5),
                                            }
                                        },
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.5,
                                                colorMin    = "0xffffffff",
                                                colorMax    = "0xffffffff",
                                                sizeMin     = vector2dNew(0.2,0.2),
                                                sizeMax     = vector2dNew(1.3,1.3),
                                            }
                                        },
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.3,
                                                colorMin    = "0x00ffffff",
                                                colorMax    = "0x00ffffff",
                                                sizeMin     = vector2dNew(0.2,0.2),
                                                sizeMax     = vector2dNew(1.3,1.3),
                                            }
                                        },
                                    },
                                    renderPrio           = 0,
                                    --GFX_BLEND_UNKNOWN = 0,
                                    --GFX_BLEND_COPY = 1
                                    --GFX_BLEND_ALPHA = 2
                                    --GFX_BLEND_ALPHAPREMULT = 3
                                    --GFX_BLEND_ALPHADEST = 4
                                    --GFX_BLEND_ALPHADESTPREMULT = 5
                                    --GFX_BLEND_ADD = 6
                                    --GFX_BLEND_ADDALPHA = 7
                                    --GFX_BLEND_SUBALPHA = 8
                                    --GFX_BLEND_SUB = 9
                                    --GFX_BLEND_MUL = 10 
                                    --GFX_BLEND_ALPHAMUL = 11
                                    --GFX_BLEND_IALPHAMUL = 12 
                                    --GFX_BLEND_IALPHA = 13
                                    --GFX_BLEND_IALPHAPREMULT = 14
                                    --GFX_BLEND_IALPHADEST = 15
                                    --GFX_BLEND_IALPHADESTPREMULT = 16
                                    --GFX_BLEND_MUL2X = 17
                                    blendMode          = 2,
                                    --PARGEN_GEN_POINTS = 0
                                    --PARGEN_GEN_RECTANGLE = 1
                                    --PARGEN_GEN_CIRCLE = 2
                                    genGenType         = 0,
                                    --PARGEN_MODE_FOLLOW  = 0
                                    --PARGEN_MODE_COMPLEX = 1
                                    genMode            = 1,
                                }
                            },
                        }
                    },
                }
            },
            {
                FxDescriptor_Template=
                {
                    name = "test9",
                    texture	= "FX/Common/prtcl_test_1.tga",
                    gen=
                    {
                        ITF_ParticleGenerator_Template =
                        {
                            useAnim = 1,
                            animstart = 6,
                            animend = 6,
                            AnimUVfreq = 0,
                            params =
                            {
                                ParticleGeneratorParameters =
                                {
                                    maxParticles         = 10,
                                    emitParticlesCount   = -1,
                                    velNorm              = 20 ,
                                    grav                 = vectorNew(0.0,-50.0,0.0),
                                    acc                  = vectorNew(0.0,0.0,0.0),
                                    velocityVar          = 10,
                                    friction             = 0.7,
                                    freq                 = 0.0000001 ,
                                    emitInterval         = 6,
                                    initAngle            = 0.0,
                                    angleDelta           = 0,
                                    angularSpeed         = 000,
                                    angularSpeedDelta    = 00,
                                    timeTarget           = 6.0,
                                    startTime             = 0.0,
                                    stopTime              = 0.0,
                                    genBox               =
                                    {
                                        AABB =
                                        {
                                            MIN = vector2dNew(-0.0,-0.0),
                                            MAX = vector2dNew(0.1,0.1),
                                        }
                                    },
                                    boundingBox          =
                                    {
                                        AABB =
                                        {
                                            MIN = vector2dNew(0.0,0.0),
                                            MAX = vector2dNew(0.1,0.1),
                                        }
                                    },
                                    nbPhase              = 4,
                                    phases =
                                    {
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.01,
                                                colorMin    = "0xffffffff",
                                                colorMax    = "0xffffffff",
                                                sizeMin     = vector2dNew(0.1,0.1),
                                                sizeMax     = vector2dNew(0.1,0.1),
                                            }
                                        },
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.01,
                                                colorMin    = "0xffffffff",
                                                colorMax    = "0xffffffff",
                                                sizeMin     = vector2dNew(1.0,1.0),
                                                sizeMax     = vector2dNew(1.0,1.0),
                                            }
                                        },
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.2,
                                                colorMin    = "0xffffffff",
                                                colorMax    = "0xffffffff",
                                                sizeMin     = vector2dNew(0.0,0.0),
                                                sizeMax     = vector2dNew(0.0,0.0),
                                            }
                                        },
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 1.0,
                                                colorMin    = "0x00000000",
                                                colorMax    = "0x00000000",
                                                sizeMin     = vector2dNew(0.0,0.0),
                                                sizeMax     = vector2dNew(0.0,0.0),
                                            }
                                        },
                                    },
                                    renderPrio           = 0,
                                    --GFX_BLEND_UNKNOWN = 0,
                                    --GFX_BLEND_COPY = 1
                                    --GFX_BLEND_ALPHA = 2
                                    --GFX_BLEND_ALPHAPREMULT = 3
                                    --GFX_BLEND_ALPHADEST = 4
                                    --GFX_BLEND_ALPHADESTPREMULT = 5
                                    --GFX_BLEND_ADD = 6
                                    --GFX_BLEND_ADDALPHA = 7
                                    --GFX_BLEND_SUBALPHA = 8
                                    --GFX_BLEND_SUB = 9
                                    --GFX_BLEND_MUL = 10 
                                    --GFX_BLEND_ALPHAMUL = 11
                                    --GFX_BLEND_IALPHAMUL = 12 
                                    --GFX_BLEND_IALPHA = 13
                                    --GFX_BLEND_IALPHAPREMULT = 14
                                    --GFX_BLEND_IALPHADEST = 15
                                    --GFX_BLEND_IALPHADESTPREMULT = 16
                                    --GFX_BLEND_MUL2X = 17
                                    blendMode          = 2,
                                    --PARGEN_GEN_POINTS = 0
                                    --PARGEN_GEN_RECTANGLE = 1
                                    --PARGEN_GEN_CIRCLE = 2
                                    genGenType      	   	= 2,
                                    circleradius     	    	= 0.1,
                                    innercircleradius        = 0.0,
                                    --PARGEN_MODE_FOLLOW  = 0
                                    --PARGEN_MODE_COMPLEX = 1
                                    genMode            = 1,
                                }
                            },
                        }
                    },
                }
            },
            {
                FxDescriptor_Template=
                {
                    name = "test8",
                    texture	= "FX/Common/prtcl_test_1.tga",
                    gen=
                    {
                        ITF_ParticleGenerator_Template =
                        {
                            useAnim = 1,
                            animstart = 1,
                            animend = 1,
                            AnimUVfreq = 0,
                            params =
                            {
                                ParticleGeneratorParameters =
                                {
                                    maxParticles         = 10,
                                    emitParticlesCount   = -1,
                                    velNorm              = 20 ,
                                    grav                 = vectorNew(0.0,-50.0,0.0),
                                    acc                  = vectorNew(0.0,0.0,0.0),
                                    velocityVar          = 10,
                                    friction             = 0.7,
                                    freq                 = 0.0000001 ,
                                    emitInterval         = 6,
                                    initAngle            = 0.0,
                                    angleDelta           = 360,
                                    angularSpeed         = 000,
                                    angularSpeedDelta    = 100,
                                    timeTarget           = 6.0,
                                    startTime             = 0.0,
                                    stopTime              = 0.0,
                                    genBox               =
                                    {
                                        AABB =
                                        {
                                            MIN = vector2dNew(-0.0,-0.0),
                                            MAX = vector2dNew(0.1,0.1),
                                        }
                                    },
                                    boundingBox          =
                                    {
                                        AABB =
                                        {
                                            MIN = vector2dNew(0.0,0.0),
                                            MAX = vector2dNew(0.1,0.1),
                                        }
                                    },
                                    nbPhase              = 4,
                                    phases =
                                    {
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.01,
                                                colorMin    = "0xffffffff",
                                                colorMax    = "0xffffffff",
                                                sizeMin     = vector2dNew(0.1,0.1),
                                                sizeMax     = vector2dNew(0.1,0.1),
                                            }
                                        },
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.01,
                                                colorMin    = "0xffffffff",
                                                colorMax    = "0xffffffff",
                                                sizeMin     = vector2dNew(1.0,1.0),
                                                sizeMax     = vector2dNew(1.0,1.0),
                                            }
                                        },
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.2,
                                                colorMin    = "0xffffffff",
                                                colorMax    = "0xffffffff",
                                                sizeMin     = vector2dNew(0.0,0.0),
                                                sizeMax     = vector2dNew(0.0,0.0),
                                            }
                                        },
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 1.0,
                                                colorMin    = "0x00000000",
                                                colorMax    = "0x00000000",
                                                sizeMin     = vector2dNew(0.0,0.0),
                                                sizeMax     = vector2dNew(0.0,0.0),
                                            }
                                        },
                                    },
                                    renderPrio           = 0,
                                    --GFX_BLEND_UNKNOWN = 0,
                                    --GFX_BLEND_COPY = 1
                                    --GFX_BLEND_ALPHA = 2
                                    --GFX_BLEND_ALPHAPREMULT = 3
                                    --GFX_BLEND_ALPHADEST = 4
                                    --GFX_BLEND_ALPHADESTPREMULT = 5
                                    --GFX_BLEND_ADD = 6
                                    --GFX_BLEND_ADDALPHA = 7
                                    --GFX_BLEND_SUBALPHA = 8
                                    --GFX_BLEND_SUB = 9
                                    --GFX_BLEND_MUL = 10 
                                    --GFX_BLEND_ALPHAMUL = 11
                                    --GFX_BLEND_IALPHAMUL = 12 
                                    --GFX_BLEND_IALPHA = 13
                                    --GFX_BLEND_IALPHAPREMULT = 14
                                    --GFX_BLEND_IALPHADEST = 15
                                    --GFX_BLEND_IALPHADESTPREMULT = 16
                                    --GFX_BLEND_MUL2X = 17
                                    blendMode          = 2,
                                    --PARGEN_GEN_POINTS = 0
                                    --PARGEN_GEN_RECTANGLE = 1
                                    --PARGEN_GEN_CIRCLE = 2
                                    genGenType      	   	= 2,
                                    circleradius     	    	= 0.1,
                                    innercircleradius        = 0.0,
                                    --PARGEN_MODE_FOLLOW  = 0
                                    --PARGEN_MODE_COMPLEX = 1
                                    genMode            = 1,
                                }
                            },
                        }
                    },
                }
            },
            {
                FxDescriptor_Template=
                {
                    name = "test7",
                    texture	= "FX/Common/prtcl_test_1.tga",
                    gen=
                    {
                        ITF_ParticleGenerator_Template =
                        {
                            useAnim = 1,
                            animstart = 5,
                            animend = 5,
                            AnimUVfreq = 0,
                            params =
                            {
                                ParticleGeneratorParameters =
                                {
                                    maxParticles         = 20,
                                    emitParticlesCount   = -1,
                                    velNorm              = 7 ,
                                    grav                 = vectorNew(0.0,-8.0,0.0),
                                    acc                  = vectorNew(0.0,0.0,0.0),
                                    velocityVar          = 1.5,
                                    friction             = 0.95,
                                    freq                 = 0.05 ,
                                    emitInterval         = 0.1,
                                    initAngle            = 0.0,
                                    angleDelta           = 360,
                                    angularSpeed         = 30,
                                    angularSpeedDelta    = 400,
                                    timeTarget           = 0.0,
                                    startTime             = 1.0,
                                    stopTime              = 1.0,
                                    genBox               =
                                    {
                                        AABB =
                                        {
                                            MIN = vector2dNew(-0.0,-0.0),
                                            MAX = vector2dNew(0.1,0.1),
                                        }
                                    },
                                    boundingBox          =
                                    {
                                        AABB =
                                        {
                                            MIN = vector2dNew(0.0,0.0),
                                            MAX = vector2dNew(0.1,0.1),
                                        }
                                    },
                                    nbPhase              = 3,
                                    phases =
                                    {
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.1,
                                                colorMin    = "0x00ffffff",
                                                colorMax    = "0x00ffffff",
                                                sizeMin     = vector2dNew(0.1,0.1),
                                                sizeMax     = vector2dNew(0.3,0.3),
                                            }
                                        },
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.5,
                                                colorMin    = "0xffffffff",
                                                colorMax    = "0xffffffff",
                                                sizeMin     = vector2dNew(0.1,0.1),
                                                sizeMax     = vector2dNew(0.3,0.3),
                                            }
                                        },
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.1,
                                                colorMin    = "0xffffffff",
                                                colorMax    = "0xffffffff",
                                                sizeMin     = vector2dNew(0.1,0.1),
                                                sizeMax     = vector2dNew(0.1,0.1),
                                            }
                                        },
                                    },
                                    renderPrio           = 0,
                                    --GFX_BLEND_UNKNOWN = 0,
                                    --GFX_BLEND_COPY = 1
                                    --GFX_BLEND_ALPHA = 2
                                    --GFX_BLEND_ALPHAPREMULT = 3
                                    --GFX_BLEND_ALPHADEST = 4
                                    --GFX_BLEND_ALPHADESTPREMULT = 5
                                    --GFX_BLEND_ADD = 6
                                    --GFX_BLEND_ADDALPHA = 7
                                    --GFX_BLEND_SUBALPHA = 8
                                    --GFX_BLEND_SUB = 9
                                    --GFX_BLEND_MUL = 10 
                                    --GFX_BLEND_ALPHAMUL = 11
                                    --GFX_BLEND_IALPHAMUL = 12 
                                    --GFX_BLEND_IALPHA = 13
                                    --GFX_BLEND_IALPHAPREMULT = 14
                                    --GFX_BLEND_IALPHADEST = 15
                                    --GFX_BLEND_IALPHADESTPREMULT = 16
                                    --GFX_BLEND_MUL2X = 17
                                    blendMode          = 2,
                                    --PARGEN_GEN_POINTS = 0
                                    --PARGEN_GEN_RECTANGLE = 1
                                    --PARGEN_GEN_CIRCLE = 2
                                    genGenType         = 0,
                                    --PARGEN_MODE_FOLLOW  = 0
                                    --PARGEN_MODE_COMPLEX = 1
                                    genMode            = 1,
                                }
                            },
                        }
                    },
                }
            },
            {
                FxDescriptor_Template=
                {
                    name = "test6",
                    texture	= "FX/Common/prtcl_test_1.tga",
                    gen=
                    {
                        ITF_ParticleGenerator_Template =
                        {
                            useAnim = 1,
                            animstart = 4,
                            animend = 4,
                            AnimUVfreq = 0,
                            params =
                            {
                                ParticleGeneratorParameters =
                                {
                                    maxParticles         = 20,
                                    emitParticlesCount   = -1,
                                    velNorm              = 1.5,
                                    grav                 = vectorNew(0.0,0.2,0.0),
                                    acc                  = vectorNew(3.0,0.0,0.0),
                                    velocityVar          = 0.25,
                                    friction             = 0.98,
                                    freq                 = 0.15,
                                    emitInterval         = 0.075,
                                    initAngle            = 0.0,
                                    angleDelta           = 30,
                                    angularSpeed         = 0,
                                    angularSpeedDelta    = 30,
                                    timeTarget           = 0.0,
                                    startTime             = 1.0,
                                    stopTime              = 1.0,
                                    genBox               =
                                    {
                                        AABB =
                                        {
                                            MIN = vector2dNew(-0.0,-0.0),
                                            MAX = vector2dNew(0.1,0.1),
                                        }
                                    },
                                    boundingBox          =
                                    {
                                        AABB =
                                        {
                                            MIN = vector2dNew(0.0,0.0),
                                            MAX = vector2dNew(0.1,0.1),
                                        }
                                    },
                                    nbPhase              = 3,
                                    phases =
                                    {
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.3,
                                                colorMin    = "0xffffffff",
                                                colorMax    = "0x22ffffff",
                                                sizeMin     = vector2dNew(0.0,0.0),
                                                sizeMax     = vector2dNew(0.0,0.0),
                                            }
                                        },
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.3,
                                                colorMin    = "0xffffffff",
                                                colorMax    = "0x22ffffff",
                                                sizeMin     = vector2dNew(0.8,0.8),
                                                sizeMax     = vector2dNew(1.0,1.0),
                                            }
                                        },
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.5,
                                                colorMin    = "0xffffffff",
                                                colorMax    = "0x22ffffff",
                                                sizeMin     = vector2dNew(0.0,0.0),
                                                sizeMax     = vector2dNew(0.0,0.0),
                                            }
                                        },
                                    },
                                    renderPrio           = 0,
                                    --GFX_BLEND_UNKNOWN = 0,
                                    --GFX_BLEND_COPY = 1
                                    --GFX_BLEND_ALPHA = 2
                                    --GFX_BLEND_ALPHAPREMULT = 3
                                    --GFX_BLEND_ALPHADEST = 4
                                    --GFX_BLEND_ALPHADESTPREMULT = 5
                                    --GFX_BLEND_ADD = 6
                                    --GFX_BLEND_ADDALPHA = 7
                                    --GFX_BLEND_SUBALPHA = 8
                                    --GFX_BLEND_SUB = 9
                                    --GFX_BLEND_MUL = 10 
                                    --GFX_BLEND_ALPHAMUL = 11
                                    --GFX_BLEND_IALPHAMUL = 12 
                                    --GFX_BLEND_IALPHA = 13
                                    --GFX_BLEND_IALPHAPREMULT = 14
                                    --GFX_BLEND_IALPHADEST = 15
                                    --GFX_BLEND_IALPHADESTPREMULT = 16
                                    --GFX_BLEND_MUL2X = 17
                                    blendMode          = 2,
                                    --PARGEN_GEN_POINTS = 0
                                    --PARGEN_GEN_RECTANGLE = 1
                                    --PARGEN_GEN_CIRCLE = 2
                                    genGenType         = 0,
                                    --PARGEN_MODE_FOLLOW  = 0
                                    --PARGEN_MODE_COMPLEX = 1
                                    genMode            = 1,
                                }
                            },
                        }
                    },
                }
            },
            {
                FxDescriptor_Template=
                {
                    name = "GolemHit",
                    texture	= "FX/Common/Prtcl_fx_golem.tga",
                    gen=
                    {
                        ITF_ParticleGenerator_Template =
                        {
                            params =
                            {
                                ParticleGeneratorParameters =
                                {
                                    maxParticles         = 3,
                                    emitParticlesCount   = 3,
                                    velNorm              = 1,
                                    grav                 = vectorNew(0.0,-14.0,5),
                                    acc                  = vectorNew(0.0,0.0,0),
                                    velocityVar          = 4,
                                    friction             = 0.98,
                                    freq                 = 0.001,
                                    emitInterval         = 0.005,
                                    initAngle            = -180,
                                    angleDelta           = 360,
                                    angularSpeed         = -200,
                                    angularSpeedDelta    = 200,
                                    timeTarget           = 0.0,
                                    startTime             = 1.0,
                                    stopTime              = 2.0,
                                    genBox               =
                                    {
                                        AABB =
                                        {
                                            MIN = vector2dNew(-1,-1),
                                            MAX = vector2dNew(1,1),
                                        }
                                    },
                                    boundingBox          =
                                    {
                                        AABB =
                                        {
                                            MIN = vector2dNew(15.0,15.0),
                                            MAX = vector2dNew(15.0,15.0),
                                        }
                                    },
                                    nbPhase              = 3,
                                    phases =
                                    {
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.01,
                                                colorMin    = "0x00000000",
                                                colorMax    = "0x00000000",
                                                sizeMin     = vector2dNew(0.5,0.5),
                                                sizeMax     = vector2dNew(1,1),
                                            }
                                        },
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 2,
                                                colorMin    = "0x99999999",
                                                colorMax    = "0x99ffffff",
                                                sizeMin     = vector2dNew(1,1),
                                                sizeMax     = vector2dNew(1.5,1.5),
                                            }
                                        },
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.1,
                                                colorMin    = "0x00000000",
                                                colorMax    = "0x00000000",
                                                sizeMin     = vector2dNew(1.0,1.0),
                                                sizeMax     = vector2dNew(1.5,1.5),
                                            }
                                        },
                                    },
                                    renderPrio           = 1,
                                    --GFX_BLEND_UNKNOWN = 0,
                                    --GFX_BLEND_COPY = 1
                                    --GFX_BLEND_ALPHA = 2
                                    --GFX_BLEND_ALPHAPREMULT = 3
                                    --GFX_BLEND_ALPHADEST = 4
                                    --GFX_BLEND_ALPHADESTPREMULT = 5
                                    --GFX_BLEND_ADD = 6
                                    --GFX_BLEND_ADDALPHA = 7
                                    --GFX_BLEND_SUBALPHA = 8
                                    --GFX_BLEND_SUB = 9
                                    --GFX_BLEND_MUL = 10 
                                    --GFX_BLEND_ALPHAMUL = 11
                                    --GFX_BLEND_IALPHAMUL = 12 
                                    --GFX_BLEND_IALPHA = 13
                                    --GFX_BLEND_IALPHAPREMULT = 14
                                    --GFX_BLEND_IALPHADEST = 15
                                    --GFX_BLEND_IALPHADESTPREMULT = 16
                                    --GFX_BLEND_MUL2X = 17
                                    blendMode          = 2,
                                    --PARGEN_GEN_POINTS = 0
                                    --PARGEN_GEN_RECTANGLE = 1
                                    --PARGEN_GEN_CIRCLE = 2
                                    genGenType         = 1,
                                    --PARGEN_MODE_FOLLOW  = 0
                                    --PARGEN_MODE_COMPLEX = 1
                                    genMode            = 1,
                                }
                            },
                        }
                    },
                }
            },
            {
                FxDescriptor_Template=
                {
                    name = "flash",
                    texture	= "FX/Common/prtcl_hit.tga",
                    gen=
                    {
                        ITF_ParticleGenerator_Template =
                        {
                            useAnim = 1,
	                        animstart = 0,
	                        animend =3 ,
	                        AnimUVfreq = 16.0,
                            params =
                            {
                                ParticleGeneratorParameters =
                                {
                                    maxParticles         = 1,
                                    emitParticlesCount   = 1,
                                    velNorm              = 0.0,
                                    grav                 = vectorNew(0.0,0.0,1.0),
                                    acc                  = vectorNew(0.0,0.0,0.0),
                                    velocityVar          = 0.0,
                                    friction             = 1.0,
                                    freq                 = 0.0000001,
                                    emitInterval         = 0.01,
                                    initAngle            = 0.0,
                                    angleDelta           = 0,
                                    angularSpeed         = 0,
                                    angularSpeedDelta    = 0,
                                    timeTarget           = 0.0,
                                    startTime             = 0.0,
                                    stopTime              = 1.0,
                                    genBox               =
                                    {
                                        AABB =
                                        {
                                            MIN = vector2dNew(0.0,0.0),
                                            MAX = vector2dNew(0.0,0.0),
                                        }
                                    },
                                    boundingBox          =
                                    {
                                        AABB =
                                        {
                                            -- MIN = vector2dNew(15.0,15.0),
                                            -- MAX = vector2dNew(15.0,15.0),
                                            MIN = vector2dNew(1.0,1.0),
                                            MAX = vector2dNew(1.0,1.0),
                                        }
                                    },
                                    nbPhase              = 1,
                                    phases =
                                    {
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.25,
                                                colorMin    = "0xffffffff",
                                                colorMax    = "0xffffffff",
                                                sizeMin     = vector2dNew(2.0,2.0),
                                                sizeMax     = vector2dNew(2.0,2.0),
                                            }
                                        },
                                    },
                                    renderPrio           = 1,
                                    --GFX_BLEND_UNKNOWN = 0,
                                    --GFX_BLEND_COPY = 1
                                    --GFX_BLEND_ALPHA = 2
                                    --GFX_BLEND_ALPHAPREMULT = 3
                                    --GFX_BLEND_ALPHADEST = 4
                                    --GFX_BLEND_ALPHADESTPREMULT = 5
                                    --GFX_BLEND_ADD = 6
                                    --GFX_BLEND_ADDALPHA = 7
                                    --GFX_BLEND_SUBALPHA = 8
                                    --GFX_BLEND_SUB = 9
                                    --GFX_BLEND_MUL = 10 
                                    --GFX_BLEND_ALPHAMUL = 11
                                    --GFX_BLEND_IALPHAMUL = 12 
                                    --GFX_BLEND_IALPHA = 13
                                    --GFX_BLEND_IALPHAPREMULT = 14
                                    --GFX_BLEND_IALPHADEST = 15
                                    --GFX_BLEND_IALPHADESTPREMULT = 16
                                    --GFX_BLEND_MUL2X = 17
                                    blendMode          = 7,
                                    --PARGEN_GEN_POINTS = 0
                                    --PARGEN_GEN_RECTANGLE = 1
                                    --PARGEN_GEN_CIRCLE = 2
                                    genGenType         = 1,
                                    --PARGEN_MODE_FOLLOW  = 0
                                    --PARGEN_MODE_COMPLEX = 1
                                    genMode            = 0,
                                }
                            },
                        }
                    },
                }
            },
            {
                FxDescriptor_Template=
                {
                    name = "test10",
                    texture	= "FX/Common/prtcl_test_1.tga",
                    gen=
                    {
                        ITF_ParticleGenerator_Template =
                        {
                            useAnim = 1,
	                        animstart = 6,
	                        animend = 6,
	                        AnimUVfreq = 0,
                            params =
                            {
                                ParticleGeneratorParameters =
                                {
                                    maxParticles         = 50,
                                    emitParticlesCount   = -1,
                                    velNorm              = 10 ,
                                    grav                 = vectorNew(0.0,0.2,0.0),
                                    acc                  = vectorNew(0.0,0.0,0.0),
                                    velocityVar          = 0.2,
                                    friction             = 0.98,
                                    freq                 = 0.05,
                                    emitInterval         = 0.002,
                                    initAngle            = 0.0,
                                    angleDelta           = 360,
                                    angularSpeed         = 00,
                                    angularSpeedDelta    = 100,
                                    timeTarget           = 0.0,
                                    startTime             = 1.0,
                                    stopTime              = 1.0,
                                    genBox               =
                                    {
                                        AABB =
                                        {
                                            MIN = vector2dNew(-0.0,-0.0),
                                            MAX = vector2dNew(0.1,0.1),
                                        }
                                    },
                                    boundingBox          =
                                    {
                                        AABB =
                                        {
                                            MIN = vector2dNew(0.0,0.0),
                                            MAX = vector2dNew(0.1,0.1),
                                        }
                                    },
                                    nbPhase              = 3,
                                    phases =
                                    {
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.5,
                                                colorMin    = "0x00ffffff",
                                                colorMax    = "0x00ffffff",
                                                sizeMin     = vector2dNew(1,1),
                                                sizeMax     = vector2dNew(1,1),
                                            }
                                        },
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 0.5,
                                                colorMin    = "0xffffffff",
                                                colorMax    = "0xffffffff",
                                                sizeMin     = vector2dNew(1,1),
                                                sizeMax     = vector2dNew(1,1),
                                            }
                                        },
                                        {
                                            ParPhase =
                                            {
                                                phaseTime   = 1,
                                                colorMin    = "0xffffffff",
                                                colorMax    = "0xffffffff",
                                                sizeMin     = vector2dNew(1,1),
                                                sizeMax     = vector2dNew(1,1),
                                            }
                                        },
                                    },
                                    renderPrio           = 0,
                                    --GFX_BLEND_UNKNOWN = 0,
                                    --GFX_BLEND_COPY = 1
                                    --GFX_BLEND_ALPHA = 2
                                    --GFX_BLEND_ALPHAPREMULT = 3
                                    --GFX_BLEND_ALPHADEST = 4
                                    --GFX_BLEND_ALPHADESTPREMULT = 5
                                    --GFX_BLEND_ADD = 6
                                    --GFX_BLEND_ADDALPHA = 7
                                    --GFX_BLEND_SUBALPHA = 8
                                    --GFX_BLEND_SUB = 9
                                    --GFX_BLEND_MUL = 10 
                                    --GFX_BLEND_ALPHAMUL = 11
                                    --GFX_BLEND_IALPHAMUL = 12 
                                    --GFX_BLEND_IALPHA = 13
                                    --GFX_BLEND_IALPHAPREMULT = 14
                                    --GFX_BLEND_IALPHADEST = 15
                                    --GFX_BLEND_IALPHADESTPREMULT = 16
                                    --GFX_BLEND_MUL2X = 17
                                    blendMode          = 2,
                                    --PARGEN_GEN_POINTS = 0
                                    --PARGEN_GEN_RECTANGLE = 1
                                    --PARGEN_GEN_CIRCLE = 2
                                    genGenType         = 0,
                                    --PARGEN_MODE_FOLLOW  = 0
                                    --PARGEN_MODE_COMPLEX = 1
                                    genMode            = 1,
                                }
                            },
                        }
                    },
                }
            },
        },
    }
}

appendTable(params.Actor_Template.COMPONENTS,{component})
