

DESCRIPTOR = 
{
	{
                SoundDescriptor_Template=
                {
			        name="MRK_Squashed_Beard_Voice",
			        WwiseEventGuid="25CB8B64-4188-4DBD-81B8-3FAAACEA6E02",
                    volume=-13,
			        category="NPC_CAT_01",
					--limitCategory="Fakir_Squash_Voice",
					limitMode=LimiterMode.RejectNew,
					maxInstances=3,
                    params =
					{
						SoundParams =
						{
							numChannels = 1,
							loop = 0,
							playMode = 1,
							randomVolMin = 0.000000,
							randomVolMax = 0.000000,
							randomPitchMin = 1.0,
							randomPitchMax = 1.0,
							fadeInTime = 0.00000,
							fadeOutTime = 0.000000,
							modifiers=
							{
						
								{
									NAME="SpatializedPanning",
									SpatializedPanning=
									{
										widthMin = 0.5,
										widthMax = 2.0,
									}
								},
								{
									NAME="ScreenRollOff",
									ScreenRollOff=
									{
										distanceMin = 0.5,
										distanceMax = 2.0,
									}
								},
								
							}
						},
					},
                    files=
                    {
                        { VAL = "Sound/200_Characters/210_Common/Fakir/Vox_Fakir_Squash_01.wav", },
						{ VAL = "Sound/200_Characters/210_Common/Fakir/Vox_Fakir_Squash_02.wav", }, 
				        
                    }
                }
	},			
}

appendTable(component.SoundComponent_Template.soundList,DESCRIPTOR)
