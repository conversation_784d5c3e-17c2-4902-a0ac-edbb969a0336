includeReference("Actor/Includes/helpers.ilu")
includeReference("Actor/Includes/gameplay_types.ilu")

shape =
{
    Name="PhysShapeCircle",
    PhysShapeCircle =
    {
        radius = 0.5,
    }
}


params =
{
    
NAME = "Actor_Template",
Actor_Template =
    {
        SCALE = vector2dNew(3.0,3.0),
        RANK = 0,		  
		red = 10,
		green = 40,
		blue = 40,
        COMPONENTS =
        {
            {
                NAME="AnimatedComponent_Template",
                AnimatedComponent_Template =
                {
                    animationPath = "Actor/Friendly/Butterfly/animation/",
                    animSet=
                    {
						SubAnimSet_Template =
                        {
		                    animations={
								{NAME="SubAnim_Template",SubAnim_Template={friendlyName="Stand",name="stand.anm",loop=1}},
								{NAME="SubAnim_Template",SubAnim_Template={friendlyName="Takeoff",name="takeoff.anm"}},
								{NAME="SubAnim_Template",SubAnim_Template={friendlyName="Offstand",name="offstand.anm",loop=1}},
								{NAME="SubAnim_Template",SubAnim_Template={friendlyName="Landing",name="landing.anm"}},
							},
						},
					},
                    defaultAnimation = "UNTRIGGER",
					tree = 
                    {
                        AnimTree_Template =
                        {
                            nodes =
                            {
                                {
                                    NAME="AnimTreeNodePlayAnim_Template",
                                    AnimTreeNodePlayAnim_Template =
                                    {
                                        nodeName = "TRIGGER",
                                        animationName = "Offstand"
                                    }
                                },
                                {
                                    NAME="AnimTreeNodePlayAnim_Template",
                                    AnimTreeNodePlayAnim_Template =
                                    {
                                        nodeName = "UNTRIGGER",
                                        animationName = "Stand"
                                    }
                                },
                            },
                            nodeTransitions =
                            {
                                {
                                    BlendTreeTransition_Template =
                                    {
                                        from =
                                        {
                                            { VAL = "TRIGGER" },
                                        },
                                        to =
                                        {
                                            { VAL = "UNTRIGGER" },
                                        },
                                        node =
                                        {
                                            NAME="AnimTreeNodePlayAnim_Template",
                                            AnimTreeNodePlayAnim_Template =
                                            {
                                                animationName = "landing"
                                            }
                                        },
                                        blend = 4,
                                    }
                                },
                                {
                                    BlendTreeTransition_Template =
                                    {
                                        from =
                                        {
                                            { VAL = "UNTRIGGER" },
                                        },
                                        to =
                                        {
                                            { VAL = "TRIGGER" },
                                        },
                                        node =
                                        {
                                            NAME="AnimTreeNodePlayAnim_Template",
                                            AnimTreeNodePlayAnim_Template =
                                            {
                                                animationName = "Takeoff"
                                            }
                                        },
                                        blend = 4,
                                    }
                                },
                            },
                        }
                    },
                }
            },
            -- {
                -- NAME="ActorSpawnComponent_Template",
                -- ActorSpawnComponent_Template =
                -- {
					-- spawnActors=
					-- {
					    -- {
						    -- SpawnData =
						    -- {
							    -- spawnActorBoneName = "B_butterfly01",
                                -- actorLua = "Personal/Colas/TestPrtcl/FX/Prtcl_test_5.lua",
						    -- },
						-- },
						-- {
						    -- SpawnData =
						    -- {
							    -- spawnActorBoneName = "B_butterfly03",
                                -- actorLua = "Personal/Colas/TestPrtcl/FX/Prtcl_test_5.lua",
						    -- },				
						-- },
					-- },
				-- },
			-- },
            {
                NAME="PhantomComponent_Template",
                PhantomComponent_Template =
                {
                    collisionGroup = CollisionGroup.Item,
                    -- drawDebug = 1,
                    shape = shape,
                }
            },
            {
                NAME = "LinkComponent_Template",
                LinkComponent_Template =
                {
                },
            },
            {
                NAME = "PhantomDetectorComponent_Template",
                PhantomDetectorComponent_Template =
                {
                    shape = shape,
                },
            },
            {
                NAME="TriggerComponent_Template",
                TriggerComponent_Template =
                {
					triggerOnWind = 1,
					TriggerOnce = 0,
                    TriggerSelf = 1,
                    TriggerChildren = 1,
                    TriggerActivator = 0,
                    ResetOnCheckpoint = 1,
                    onEnterEvent = 
                    {
                        NAME="EventTrigger",
                        EventTrigger = 
                        {
                            activated = 1,
                        }
                    },
                    onExitEvent = 
                    {
                        NAME="EventTrigger",
                        EventTrigger = 
                        {
                            activated = 0,
                        }
                    },
				},
			},	
            {
                NAME="AnimTriggeredComponent_Template",
                AnimTriggeredComponent_Template =
                {
					TriggeredAction = "TRIGGER",
					UntriggeredAction = "UNTRIGGER",		
				},
			},				
        },
    }
}

includeReference("Actor/Friendly/Butterfly/Components/Butterfly_sound.ilu")
includeReference("Actor/Friendly/Butterfly/Components/Butterfly_fxController.ilu")
