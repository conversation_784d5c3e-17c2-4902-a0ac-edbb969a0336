<?xml version="1.0" ?>
<root>
	<AnimsList>
		<Element index="1" type="string" value="Actor/Enemy/ShooterGunner/Animation/Death.anm"/>
		<Element index="2" type="string" value="Actor/Enemy/ShooterGunner/Animation/GunnerAnim1.anm"/>
		<Element index="3" type="string" value="Actor/Enemy/ShooterGunner/Animation/Receive_Hit.anm"/>
		<Element index="4" type="string" value="Actor/Enemy/ShooterGunner/Animation/Rotation.anm"/>
		<Element index="5" type="string" value="Actor/Enemy/ShooterGunner/Animation/Shoot.anm"/>
		<Element index="6" type="string" value="Actor/Enemy/ShooterGunner/Animation/Shoot_Addit.anm"/>
		<Element index="7" type="string" value="Actor/Enemy/ShooterGunner/Animation/Stand.anm"/>
		<Element index="8" type="string" value="Actor/Enemy/ShooterGunner/Animation/Stand_Death.anm"/>
	</AnimsList>
	<PatchBankList>
		<Tiko type="string" value="Actor/Enemy/ShooterGunner/Animation/Tiko.tga"/>
	</PatchBankList>
	<Scale type="number" value="1"/>
	<SceneVersion type="number" value="17"/>
	<Squeleton type="string" value="Actor/Enemy/ShooterGunner/Animation/Gunner_Squeleton.skl"/>
	<UseDataFolder type="boolean" value="true"/>
	<UseRelative type="boolean" value="true"/>
</root>
