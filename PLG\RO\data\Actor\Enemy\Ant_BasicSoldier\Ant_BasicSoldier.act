includeReference("Actor/Includes/helpers.ilu")

params =
{
    
NAME = "Actor_Template",
Actor_Template =
    {
        SCALE = vector2dNew(2.5,2.5),
        RANK = 0,
        COMPONENTS =
        {
        }
    }
}

includeReference("Actor/Enemy/Ant_BasicSoldier/Components/Ant_BasicSoldier_AI.ilu")
includeReference("Actor/Enemy/Ant_BasicSoldier/Components/Ant_BasicSoldier_Stick.ilu")
includeReference("Actor/Enemy/Ant_BasicSoldier/Components/Ant_BasicSoldier_AnimatedComponent.ilu")
includeReference("Actor/Enemy/Ant_BasicSoldier/Components/Ant_BasicSoldier_sound.ilu")
includeReference("Actor/Enemy/Ant_BasicSoldier/Components/Ant_BasicSoldier_fxController.ilu")
includeReference("Actor/Enemy/Common/Components/FxBankComponent.ilu")
