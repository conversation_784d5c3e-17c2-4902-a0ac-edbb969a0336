includeReference("Actor/Includes/Sound/sound_base.ilu")
includeReference("Actor/Includes/helpers.ilu")
component = 
{
    NAME = "FXControllerComponent_Template",
    FXControllerComponent_Template=
    {
        fxControlList =
        {
            --{name="MRK_example",sound="example",playContact=1,fxStopOnEndAnim=1,fxPlayOnce=1},
		    
            {FXControl={name="MRK_Squashed",sounds={{VAL="MRK_Squashed"},{VAL="FakirBell"}}}},
			-- {FXControl={name="MRK_Squashed_Beard",sound="MRK_Squashed_Beard"}},
			{FXControl={name="MRK_Squashed_Voice",sound="MRK_Squashed_Voice"}},
			{FXControl={name="MRK_Unsquashed",sound="MRK_Unsquashed"}},
			{FXControl={name="MRK_Squashed_Idle",sound="MRK_Squashed_Idle",fxStopOnEndAnim=1,fxPlayOnce=1}},
			{FXControl={name="MRK_Squashed_Beard_Voice",sound="MRK_Squashed_Beard_Voice"}},
			
			
        },
    },
}
appendTable(params.Actor_Template.COMPONENTS,{component})
