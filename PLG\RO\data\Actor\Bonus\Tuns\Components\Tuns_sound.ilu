includeReference("Actor/Includes/Sound/sound_base.ilu")

component = {
    NAME="SoundComponent_Template",
    SoundComponent_Template = {
        soundList = {
            {SoundDescriptor_Template={   
                name="MRK_Tuns_Material",
                volume=-25,
                category="Bonus",
				limitCategory="tuns_material",
                params = MONO_TUNS,
                files=
                {
                    -- { VAL = "Sound/Environment/Tuns/Tuns_material01.wav" },
                    -- { VAL = "Sound/Environment/Tuns/Tuns_material02.wav" },
                    -- { VAL = "Sound/Environment/Tuns/Tuns_material03.wav" },
                }
            }}, 
        }
    }
}

appendTable(params.Actor_Template.COMPONENTS,{component})


