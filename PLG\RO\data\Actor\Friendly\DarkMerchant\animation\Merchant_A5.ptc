<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-89.************"/>
			<AngleLocal type="number" value="-89.************"/>
			<Lenght type="number" value="0.071527644991875"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Head"/>
			<Pos type="vector" x="0.95873719453812" y="0.33804556727409" z="0"/>
			<PosEnd type="vector" x="0.95873755216599" y="0.40957319736481" z="0"/>
			<PosLocal type="vector" x="0.95873719453812" y="0.33804556727409" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp410"/>
				<Element index="2" type="string" value="MyApp411"/>
				<Element index="3" type="string" value="MyApp412"/>
				<Element index="4" type="string" value="MyApp413"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A5"/>
			<UID type="string" value="MyApp409"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A5.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Head01"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp411"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp409"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-0.23638190329075" y="0.024768019095063" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="1" y="-4.9998561735265e-006" z="0"/>
			<PosUV type="vector" x="0.98350512981415" y="0.32113760709763" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A5"/>
			<UID type="string" value="MyApp410"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp410"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp409"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="-0.24706919491291" y="-0.024763496592641" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-1" y="4.9998561735265e-006" z="0"/>
			<PosUV type="vector" x="0.93397361040115" y="0.32037341594696" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A5"/>
			<UID type="string" value="MyApp411"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp413"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp409"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="1.1562062501907" y="0.022856239229441" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="1" y="-4.9998561735265e-006" z="0"/>
			<PosUV type="vector" x="0.98159384727478" y="0.4207461476326" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A5"/>
			<UID type="string" value="MyApp412"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp412"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp409"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="1.1615546941757" y="-0.027061698958278" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-1" y="4.9998561735265e-006" z="0"/>
			<PosUV type="vector" x="0.93167591094971" y="0.42112895846367" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A5"/>
			<UID type="string" value="MyApp413"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A5"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="-1398.7863769531" y="-299.23193359375" z="0"/>
	<zoomSize type="vector" x="2615.7182617188" y="2615.7180175781" z="2.5175342559814"/>
</root>
