includeReference("Actor/Includes/Sound/sound_base.ilu")
includeReference("Actor/Includes/helpers.ilu")

component =
{
    NAME="SoundComponent_Template",
    SoundComponent_Template =
    {
        soundList =
        {
            
            {
                SoundDescriptor_Template=
                {
                    name="MRK_Fish_Material",
                    volume=-10,
			        category="Environment",
                    params = MONO_3D_RANDOM_NOVOL_MEDPITCH_NOFADE,
                    files=
                    {
						 {VAL="Sound/Friendly/Fish/m_fish01.wav"} ,
						 {VAL="Sound/Friendly/Fish/m_fish02.wav"} ,
						 {VAL="Sound/Friendly/Fish/m_fish03.wav"} ,
						 {VAL="Sound/Friendly/Fish/m_fish04.wav"} ,
						 {VAL="Sound/Friendly/Fish/m_fish05.wav"} ,
                    }
                }
            },
			{
                SoundDescriptor_Template=
                {
                    name="MRK_Fish_Lum_Material",
                    volume=-20,
			        category="Environment",
                    params = MONO_3D_RANDOM_NOVOL_MEDPITCH_NOFADE,
                    files=
                    {
						 {VAL="Sound/Friendly/Fish/m_fish_lum01.wav"} ,
                    }
                }
            },
        }
    }
}

includeReference("Actor/Includes/Sound/hit_rayman.ilu")
includeReference("Actor/Includes/Sound/hit_globox.ilu")
includeReference("Actor/Includes/Sound/hit_livingstone.ilu")

appendTable(component.SoundComponent_Template.soundList,HIT_RAYMAN)
appendTable(component.SoundComponent_Template.soundList,HIT_GLOBOX)
appendTable(component.SoundComponent_Template.soundList,HIT_LIVINGSTONE)

appendTable(params.Actor_Template.COMPONENTS,{component})

component = {}
