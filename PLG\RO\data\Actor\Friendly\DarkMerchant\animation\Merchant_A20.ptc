<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="1.6666016968671"/>
			<AngleLocal type="number" value="1.6666016968671"/>
			<Lenght type="number" value="0.012712074443698"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Bonus_02"/>
			<Pos type="vector" x="0.85447239875793" y="0.13584341108799" z="0"/>
			<PosEnd type="vector" x="0.86717909574509" y="0.13547369837761" z="0"/>
			<PosLocal type="vector" x="0.85447239875793" y="0.13584341108799" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="D1710MyApp1699"/>
				<Element index="2" type="string" value="D1710MyApp1700"/>
				<Element index="3" type="string" value="D1710MyApp1701"/>
				<Element index="4" type="string" value="D1710MyApp1702"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A20"/>
			<UID type="string" value="D1710MyApp1698"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A20.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Bonus_02"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D1710MyApp1700"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D1710MyApp1698"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.38941836357117" y="0.92106103897095" z="0"/>
					<Pos type="vector" x="-1.8295983076096" y="0.01052331738174" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.41604140400887" y="-0.90934574604034" z="0"/>
			<PosUV type="vector" x="0.83091819286346" y="0.12600097060204" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A20"/>
			<UID type="string" value="D1710MyApp1699"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D1710MyApp1699"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D1710MyApp1698"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.19350835680962" y="-0.98109865188599" z="0"/>
					<Pos type="vector" x="-0.88946050405502" y="-0.040475886315107" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.22196036577225" y="0.97505569458008" z="0"/>
			<PosUV type="vector" x="0.84434747695923" y="0.1766310185194" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A20"/>
			<UID type="string" value="D1710MyApp1700"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D1710MyApp1702"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D1710MyApp1698"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="-0.29552021622658" y="0.9553365111351" z="0"/>
					<Pos type="vector" x="1.0193566083908" y="0.022403310984373" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.32317981123924" y="-0.94633758068085" z="0"/>
			<PosUV type="vector" x="0.86677348613739" y="0.11307270824909" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A20"/>
			<UID type="string" value="D1710MyApp1701"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D1710MyApp1701"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D1710MyApp1698"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.38941836357117" y="-0.92106103897095" z="0"/>
					<Pos type="vector" x="2.3585932254791" y="-0.02787022665143" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.41604140400887" y="0.90934574604034" z="0"/>
			<PosUV type="vector" x="0.88525289297104" y="0.16282984614372" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A20"/>
			<UID type="string" value="D1710MyApp1702"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A20"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="-7162.1083984375" y="-678.64086914063" z="0"/>
	<zoomSize type="vector" x="9156.5869140625" y="9156.58984375" z="8.6220235824585"/>
</root>
