<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-90"/>
			<AngleLocal type="number" value="-90"/>
			<Lenght type="number" value="0.020752131938934"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_RockGlow"/>
			<Pos type="vector" x="0.68303698301315" y="0.74194628000259" z="0"/>
			<PosEnd type="vector" x="0.68303698301315" y="0.76269841194153" z="0"/>
			<PosLocal type="vector" x="0.68303698301315" y="0.74194628000259" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp5192"/>
				<Element index="2" type="string" value="MyApp5193"/>
				<Element index="3" type="string" value="MyApp5194"/>
				<Element index="4" type="string" value="MyApp5195"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A45"/>
			<UID type="string" value="MyApp5191"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A45.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_RockGlow"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp5193"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp5191"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.2089746594429" y="0.97792112827301" z="0"/>
					<Pos type="vector" x="-2.2272698879242" y="0.06366378068924" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.97792112827301" y="0.2089746594429" z="0"/>
			<PosUV type="vector" x="0.74670076370239" y="0.69572567939758" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A45"/>
			<UID type="string" value="MyApp5192"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp5192"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp5191"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.29552021622658" y="-0.9553365111351" z="0"/>
					<Pos type="vector" x="-1.9322587251663" y="-0.06257551908493" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.9553365111351" y="0.29552021622658" z="0"/>
			<PosUV type="vector" x="0.62046146392822" y="0.70184779167175" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A45"/>
			<UID type="string" value="MyApp5193"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp5195"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp5191"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.41351920366287" y="0.91049546003342" z="0"/>
					<Pos type="vector" x="3.0116555690765" y="0.02653294801712" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.91049546003342" y="0.41351920366287" z="0"/>
			<PosUV type="vector" x="0.70956993103027" y="0.8044445514679" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A45"/>
			<UID type="string" value="MyApp5194"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp5194"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp5191"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.38941836357117" y="-0.92106103897095" z="0"/>
					<Pos type="vector" x="2.6799488067627" y="-0.024342656135559" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.92106103897095" y="0.38941836357117" z="0"/>
			<PosUV type="vector" x="0.65869432687759" y="0.79756093025208" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A45"/>
			<UID type="string" value="MyApp5195"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A45"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="-1366.048828125" y="-1826.119140625" z="0"/>
	<zoomSize type="vector" x="3195.9616699219" y="3195.9621582031" z="3.0908718109131"/>
</root>
