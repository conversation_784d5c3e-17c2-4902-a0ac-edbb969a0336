#!/usr/bin/env python3
"""
Example usage of the batch_git_commit.py script

This demonstrates various ways to use the batch commit functionality
for different scenarios common in game development.
"""

import subprocess
import sys
from pathlib import Path

def run_command(cmd, description):
    """Run a command and display the results."""
    print(f"\n{'='*60}")
    print(f"EXAMPLE: {description}")
    print(f"{'='*60}")
    print(f"Command: {' '.join(cmd)}")
    print("-" * 60)
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.stdout:
            print("Output:")
            print(result.stdout)
        
        if result.stderr:
            print("Errors:")
            print(result.stderr)
        
        print(f"Return code: {result.returncode}")
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("Command timed out after 5 minutes")
        return False
    except Exception as e:
        print(f"Error running command: {e}")
        return False

def main():
    """Demonstrate various usage scenarios."""
    script_path = "batch_git_commit.py"
    
    # Check if script exists
    if not Path(script_path).exists():
        print(f"Error: {script_path} not found in current directory")
        return False
    
    print("Batch Git Commit - Usage Examples")
    print("This script demonstrates various ways to use batch_git_commit.py")
    print("Note: All examples use --dry-run to avoid making actual changes")
    
    examples = [
        {
            "cmd": ["python", script_path, "--dry-run"],
            "desc": "Basic usage with default settings (1GB batches, 100MB LFS threshold)"
        },
        {
            "cmd": ["python", script_path, "--dry-run", "--batch-size", "0.5"],
            "desc": "Smaller batches (500MB) for systems with limited memory"
        },
        {
            "cmd": ["python", script_path, "--dry-run", "--max-file-size", "50"],
            "desc": "Lower LFS threshold (50MB) for projects with many medium-sized assets"
        },
        {
            "cmd": ["python", script_path, "--dry-run", "--batch-size", "2.0", "--max-file-size", "200"],
            "desc": "Large batches (2GB) with high LFS threshold (200MB) for powerful systems"
        },
        {
            "cmd": ["python", script_path, "--dry-run", "--verbose"],
            "desc": "Verbose output for debugging and detailed progress information"
        },
        {
            "cmd": ["python", script_path, "--help"],
            "desc": "Show all available command-line options"
        }
    ]
    
    success_count = 0
    
    for i, example in enumerate(examples, 1):
        success = run_command(example["cmd"], f"{i}. {example['desc']}")
        if success:
            success_count += 1
    
    print(f"\n{'='*60}")
    print("SUMMARY")
    print(f"{'='*60}")
    print(f"Examples run: {len(examples)}")
    print(f"Successful: {success_count}")
    print(f"Failed: {len(examples) - success_count}")
    
    if success_count == len(examples):
        print("\nAll examples completed successfully!")
        print("\nNext steps:")
        print("1. Review the dry-run output above")
        print("2. Remove --dry-run from your chosen command to actually commit files")
        print("3. Consider setting up Git LFS for large files as recommended")
        print("4. Run the script on your actual project directory")
    else:
        print(f"\n{len(examples) - success_count} examples failed.")
        print("Please check the error messages above and ensure:")
        print("- Python 3.6+ is installed")
        print("- Git is installed and accessible")
        print("- You have write permissions in the current directory")
    
    return success_count == len(examples)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
