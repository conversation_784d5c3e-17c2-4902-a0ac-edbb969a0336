<?xml version="1.0" ?>
<root>
	<AnimsList>
		<Element index="1" type="string" value="Actor/Friendly/RedWizard/RedWizard_BubbleDreamer/Animation/1_1_seq02_BBD_Reacfly.anm"/>
		<Element index="2" type="string" value="Actor/Friendly/RedWizard/RedWizard_BubbleDreamer/Animation/1_1_seq02_BBD_Reacfly_Beard_Scale.anm"/>
		<Element index="3" type="string" value="Actor/Friendly/RedWizard/RedWizard_BubbleDreamer/Animation/1_1_seq03_BBD_Sick.anm"/>
		<Element index="4" type="string" value="Actor/Friendly/RedWizard/RedWizard_BubbleDreamer/Animation/Fly.anm"/>
		<Element index="5" type="string" value="Actor/Friendly/RedWizard/RedWizard_BubbleDreamer/Animation/Stand.anm"/>
	</AnimsList>
	<PatchBankList>
		<wang type="string" value="Actor/Friendly/RedWizard/RedWizard_BubbleDreamer/Animation/RedWizard_BubbleDreamer01.tga"/>
		<wang_2 type="string" value="Actor/Friendly/RedWizard/RedWizard_BubbleDreamer/Animation/RedWizard_BubbleDreamer02.tga"/>
		<wang_3 type="string" value="Actor/Friendly/RedWizard/RedWizard_BubbleDreamer/Animation/RedWizard_BubbleDreamer03.tga"/>
	</PatchBankList>
	<Scale type="number" value="1"/>
	<SceneVersion type="number" value="17"/>
	<Squeleton type="string" value="Actor/Friendly/RedWizard/RedWizard_BubbleDreamer/Animation/RedWizard_BubbleDreamer.skl"/>
	<UseDataFolder type="boolean" value="true"/>
	<UseRelative type="boolean" value="true"/>
</root>
