includeReference("Actor/Includes/helpers.ilu")
includeReference("Actor/Includes/gameplay_types.ilu")

PhantomComponent_Template =
{NAME = "PhantomComponent_Template",
PhantomComponent_Template =
{
    collisionGroup = CollisionGroup.Character,
    -- drawDebug = 1,
    shape = 
        {NAME = "PhysShapePolygon",
        PhysShapePolygon =
        {
            Points =
            {
                { VAL = vector2dNew(-3.0, -1.0) },
                { VAL = vector2dNew(-3.0,  5.0) },
                { VAL = vector2dNew( 3.0,  5.0) },
                { VAL = vector2dNew( 3.0, -1.0) },
            },
        }},
}}
appendTable(params.Actor_Template.COMPONENTS,{PhantomComponent_Template})
PhantomComponent_Template = {}

component = {
NAME="Ray_ShooterSkarabAIComponent_Template",
Ray_ShooterSkarabAIComponent_Template =
{
    idleBehavior =
        {NAME="AIRoamingBehavior_Template",
        AIRoamingBehavior_Template =
        {
                idle = 
                    {
                        NAME="AIIdleAction_Template",
                        AIIdleAction_Template=
                        {
                            minTime =  1.5,
                            maxTime = 2.5,
                        }
                    },
                walk = 
                    {
                        NAME="AIWalkInDirAction_Template",
                        AIWalkInDirAction_Template=
                        {
                            minTime = 4,
                            maxTime = 6,
                            walkForce = 4.0,
                            walkAnimRate = 3.0,
                        }
                    },
            }},
    
    deathBehavior =
        {NAME="Ray_AIDeathBehavior_Template",
        Ray_AIDeathBehavior_Template =
        {
            pauseActorWhenDone = 0,
            resetActorWhenDone = 0,
            
            actions =
            {
                {NAME="AIPlayAnimAction_Template",
                AIPlayAnimAction_Template =
                {
                    action = "DEATH", 
                }},
                {NAME="AIFadeAction_Template",
                AIFadeAction_Template =
                {
                    visible = 1,
                    fadeDuration = 0,
                }},
            },
        }},
    
    faction = Faction.Enemy,
    damageLevels =
    {
        { VAL = 25 },
        { VAL = 50 },
        { VAL = 100 },
        { VAL = 100 },
    },
}}

appendTable(params.Actor_Template.COMPONENTS,{component})
component = {}
