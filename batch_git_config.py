"""
Configuration file for batch_git_commit.py

Modify these settings to customize the behavior for your specific project.
"""

# Batch processing settings
DEFAULT_BATCH_SIZE_GB = 1.0  # Size of each commit batch in GB
DEFAULT_MAX_FILE_SIZE_MB = 100  # Files larger than this will be flagged for LFS

# Additional .gitignore patterns specific to your project
# These will be added to the standard game development patterns
CUSTOM_GITIGNORE_PATTERNS = [
    # Add your project-specific patterns here
    # Examples:
    # "MyProject/SpecificFolder/",
    # "*.myextension",
    # "config/local_settings.ini",
]

# File extensions that should definitely use Git LFS
# These will be recommended for LFS regardless of size
LFS_EXTENSIONS = [
    '.fbx', '.max', '.mb', '.ma',  # 3D models
    '.psd', '.tga', '.exr', '.hdr', '.dds',  # Large textures
    '.wav', '.aiff', '.flac',  # Uncompressed audio
    '.mp4', '.avi', '.mov', '.mkv',  # Video files
    '.zip', '.rar', '.7z',  # Archives
    '.iso', '.dmg',  # Disk images
]

# Directories to always exclude (in addition to .gitignore)
EXCLUDE_DIRECTORIES = [
    '.git',
    '.svn',
    '.hg',
    '__pycache__',
    'node_modules',
    '.vs',
    '.vscode',
]

# File patterns to always exclude (high priority)
EXCLUDE_PATTERNS = [
    '*.tmp',
    '*.temp',
    '*.log',
    '*.cache',
    '*.bak',
    '*~',
    '.DS_Store',
    'Thumbs.db',
]

# Commit message templates
COMMIT_MESSAGE_TEMPLATE = "Batch commit {batch_num}/{total_batches}: {file_count} files ({size_mb:.1f} MB)"

# Custom commit messages for specific file types
COMMIT_MESSAGE_BY_TYPE = {
    'source': "Batch commit {batch_num}/{total_batches}: Source code ({file_count} files, {size_mb:.1f} MB)",
    'assets': "Batch commit {batch_num}/{total_batches}: Game assets ({file_count} files, {size_mb:.1f} MB)",
    'docs': "Batch commit {batch_num}/{total_batches}: Documentation ({file_count} files, {size_mb:.1f} MB)",
}

# File type classification
FILE_TYPE_PATTERNS = {
    'source': ['.cpp', '.c', '.h', '.hpp', '.cs', '.py', '.js', '.ts', '.java', '.go', '.rs'],
    'assets': ['.fbx', '.obj', '.dae', '.3ds', '.blend', '.max', '.ma', '.mb', 
               '.png', '.jpg', '.jpeg', '.tga', '.bmp', '.psd', '.exr', '.hdr',
               '.wav', '.mp3', '.ogg', '.flac', '.aiff', '.wem'],
    'docs': ['.md', '.txt', '.doc', '.docx', '.pdf', '.rtf', '.html', '.xml'],
}

# Performance settings
MAX_FILES_PER_BATCH = 10000  # Maximum number of files in a single batch
SCAN_CHUNK_SIZE = 1000  # Number of files to process at once during scanning
PROGRESS_UPDATE_INTERVAL = 100  # Update progress every N files

# Logging settings
LOG_LEVEL = 'INFO'  # DEBUG, INFO, WARNING, ERROR
LOG_FILE = 'batch_git_commit.log'
LOG_FORMAT = '%(asctime)s - %(levelname)s - %(message)s'

# Git LFS settings
LFS_TRACK_BY_SIZE = True  # Automatically recommend LFS for large files
LFS_SIZE_THRESHOLD_MB = 100  # Recommend LFS for files larger than this
LFS_TRACK_BY_EXTENSION = True  # Recommend LFS for specific extensions

# Safety settings
REQUIRE_CONFIRMATION = False  # Require user confirmation before each batch
DRY_RUN_BY_DEFAULT = False  # Start in dry-run mode by default
BACKUP_GITIGNORE = True  # Backup existing .gitignore before modifying

# Advanced settings
USE_PARALLEL_PROCESSING = False  # Use multiple threads for file scanning (experimental)
PARALLEL_WORKERS = 4  # Number of worker threads
MEMORY_LIMIT_GB = 4  # Approximate memory limit for file list
TEMP_DIR = None  # Custom temporary directory (None = system default)

# Integration settings
PERFORCE_INTEGRATION = True  # Respect Perforce ignore files
PERFORCE_IGNORE_FILE = '.p4ignore.txt'
UNITY_INTEGRATION = True  # Special handling for Unity projects
UNREAL_INTEGRATION = True  # Special handling for Unreal Engine projects

# Validation settings
VALIDATE_GIT_INSTALL = True  # Check if Git is properly installed
VALIDATE_DISK_SPACE = True  # Check available disk space before processing
MIN_FREE_SPACE_GB = 1.0  # Minimum free space required
VALIDATE_FILE_PERMISSIONS = True  # Check file permissions before processing
