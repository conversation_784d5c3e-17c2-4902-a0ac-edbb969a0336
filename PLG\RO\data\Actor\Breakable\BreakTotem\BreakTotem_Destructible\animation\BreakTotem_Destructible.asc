<?xml version="1.0" ?>
<root>
	<AnimsList>
		<Element index="1" type="string" value="actor/breakable/breaktotem/breaktotem_destructible/animation/Destruction.anm"/>
	</AnimsList>
	<PatchBankList>
		<BreakTotem_Temp type="string" value="Actor/Breakable/BreakTotem/BreakTotem_Destructible/animation/BreakTotem_Temp.tga"/>
		<Graphic_BasicAnimation type="string" value="actor/breakable/breaktotem/breaktotem_destructible/animation/BreakTotem_Destructible.tga"/>
	</PatchBankList>
	<Scale type="number" value="1"/>
	<SceneVersion type="number" value="19"/>
	<Squeleton type="string" value="actor/breakable/breaktotem/breaktotem_destructible/animation/BreakTotem_Destructible.skl"/>
	<UseDataFolder type="boolean" value="true"/>
	<UseRelative type="boolean" value="true"/>
</root>
