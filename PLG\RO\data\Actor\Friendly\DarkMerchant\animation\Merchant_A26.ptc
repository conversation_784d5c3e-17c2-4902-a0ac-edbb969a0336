<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="0.11049752688139"/>
			<AngleLocal type="number" value="0.11049752688139"/>
			<Lenght type="number" value="0.67147558927536"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_01_L"/>
			<Pos type="vector" x="0.01590034738183" y="0.89097613096237" z="0"/>
			<PosEnd type="vector" x="0.68737471103668" y="0.88968116044998" z="0"/>
			<PosLocal type="vector" x="0.01590034738183" y="0.89097613096237" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="D2230D776D736D647MyApp578"/>
				<Element index="2" type="string" value="D2230D776D736D647MyApp579"/>
				<Element index="3" type="string" value="D2230D776D736D647MyApp580"/>
				<Element index="4" type="string" value="D2230D776D736D647MyApp581"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A26"/>
			<UID type="string" value="D2230D776D736D647MyApp576"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A26.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Cloak06_L"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2230D776D736D647MyApp579"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2230D776D736D647MyApp576"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-0.024087961763144" y="0.0065646492876112" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.0019285444868729" y="-0.99999815225601" z="0"/>
			<PosUV type="vector" x="-0.00028676149668172" y="0.88444268703461" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A26"/>
			<UID type="string" value="D2230D776D736D647MyApp578"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2230D776D736D647MyApp578"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2230D776D736D647MyApp576"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.09983341395855" y="-0.99500417709351" z="0"/>
					<Pos type="vector" x="-0.02952478453517" y="-0.1118797659874" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.10175213962793" y="0.99480980634689" z="0"/>
			<PosUV type="vector" x="-0.0037090219557285" y="1.0028939247131" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A26"/>
			<UID type="string" value="D2230D776D736D647MyApp579"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2230D776D736D647MyApp581"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2230D776D736D647MyApp576"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.0052631115540862" y="0.99998617172241" z="0"/>
					<Pos type="vector" x="1.0210410356522" y="0.011626317165792" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.003334583947435" y="-0.99999445676804" z="0"/>
			<PosUV type="vector" x="0.70148074626923" y="0.87802761793137" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A26"/>
			<UID type="string" value="D2230D776D736D647MyApp580"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D2230D776D736D647MyApp580"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D2230D776D736D647MyApp576"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.09983341395855" y="-0.99500417709351" z="0"/>
					<Pos type="vector" x="1.0135165452957" y="-0.018977835774422" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.10175213962793" y="0.99480980634689" z="0"/>
			<PosUV type="vector" x="0.69648730754852" y="0.90864145755768" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A26"/>
			<UID type="string" value="D2230D776D736D647MyApp581"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A26"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="93.************" y="-656.81091308594" z="0"/>
	<zoomSize type="vector" x="1406.6276855469" y="1406.6282958984" z="1.3603748083115"/>
</root>
