

DESCRIPTOR = 
{
	{
		        SoundDescriptor_Template=
		        {
			        name="MRK_Squashed_Idle",
			        WwiseEventGuid="6BF03028-0D2B-4DC9-9B30-01E3B5C70E4F",
                    volume=-25,
			        category="NPC_CAT_01",
					--limitCategory="Fakir_Squash_Idle",
					limitMode=LimiterMode.RejectNew,
					maxInstances=3,
                    params = 
					{SoundParams={
						numChannels=1,
						loop=1, --if loop = 1 special behaviour for random and sequence
						playMode=PlayMode.Random, --0 for first,1 for random, 2 for random remember last, 3 for random sequence, 4 for sequence
						randomVolMin=0.0,
						randomVolMax=0.0,
						randomPitchMin=0.95,
						randomPitchMax=1.05,
						fadeInTime=0.1,
						fadeOutTime=0.1,
						
						modifiers=
							{
								{
									NAME="SpatializedPanning",
									SpatializedPanning=
									{
										widthMin = 0.5,
										widthMax = 2.0,
									}
								},
								{
									NAME="ScreenRollOff",
									ScreenRollOff=
									{
										distanceMin = 0.2,
										distanceMax = 2.0,
									}
								},
								{
									NAME="ZRollOff",
									ZRollOff=
									{
										distanceMin = 10.0,
										distanceMax = 20.0,
									}
								},
							}
					}},					
                    files=
                    {
				        { VAL = "Sound/200_Characters/210_Common/Fakir/Sfx_Fakir_Beard_Iddle_Squash_LP.wav", },
                    }
                }
	},			
}

appendTable(component.SoundComponent_Template.soundList,DESCRIPTOR)
