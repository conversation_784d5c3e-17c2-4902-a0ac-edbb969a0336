<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="90.************"/>
			<AngleLocal type="number" value="90.************"/>
			<Lenght type="number" value="0.097434513270855"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Glasses"/>
			<Pos type="vector" x="0.91056501865387" y="0.49986487627029" z="0"/>
			<PosEnd type="vector" x="0.90947437286377" y="0.40243646502495" z="0"/>
			<PosLocal type="vector" x="0.91056501865387" y="0.49986487627029" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp2454"/>
				<Element index="2" type="string" value="MyApp2455"/>
				<Element index="3" type="string" value="MyApp2456"/>
				<Element index="4" type="string" value="MyApp2457"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A32"/>
			<UID type="string" value="MyApp2453"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A32.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Fx_HatSpin_01"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2455"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2453"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.50235116481781" y="0.86466372013092" z="0"/>
					<Pos type="vector" x="-0.14246113598347" y="0.088697671890259" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.87023270130157" y="-0.49264097213745" z="0"/>
			<PosUV type="vector" x="0.8220282793045" y="0.51473748683929" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A32"/>
			<UID type="string" value="MyApp2454"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2454"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2453"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.64421778917313" y="-0.76484227180481" z="0"/>
					<Pos type="vector" x="8.1360820331611e-005" y="-0.10209649056196" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.75758320093155" y="-0.65273880958557" z="0"/>
			<PosUV type="vector" x="1.0126550197601" y="0.49871411919594" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A32"/>
			<UID type="string" value="MyApp2455"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2457"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2453"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.50689536333084" y="0.86200761795044" z="0"/>
					<Pos type="vector" x="1.0764573812485" y="0.012582791037858" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.86762762069702" y="-0.************" z="0"/>
			<PosUV type="vector" x="0.89680898189545" y="0.39512819051743" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A32"/>
			<UID type="string" value="MyApp2456"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp2456"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp2453"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0.64421778917313" y="-0.76484227180481" z="0"/>
					<Pos type="vector" x="1.0925478935242" y="-0.019355045631528" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.75758320093155" y="-0.65273880958557" z="0"/>
			<PosUV type="vector" x="0.92872726917267" y="0.39320302009583" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A32"/>
			<UID type="string" value="MyApp2457"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="false"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A32"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="-1065.9592285156" y="-655.49426269531" z="0"/>
	<zoomSize type="vector" x="2482.5395507813" y="2482.5400390625" z="2.3916573524475"/>
</root>
