params =
{
    
NAME = "Actor_Template",
Actor_Template =
    {
        SCALE = vector2dNew(7.000000,7.000000),
        RANK = 0.000000,
        COMPONENTS =
        {
            {
                NAME = "GenericAIComponent_Template",
                GenericAIComponent_Template =
                {
                    registerToAIManager = 1,
                    damageLevels =
                    {
                        {
                            VAL = 5,
                        },
                        {
                            VAL = 25,
                        },
                        {
                            VAL = 50,
                        },
                        {
                            VAL = 100,
                        },
                    },
                    genericBehavior =
                    {
                        NAME = "Ray_AIMerchantOpenBehavior_Template",
                        Ray_AIMerchantOpenBehavior_Template =
                        {
                            closed =
                            {
                                NAME = "AIIdleAction_Template",
                                AIIdleAction_Template =
                                {
                                    action = "CLOSED",
                                },
                            },
                            open =
                            {
                                NAME = "AIPlayAnimAction_Template",
                                AIPlayAnimAction_Template =
                                {
                                    action = "OPEN",
                                    endMarker = "Opened",
                                },
                            },
                            shop =
                            {
                                NAME = "Ray_AIShopAction_Template",
                                Ray_AIShopAction_Template =
                                {
                                },
                            },
                            itemActivationBoxWidth = 0.750000,
                            itemActivationBoxHeight = 3.000000,
                            slots =
                            {
                                {
                                    ItemSlot =
                                    {
                                        cost = 300,
                                        bone = "B_Mrc_Bonus_01",
                                        item = "Actor/Items/Bottle/Bottle.act",
                                    },
                                },
                                {
                                    ItemSlot =
                                    {
                                        cost = 400,
                                        bone = "B_Mrc_Bonus_02",
                                        item = "Actor/Items/Pump/Pump.act",
                                    },
                                },
                                {
                                    ItemSlot =
                                    {
                                        cost = 500,
                                        bone = "B_Mrc_Bonus_03",
                                        item = "Actor/Items/Warp/Warp.act",
                                    },
                                },
                                {
                                    ItemSlot =
                                    {
                                        bone = "B_Mrc_Bonus_04",
                                        item = "Actor/Items/Turnip/Turnip.act",
                                    },
                                },
                            },
                        },
                    },
                },
            },
            {
                NAME = "AnimatedComponent_Template",
                AnimatedComponent_Template =
                {
                    visualAABB =
                    {
                        AABB =
                        {
                        },
                    },
                    animationPath = "Actor/Friendly/DarkMerchant/Animation/",
                    animSet =
                    {
                        SubAnimSet_Template =
                        {
                            animations =
                            {
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "Closed_to_Open",
                                        name = "Mrc_Closed_to_Open.anm",
                                        markerStart = "MRK_Start",
                                        markerStop = "MRK_Stop",
                                    },
                                },
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "Open_to_Closed",
                                        name = "Mrc_Closed_to_Open.anm",
                                        reverse = 1,
                                        markerStart = "MRK_Stop",
                                        markerStop = "MRK_Start",
                                    },
                                },
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "OPEN",
                                        name = "Mrc_Open_Stand.anm",
                                        loop = 1,
                                        markerStart = "MRK_Start",
                                        markerStop = "MRK_Stop",
                                    },
                                },
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "Open_to_Away",
                                        name = "Mrc_Open_to_Away.anm",
                                        markerStart = "MRK_Start",
                                        markerStop = "MRK_Stop",
                                    },
                                },
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "Away",
                                        name = "Mrc_Away_Stand.anm",
                                        loop = 1,
                                        markerStart = "MRK_Start",
                                        markerStop = "MRK_Stop",
                                    },
                                },
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "Open_to_Warp",
                                        name = "Mrc_Open_Warp.anm",
                                        markerStart = "MRK_Stand_to_Warp_Start",
                                        markerStop = "MRK_Stand_to_Warp_Stop",
                                    },
                                },
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "Warp",
                                        name = "Mrc_Open_Warp.anm",
                                        loop = 1,
                                        markerStart = "MRK_Warp_Start",
                                        markerStop = "MRK_Warp_Stop",
                                    },
                                },
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "CLOSED",
                                        name = "Mrc_Closed_Stand.anm",
                                        loop = 1,
                                        markerStart = "MRK_Start",
                                        markerStop = "MRK_Stop",
                                    },
                                },
                            },
                        },
                    },
                    defaultAnimation = "CLOSED",
                    tree =
                    {
                        AnimTree_Template =
                        {
                            nodes =
                            {
                                {
                                    NAME = "AnimTreeNodePlayAnim_Template",
                                    AnimTreeNodePlayAnim_Template =
                                    {
                                        nodeName = "OPEN",
                                        animationName = "Open",
                                        proceduralInput =
                                        {
                                            ProceduralInputData =
                                            {
                                            },
                                        },
                                        proceduralPlayRate =
                                        {
                                            ProceduralInputData =
                                            {
                                            },
                                        },
                                    },
                                },
                                {
                                    NAME = "AnimTreeNodePlayAnim_Template",
                                    AnimTreeNodePlayAnim_Template =
                                    {
                                        nodeName = "CLOSED",
                                        animationName = "Closed",
                                        proceduralInput =
                                        {
                                            ProceduralInputData =
                                            {
                                            },
                                        },
                                        proceduralPlayRate =
                                        {
                                            ProceduralInputData =
                                            {
                                            },
                                        },
                                    },
                                },
                                {
                                    NAME = "AnimTreeNodePlayAnim_Template",
                                    AnimTreeNodePlayAnim_Template =
                                    {
                                        nodeName = "Away",
                                        animationName = "Away",
                                        proceduralInput =
                                        {
                                            ProceduralInputData =
                                            {
                                            },
                                        },
                                        proceduralPlayRate =
                                        {
                                            ProceduralInputData =
                                            {
                                            },
                                        },
                                    },
                                },
                                {
                                    NAME = "AnimTreeNodePlayAnim_Template",
                                    AnimTreeNodePlayAnim_Template =
                                    {
                                        nodeName = "Warp",
                                        animationName = "Warp",
                                        proceduralInput =
                                        {
                                            ProceduralInputData =
                                            {
                                            },
                                        },
                                        proceduralPlayRate =
                                        {
                                            ProceduralInputData =
                                            {
                                            },
                                        },
                                    },
                                },
                            },
                            nodeTransitions =
                            {
                                {
                                    BlendTreeTransition_Template =
                                    {
                                        from =
                                        {
                                            {
                                                VAL = "OPEN",
                                            },
                                        },
                                        to =
                                        {
                                            {
                                                VAL = "CLOSED",
                                            },
                                        },
                                        node =
                                        {
                                            NAME = "AnimTreeNodePlayAnim_Template",
                                            AnimTreeNodePlayAnim_Template =
                                            {
                                                animationName = "Open_to_Closed",
                                                proceduralInput =
                                                {
                                                    ProceduralInputData =
                                                    {
                                                    },
                                                },
                                                proceduralPlayRate =
                                                {
                                                    ProceduralInputData =
                                                    {
                                                    },
                                                },
                                            },
                                        },
                                    },
                                },
                                {
                                    BlendTreeTransition_Template =
                                    {
                                        from =
                                        {
                                            {
                                                VAL = "CLOSED",
                                            },
                                        },
                                        to =
                                        {
                                            {
                                                VAL = "OPEN",
                                            },
                                        },
                                        node =
                                        {
                                            NAME = "AnimTreeNodePlayAnim_Template",
                                            AnimTreeNodePlayAnim_Template =
                                            {
                                                animationName = "Closed_to_Open",
                                                proceduralInput =
                                                {
                                                    ProceduralInputData =
                                                    {
                                                    },
                                                },
                                                proceduralPlayRate =
                                                {
                                                    ProceduralInputData =
                                                    {
                                                    },
                                                },
                                            },
                                        },
                                    },
                                },
                                {
                                    BlendTreeTransition_Template =
                                    {
                                        from =
                                        {
                                            {
                                                VAL = "OPEN",
                                            },
                                        },
                                        to =
                                        {
                                            {
                                                VAL = "Away",
                                            },
                                        },
                                        node =
                                        {
                                            NAME = "AnimTreeNodePlayAnim_Template",
                                            AnimTreeNodePlayAnim_Template =
                                            {
                                                animationName = "Open_to_Away",
                                                proceduralInput =
                                                {
                                                    ProceduralInputData =
                                                    {
                                                    },
                                                },
                                                proceduralPlayRate =
                                                {
                                                    ProceduralInputData =
                                                    {
                                                    },
                                                },
                                            },
                                        },
                                    },
                                },
                                {
                                    BlendTreeTransition_Template =
                                    {
                                        from =
                                        {
                                            {
                                                VAL = "OPEN",
                                            },
                                        },
                                        to =
                                        {
                                            {
                                                VAL = "Warp",
                                            },
                                        },
                                        node =
                                        {
                                            NAME = "AnimTreeNodePlayAnim_Template",
                                            AnimTreeNodePlayAnim_Template =
                                            {
                                                animationName = "Open_to_Warp",
                                                proceduralInput =
                                                {
                                                    ProceduralInputData =
                                                    {
                                                    },
                                                },
                                                proceduralPlayRate =
                                                {
                                                    ProceduralInputData =
                                                    {
                                                    },
                                                },
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            },
        },
    },
    ActorEditorParams =
    {
    },
}
