<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="90.************"/>
			<AngleLocal type="number" value="90.************"/>
			<Lenght type="number" value="0.025459578260779"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Hair_04_R"/>
			<Pos type="vector" x="0.71356099843979" y="0.38308987021446" z="0"/>
			<PosEnd type="vector" x="0.71334880590439" y="0.35763117671013" z="0"/>
			<PosLocal type="vector" x="0.71356099843979" y="0.38308987021446" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="D4856D4854D4852MyApp4841"/>
				<Element index="2" type="string" value="D4856D4854D4852MyApp4842"/>
				<Element index="3" type="string" value="D4856D4854D4852MyApp4843"/>
				<Element index="4" type="string" value="D4856D4854D4852MyApp4844"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A44"/>
			<UID type="string" value="D4856D4854D4852MyApp4840"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A44.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Fx_Sparkle_04"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D4856D4854D4852MyApp4842"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D4856D4854D4852MyApp4840"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-1.9250262975693" y="0.018601888790727" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.99996519088745" y="0.0083344867452979" z="0"/>
			<PosUV type="vector" x="0.69536823034286" y="0.43225356936455" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A44"/>
			<UID type="string" value="D4856D4854D4852MyApp4841"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D4856D4854D4852MyApp4841"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D4856D4854D4852MyApp4840"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="-1.9639251232147" y="-0.017149651423097" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99996519088745" y="-0.0083344867452979" z="0"/>
			<PosUV type="vector" x="0.73112678527832" y="0.43294590711594" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A44"/>
			<UID type="string" value="D4856D4854D4852MyApp4842"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D4856D4854D4852MyApp4844"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D4856D4854D4852MyApp4840"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="2.148638010025" y="0.017769765108824" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.99996519088745" y="0.0083344867452979" z="0"/>
			<PosUV type="vector" x="0.6953359246254" y="0.32853645086288" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A44"/>
			<UID type="string" value="D4856D4854D4852MyApp4843"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D4856D4854D4852MyApp4843"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D4856D4854D4852MyApp4840"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="2.1597356796265" y="-0.017025325447321" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.99996519088745" y="-0.0083344867452979" z="0"/>
			<PosUV type="vector" x="0.73012745380402" y="0.32796391844749" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A44"/>
			<UID type="string" value="D4856D4854D4852MyApp4844"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A44"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
</root>
