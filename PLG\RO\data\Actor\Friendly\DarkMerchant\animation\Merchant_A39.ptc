<?xml version="1.0" ?>
<root>
	<BackGroundMedia type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/Merchant_A.tga"/>
	<BonesListT>
		<Element index="1">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="-0.070048300895914"/>
			<AngleLocal type="number" value="-0.070048300895914"/>
			<Lenght type="number" value="0.043585572391748"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_01_R"/>
			<Pos type="vector" x="0.22258946299553" y="0.72283762693405" z="0"/>
			<PosEnd type="vector" x="0.26617500185966" y="0.72289091348648" z="0"/>
			<PosLocal type="vector" x="0.22258946299553" y="0.72283762693405" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="D3086MyApp2389"/>
				<Element index="2" type="string" value="D3086MyApp2390"/>
				<Element index="3" type="string" value="D3086MyApp2391"/>
				<Element index="4" type="string" value="D3086MyApp2392"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A39"/>
			<UID type="string" value="D3086D2350MyApp2319"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
		<Element index="2">
			<Alpha type="number" value="1"/>
			<Angle type="number" value="0.30871578083325"/>
			<AngleLocal type="number" value="0.37876408172916"/>
			<Lenght type="number" value="0.25378057360649"/>
			<Mirror type="boolean" value="false"/>
			<Name type="string" value="B_Mrc_Cloak_02_R"/>
			<ParentUID type="string" value="D3086D2350MyApp2319"/>
			<Pos type="vector" x="0.26617500185966" y="0.72289091348648" z="0"/>
			<PosEnd type="vector" x="0.51995187997818" y="0.72152352333069" z="0"/>
			<PosLocal type="vector" x="0" y="0" z="0"/>
			<Refs>
			</Refs>
			<RefsUID>
				<Element index="1" type="string" value="MyApp3816"/>
				<Element index="2" type="string" value="MyApp3817"/>
				<Element index="3" type="string" value="MyApp3818"/>
				<Element index="4" type="string" value="MyApp3819"/>
			</RefsUID>
			<ScaleX type="number" value="1"/>
			<ScaleY type="number" value="1"/>
			<TemplateUID type="string" value="Merchant_A39"/>
			<UID type="string" value="MyApp3815"/>
			<Zorder type="number" value="0"/>
			<noBegin type="boolean" value="true"/>
			<noEnd type="boolean" value="true"/>
		</Element>
	</BonesListT>
	<EventsList>
	</EventsList>
	<FileDir type="string" value="../../_RAYMAN_ORIGINS_/Actor/Friendly/DarkMerchant/animation/"/>
	<FileName type="string" value="Merchant_A39.ptc"/>
	<MediaWidth type="number" value="1"/>
	<Name type="string" value="P_Mrc_Arm07_R"/>
	<PatchPointList>
		<Element index="1">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D3086MyApp2390"/>
			<Color>
				<A type="number" value="0"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D3086D2350MyApp2319"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="-0.27953797578812" y="0.022705279290676" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.0012225732207298" y="-0.99999928474426" z="0"/>
			<PosUV type="vector" x="0.21043340861797" y="0.70011746883392" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A39"/>
			<UID type="string" value="D3086MyApp2389"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="2">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D3086MyApp2389"/>
			<Color>
				<A type="number" value="0"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D3086D2350MyApp2319"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="-0.29078629612923" y="-0.019014431163669" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.0012225732207298" y="0.99999928474426" z="0"/>
			<PosUV type="vector" x="0.20989213883877" y="0.74183654785156" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A39"/>
			<UID type="string" value="D3086MyApp2390"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="3">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D3086MyApp2392"/>
			<Color>
				<A type="number" value="0"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D3086D2350MyApp2319"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="0.25226017832756" y="0.022293496876955" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.0012225732207298" y="-0.99999928474426" z="0"/>
			<PosUV type="vector" x="0.23361161351204" y="0.70055758953094" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A39"/>
			<UID type="string" value="D3086MyApp2391"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="4">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="D3086MyApp2391"/>
			<Color>
				<A type="number" value="0"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="D3086D2350MyApp2319"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="0.21752060949802" y="-0.018471943214536" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.0012225732207298" y="0.99999928474426" z="0"/>
			<PosUV type="vector" x="0.23204763233662" y="0.74132114648819" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A39"/>
			<UID type="string" value="D3086MyApp2392"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="5">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp3817"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp3815"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="0.051059022545815" y="0.022489316761494" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.0053880806080997" y="-0.99998545646667" z="0"/>
			<PosUV type="vector" x="0.27901142835617" y="0.70033210515976" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A39"/>
			<UID type="string" value="MyApp3816"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="6">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp3816"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp3815"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="0.050138976424932" y="-0.018737362697721" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.0053880806080997" y="0.99998545646667" z="0"/>
			<PosUV type="vector" x="0.27900007367134" y="0.************" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A39"/>
			<UID type="string" value="MyApp3817"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="7">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp3819"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp3815"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="1" z="0"/>
					<Pos type="vector" x="1.0214456319809" y="0.022396055981517" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="-0.0053880806080997" y="-0.99998545646667" z="0"/>
			<PosUV type="vector" x="0.52527362108231" y="0.69909846782684" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A39"/>
			<UID type="string" value="MyApp3818"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
		<Element index="8">
			<Angle type="number" value="0"/>
			<BrotherUID type="string" value="MyApp3818"/>
			<Color>
				<A type="number" value="255"/>
				<B type="number" value="255"/>
				<G type="number" value="255"/>
				<R type="number" value="255"/>
			</Color>
			<Hiden type="number" value="0"/>
			<LocalData>
				<Element index="1">
					<BoneUID type="string" value="MyApp3815"/>
					<Influence type="number" value="1"/>
					<Normale type="vector" x="0" y="-1" z="0"/>
					<Pos type="vector" x="1.0302767753601" y="-0.020399900153279" z="0"/>
				</Element>
			</LocalData>
			<NormaleUV type="vector" x="0.0053880806080997" y="0.99998545646667" z="0"/>
			<PosUV type="vector" x="0.5277453660965" y="0.7418817281723" z="0"/>
			<SetPicking type="boolean" value="true"/>
			<TemplateUID type="string" value="Merchant_A39"/>
			<UID type="string" value="MyApp3819"/>
			<Zorder type="number" value="0"/>
			<isInversed type="boolean" value="true"/>
		</Element>
	</PatchPointList>
	<Ratio type="number" value="1"/>
	<UID type="string" value="Merchant_A39"/>
	<ZoomOrigin type="vector" x="0" y="0" z="0"/>
	<ZoomSize type="vector" x="1024" y="1024" z="1"/>
	<zoomOrigin type="vector" x="-1577.3486328125" y="-2740.2687988281" z="0"/>
	<zoomSize type="vector" x="4576.53125" y="4576.5317382813" z="4.4260454177856"/>
</root>
