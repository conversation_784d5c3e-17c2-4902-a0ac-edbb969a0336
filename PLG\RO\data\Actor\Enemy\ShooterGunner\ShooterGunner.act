includeReference("Actor/Includes/helpers.ilu")
includeReference("Actor/Includes/gameplay_types.ilu")

params =
{
    
NAME = "Actor_Template",
Actor_Template =
    {
        SCALE = vector2dNew(3.5, 3.5),
        RANK = 0,
        COMPONENTS =
        {
        }
    }
}

_ForceFixAngle = 0
-- _ForceFixAngleVal = 0.0
-- _ForceFixAngleRight = 1

includeReference("Actor/Enemy/ShooterGunner/Components/ShooterGunnerBot_AI.ilu")
includeReference("Actor/Enemy/ShooterGunner/Components/ShooterGunnerBot_Anim.ilu")

--includeReference("Actor/Enemy/Ant_BasicSoldier/Components/Ant_BasicSoldier_sound.ilu")
-- includeReference("Actor/Enemy/Ant_BasicSoldier/Components/Ant_BasicSoldier_fxController.ilu")
-- includeReference("Actor/Enemy/Common/Components/FxBankComponent.ilu")
