#!/usr/bin/env python3
"""
Test script for batch_git_commit.py

This script creates a test environment with various file types and sizes
to verify the batch commit functionality works correctly.
"""

import os
import tempfile
import shutil
import random
import string
from pathlib import Path
import subprocess
import sys

def create_test_file(path: Path, size_mb: float) -> None:
    """Create a test file of specified size."""
    path.parent.mkdir(parents=True, exist_ok=True)
    
    # Create file with random content
    size_bytes = int(size_mb * 1024 * 1024)
    with open(path, 'wb') as f:
        # Write in chunks to avoid memory issues
        chunk_size = min(1024 * 1024, size_bytes)  # 1MB chunks
        remaining = size_bytes
        
        while remaining > 0:
            write_size = min(chunk_size, remaining)
            # Create random binary data
            data = bytes(random.randint(0, 255) for _ in range(write_size))
            f.write(data)
            remaining -= write_size

def create_test_environment(test_dir: Path) -> None:
    """Create a test environment with various file types and sizes."""
    print(f"Creating test environment in {test_dir}")
    
    # Create directory structure
    dirs = [
        "src/engine",
        "src/game", 
        "assets/models",
        "assets/textures",
        "assets/audio",
        "build/temp",
        "docs",
        "tools"
    ]
    
    for dir_path in dirs:
        (test_dir / dir_path).mkdir(parents=True, exist_ok=True)
    
    # Create various file types and sizes
    test_files = [
        # Small source files
        ("src/engine/main.cpp", 0.001),  # 1KB
        ("src/engine/renderer.h", 0.002),  # 2KB
        ("src/game/player.cs", 0.005),  # 5KB
        ("src/game/enemy.py", 0.003),  # 3KB
        
        # Medium files
        ("assets/textures/diffuse.png", 5),  # 5MB
        ("assets/textures/normal.tga", 8),  # 8MB
        ("assets/audio/music.wav", 15),  # 15MB
        ("docs/manual.pdf", 3),  # 3MB
        
        # Large files (for LFS testing)
        ("assets/models/character.fbx", 150),  # 150MB
        ("assets/models/environment.max", 200),  # 200MB
        ("assets/audio/soundtrack.wav", 120),  # 120MB
        
        # Very large file (single batch)
        ("assets/video/intro.mp4", 1200),  # 1.2GB
        
        # Many small files (batch by count)
        *[(f"build/temp/file_{i:04d}.obj", 0.1) for i in range(50)],  # 50 x 100KB files
        
        # Build artifacts (should be ignored)
        ("build/temp/output.exe", 10),
        ("build/temp/debug.pdb", 5),
        (".vs/config.user", 0.001),
    ]
    
    print(f"Creating {len(test_files)} test files...")
    for file_path, size_mb in test_files:
        full_path = test_dir / file_path
        create_test_file(full_path, size_mb)
        print(f"  Created {file_path} ({size_mb} MB)")
    
    # Create .gitignore with some patterns
    gitignore_content = """
# Build outputs
*.exe
*.pdb
*.obj
build/temp/
.vs/

# Temporary files
*.tmp
*.log
"""
    
    with open(test_dir / ".gitignore", "w") as f:
        f.write(gitignore_content)
    
    print("Test environment created successfully!")

def run_batch_commit_test(test_dir: Path, dry_run: bool = True) -> bool:
    """Run the batch commit script in the test environment."""
    print(f"\nRunning batch commit test (dry_run={dry_run})...")
    
    # Change to test directory
    original_cwd = os.getcwd()
    os.chdir(test_dir)
    
    try:
        # Run the batch commit script
        script_path = Path(original_cwd) / "batch_git_commit.py"
        cmd = [sys.executable, str(script_path), "--batch-size", "0.5", "--max-file-size", "50"]
        
        if dry_run:
            cmd.append("--dry-run")
        
        cmd.append("--verbose")
        
        print(f"Running command: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        print("STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        print(f"Return code: {result.returncode}")
        return result.returncode == 0
        
    finally:
        os.chdir(original_cwd)

def main():
    """Main test function."""
    print("Batch Git Commit Test Suite")
    print("=" * 40)
    
    # Create temporary test directory
    with tempfile.TemporaryDirectory(prefix="batch_git_test_") as temp_dir:
        test_dir = Path(temp_dir)
        
        try:
            # Create test environment
            create_test_environment(test_dir)
            
            # Test 1: Dry run
            print("\n" + "=" * 40)
            print("TEST 1: Dry Run")
            print("=" * 40)
            success1 = run_batch_commit_test(test_dir, dry_run=True)
            
            # Test 2: Actual run
            print("\n" + "=" * 40)
            print("TEST 2: Actual Commit")
            print("=" * 40)
            success2 = run_batch_commit_test(test_dir, dry_run=False)
            
            # Test 3: Second run (should find no new files)
            print("\n" + "=" * 40)
            print("TEST 3: Second Run (No New Files)")
            print("=" * 40)
            success3 = run_batch_commit_test(test_dir, dry_run=False)
            
            # Summary
            print("\n" + "=" * 40)
            print("TEST SUMMARY")
            print("=" * 40)
            print(f"Test 1 (Dry Run): {'PASS' if success1 else 'FAIL'}")
            print(f"Test 2 (Actual Commit): {'PASS' if success2 else 'FAIL'}")
            print(f"Test 3 (No New Files): {'PASS' if success3 else 'FAIL'}")
            
            overall_success = success1 and success2 and success3
            print(f"Overall: {'PASS' if overall_success else 'FAIL'}")
            
            if overall_success:
                print("\nAll tests passed! The batch commit script is working correctly.")
            else:
                print("\nSome tests failed. Please check the output above for details.")
            
            return overall_success
            
        except Exception as e:
            print(f"Test failed with exception: {e}")
            return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
