includeReference("Actor/Includes/helpers.ilu")

params =
{
    
NAME = "Actor_Template",
Actor_Template =
    {
        SCALE = vector2dNew(2, 2),
        RANK = 5,
        COMPONENTS =
        {
            {
                NAME="AnimLightComponent_Template",
                AnimLightComponent_Template =
                {
                    scale = vector2dNew(2, 2),
                    renderRank = 1,
                    defaultAnimation = "Idle",
                    animSet=
                    {
						SubAnimSet_Template =
                        {
		                    animations=
		                    {
		                        {
			                        NAME="SubAnim_Template",
			                        SubAnim_Template=
			                        {
			                            friendlyName="Idle",
			                            name="Actor/Enemy/ZombiChiken/Animation/ZombiChik_fly.anm",
			                            loop=1,
			                            sync=1,
			                            syncRatio=0.5,
			                        }
			                    },
		                    },
						},
					},
                },
            },
            {
                NAME="Ray_OffScreenBirdComponent_Template",
                Ray_OffScreenBirdComponent_Template =
                {
                    -- hit da player
                    hitDelay = 3,                     -- s    first hit after after Delay seconds, then every Rate seconds
                    hitRate = 2,                      -- s
                    -- spawing
                    spawnDelay = 0.5,                 -- s    first spawn after after Delay seconds, then every Rate seconds
                    spawnRate = 1.5,                  -- s
                    minBirdCount = 1,
                    maxBirdCount = 2,
                    spawnRadius = 1,                  -- m
                    spawnAngle = 120,                 -- deg
                    reverseInitialDirection = 1,      -- bool
                    -- instance
                    initialSpeed = 6,                 -- m/s
                    maxSpeed = 9,                     -- m/s
                    acceleration = 50,                -- m/s
                    speedRandomFactor = 0.3,          -- %    speed will be +/- random%
                    fleeDelay = 0.5,                  -- s    when the player comes back onscreen, wait a bit before fleeing
                    failsafeDelay = 10,               -- s    birds are killed after a certain delay no matter what
                    fadeOutDuration = 1,              -- s    birds are killed fading out
                },
            },
            {
                NAME="RenderSimpleAnimComponent_Template",
                RenderSimpleAnimComponent_Template =
                {
                },
            },
        },
    }
}

includeReference("Actor/Enemy/ZombiChiken/Components/Zombichick_sound.ilu")
includeReference("Actor/Enemy/ZombiChiken/Components/Zombichick_fxController.ilu")
