includeReference("Actor/Includes/helpers.ilu")
includeReference("Actor/Includes/gameplay_types.ilu")
params =
{
    
NAME = "Actor_Template",
Actor_Template =
    {
        scaleForced = vector2dNew(5.0,5.0),
        --RANK = 1,
        COMPONENTS =
        {
			-- {
				-- NAME="FlyingComponent_Template",
				-- FlyingComponent_Template =
				-- {
					-- MoveVecBlendFactor = 0.1,
					-- MoveSpeed = 5.0,
					-- MoveDirMinAngle = 0.0,
					-- MoveDirMaxAngle = 0.5, 
					-- --MoveDirMaxAngle = 1.5,
					-- MoveDirAmplitude = 5.0,
					-- DebugData =
					-- {
						-- FlyingComponentDebugData=
						-- {
							-- Used = 1,
							-- --SnapToWaypointId = "toto",
							-- OnPlayer = 1,
							-- CollideInvForce = 20.0,
						-- }
					-- },
				-- },
			-- },
			-- {
				-- NAME="StickToPolylinePhysComponent_Template",
				-- StickToPolylinePhysComponent_Template =
				-- { 
					-- physGravityMultiplier = 0.0,
					-- physFriction = 3.0,
					-- physAirFriction = 3.0,
					-- --physAirFrictionMultiplier = 1.0,
					-- physRadius = 0.2,
					-- physWeight= 0.0,
					-- --physMinSpeedStickToWall = 0.0,
					-- --physAngularAirMultiplier = 500000.0,
					-- --physForce2Speed = 0.016667,
					-- --physUnstickMaxAngle = 180.0,
					-- --physAngularSpeedMinAngular = 149.541992,
					-- --physTransferSpeedLossMinAngle = 5.729577,
					-- --physTransferSpeedLossMaxAngle = 45.836617,
					-- --physDefaultStickMax = 299.999908,
				-- },
			-- },
			{
                NAME = "AnimLightComponent_Template",
                AnimLightComponent_Template =
                {
					posOffset = vector2dNew(0.0,- 0.55),
                    animSet =
                    {
                        SubAnimSet_Template =
                        {
                            animations =  
                            {
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "Idle",
                                        name = "Actor/Enemy/Fly/Animation/Mch_Stand.anm",
                                        loop = 1,
                                    },
                                },
                                {
                                    SubAnim_Template =
                                    {
                                        friendlyName = "receivehit",
                                        name = "Actor/Enemy/Fly/Animation/Mch_Paf.anm",
                                        loop = 0,
                                    },
                                },
                            },
                        },
                    },
                    defaultAnimation = "Idle",
                },
            },
            {
                NAME = "PhantomDetectorComponent_Template",
                PhantomDetectorComponent_Template =
                {
					offset = vector2dNew(0.08,-0.15),
					Shape =
					{                           
						NAME = "PhysShapeCircle",
						PhysShapeCircle = 
						{ 
							radius = 0.17,
						}
					},
                },
            },
			-- {
				-- NAME="TriggerComponent_Template",
				-- TriggerComponent_Template =
				-- {
					-- TriggerOnce = 0,
                    -- TriggerSelf = 0,
                    -- TriggerChildren = 0,
                    -- TriggerActivator = 0,
                    -- ResetOnCheckpoint = 0,
					-- TriggerPhantomsCollisions = 1,
				-- },
			-- },	
				
				
			-- {
                -- NAME = "PhantomComponent_Template",
                -- PhantomComponent_Template =
                -- {
                    -- collisionGroup = CollisionGroup.Character,
                    -- --drawDebug = 1,
                    -- shape =
                    -- {
                        -- NAME = "PhysShapeCircle",
                        -- PhysShapeCircle = 
                        -- { 
							-- Radius=0.8,                            
						-- }
                    -- },
					-- -- offset = vector2dNew(0.0,0.55),
                -- },
            -- },				
			 
        }
    }
}
