<?xml version="1.0" ?>
<root>
	<AnimsList>
		<Element index="1" type="string" value="Actor/Bonus/Relics/animation/Stand.anm"/>
		<Element index="2" type="string" value="Actor/Bonus/Relics/animation/add_Arrival_Rock_01.anm"/>
		<Element index="3" type="string" value="Actor/Bonus/Relics/animation/add_Arrival_Rock_02.anm"/>
		<Element index="4" type="string" value="Actor/Bonus/Relics/animation/add_Arrival_Rock_03.anm"/>
		<Element index="5" type="string" value="Actor/Bonus/Relics/animation/add_Rock_01.anm"/>
		<Element index="6" type="string" value="Actor/Bonus/Relics/animation/add_Rock_02.anm"/>
		<Element index="7" type="string" value="Actor/Bonus/Relics/animation/add_Rock_03.anm"/>
	</AnimsList>
	<PatchBankList>
		<Graphic_BasicAnimation type="string" value="actor/bonus/relics/animation/Relics_A.tga"/>
	</PatchBankList>
	<Scale type="number" value="1"/>
	<SceneVersion type="number" value="20"/>
	<Squeleton type="string" value="Actor/Bonus/Relics/animation/Relics_Squeleton.skl"/>
	<UseDataFolder type="boolean" value="true"/>
	<UseRelative type="boolean" value="true"/>
</root>
